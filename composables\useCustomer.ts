import { defineStore } from "pinia";
import { watch } from "vue";
import type {
  ICustomer,
  IOrder,
  IRejectedOrder,
  IPendingOrder,
  IAddress,
} from "~/types/customer";
import { Customer } from "~/types/customer";
import { useAuthStore } from "~/stores/auth";
import { v4 as uuidv4 } from "uuid";
import { useFetch } from "~/composables/useFetch";
import { onMounted } from "vue";
import { useProductsStore } from "~/stores/productsStore";

// Interface para o estado da store
interface CustomerState {
  customer: Customer | null;
  isLoading: boolean;
  error: string | null;
  initialized: boolean;
}

// Definição da store usando o padrão de options
export const useCustomerStore = defineStore("currentCustomer", {
  // Estado inicial
  state: (): CustomerState => ({
    customer: null,
    isLoading: false,
    error: null,
    initialized: false,
  }),

  // Getters
  getters: {
    // Verifica se um item está na wishlist
    isInWishlist: (state) => {
      return (figureId: string) =>
        state.customer?.wishlist.includes(figureId) || false;
    },
    defaultAddress: (state) => state.customer?.getDefaultAddress() || null,

    // Retorna os pedidos com as figuras completas
    ordersWithFigures: (state) => {
      return () => {
        const productsStore = useProductsStore();

        // Adicionando dependência ao estado de produtos para garantir reavaliação
        // quando os produtos forem carregados
        // Verificar se os produtos foram inicializados
        const isProductsInitialized = productsStore.isInitialized;

        console.log(
          "[useCustomer][ordersWithFigures] Produtos inicializados:",
          isProductsInitialized
        );

        if (!state.customer?.orders?.length) return [];

        return state.customer.orders.map((order) => {
          // Se o pedido já tem a figura completa, retorna como está
          if (
            order.figure &&
            "id" in order.figure &&
            order.figure.id === order.productId
          ) {
            return order;
          }

          // Busca a figura pelo productId
          const figure = productsStore.getProductById(order.productId);

          if (figure) {
            console.log(
              `[useCustomer][ordersWithFigures] Figura encontrada para pedido ${order.id}: ${figure.title}`
            );
            return {
              ...order,
              figure,
            };
          }

          console.log(
            `[useCustomer][ordersWithFigures] Figura não encontrada para pedido ${order.id} (productId: ${order.productId})`
          );
          // Se não encontrou a figura, retorna o pedido original
          return order;
        });
      };
    },
  },

  // Actions
  actions: {
    // Inicializa o cliente a partir do Firestore
    async initialize() {
      const authStore = useAuthStore();

      // Se já foi inicializado e o cliente já existe, não fazer nada
      if (this.initialized && this.customer) {
        console.log(
          `✅ [customerStore] Cliente já inicializado para ${this.customer.email}`
        );
        return;
      }

      // Marcar como inicializado
      this.initialized = true;

      if (!authStore.user) {
        console.log(
          "⚠️ [customerStore] Nenhum usuário autenticado, cliente não será inicializado"
        );
        return;
      }

      try {
        console.log(
          `🔍 [customerStore] Inicializando cliente para ${authStore.user.email}...`
        );
        this.isLoading = true;

        // Buscar documento do cliente usando a API do Nuxt
        const { data, error } = await useFetch<ICustomer>(
          `/api/customers/${authStore.user.uid}`
        );

        if (error.value) {
          throw new Error(
            error.value?.message || "Erro ao carregar dados do cliente"
          );
        }

        if (data.value) {
          // Cliente existe, carregar dados
          this.customer = new Customer(data.value as ICustomer);
          console.log("cliente: ", this.customer);
        } else {
          // Cliente não existe, criar novo
          const newCustomer: Partial<ICustomer> = {
            uid: authStore.user.uid,
            email: authStore.user.email || "",
            name: authStore.user.displayName || "",
            photoURL: authStore.user.photoURL || "",
            createdAt: new Date(),
            updatedAt: new Date(),
            wishlist: [],
            orders: [],
            addresses: [],
            phone: "",
            cpf: "",
            defaultAddressIndex: 0,
            orderHistory: [],
            rejectedOrders: [],
            pendingOrders: [],
            reviews: [],
            lastPurchaseDate: null,
          };

          // Criar documento usando a API do Nuxt
          const { data: createdCustomer, error: createError } =
            await useFetch<ICustomer>(`/api/customers/${authStore.user.uid}`, {
              method: "PUT",
              body: newCustomer,
            });

          if (createError.value) {
            throw new Error(
              createError.value?.message || "Erro ao criar cliente"
            );
          }

          // Atualizar estado local
          this.customer = new Customer(createdCustomer.value as ICustomer);
          console.log("cliente: ", this.customer);
        }
      } catch (error: any) {
        console.error("❌ [customerStore] Erro ao inicializar cliente:", error);
        this.error = "Erro ao carregar dados do cliente";
      } finally {
        this.isLoading = false;
      }
    },

    // Salva alterações do cliente no Firestore
    async save() {
      if (!this.customer) return;

      this.isLoading = true;
      this.error = null;

      try {
        // Salvar cliente usando a API do Nuxt
        const { data, error } = await useFetch<ICustomer>(
          `/api/customers/${this.customer.uid}`,
          {
            method: "PUT",
            body: {
              ...this.customer,
              updatedAt: new Date(),
            },
          }
        );

        if (error.value) {
          throw new Error(error.value?.message || "Erro ao salvar cliente");
        }

        // Atualizar estado local com os dados retornados
        if (data.value) {
          this.customer = new Customer(data.value as ICustomer);
        }
      } catch (e: any) {
        console.error("Erro ao salvar cliente:", e);
        this.error = "Erro ao salvar dados do cliente";
      } finally {
        this.isLoading = false;
      }
    },

    // Adiciona item à wishlist e salva no Firestore
    async addToWishlist(figureId: string) {
      if (!this.customer) return;

      this.isLoading = true;
      this.error = null;

      try {
        // Adicionar item à wishlist usando a API do Nuxt
        const { error } = await useFetch<{ success: boolean }>(
          `/api/customers/${this.customer.uid}/wishlist`,
          {
            method: "POST",
            body: { figureId },
          }
        );

        if (error.value) {
          throw new Error(
            error.value?.message || "Erro ao adicionar item à wishlist"
          );
        }

        // Atualizar estado local
        this.customer.addToWishlist(figureId);
      } catch (e: any) {
        console.error("Erro ao adicionar item à wishlist:", e);
        this.error = "Erro ao adicionar item à wishlist";
      } finally {
        this.isLoading = false;
      }
    },

    // Remove item da wishlist e salva no Firestore
    async removeFromWishlist(figureId: string) {
      if (!this.customer) return;

      this.isLoading = true;
      this.error = null;

      try {
        // Remover item da wishlist usando a API do Nuxt
        const { error } = await useFetch<{ success: boolean }>(
          `/api/customers/${this.customer.uid}/wishlist/${figureId}`,
          {
            method: "DELETE",
          }
        );

        if (error.value) {
          throw new Error(
            error.value?.message || "Erro ao remover item da wishlist"
          );
        }

        // Atualizar estado local
        this.customer.removeFromWishlist(figureId);
      } catch (e: any) {
        console.error("Erro ao remover item da wishlist:", e);
        this.error = "Erro ao remover item da wishlist";
      } finally {
        this.isLoading = false;
      }
    },

    // Salva pedido do Mercado Pago ou Stripe
    async saveOrder(orderData: {
      paymentId: string;
      collectionId: string;
      collectionStatus: string;
      paymentType: string;
      externalReference: string | null;
      merchantOrderId: string;
      preferenceId: string;
      siteId: string;
      processingMode: string;
      merchantAccountId: string | null;
      paymentProvider?: "mercadopago" | "stripe";
      stripePaymentIntentId?: string;
      stripeClientSecret?: string;
      productId?: string;
    }) {
      debugger;
      const authStore = useAuthStore();

      if (!authStore.user?.uid) {
        console.error("Erro ao salvar pedido: Usuário não autenticado");
        this.error = "Usuário não autenticado";
        return null;
      }

      // Verificar se o cliente está carregado
      if (!this.customer) {
        console.error("Erro ao salvar pedido: Cliente não carregado");
        this.error = "Cliente não carregado";
        return null;
      }

      // Inicializar o array de pedidos se não existir
      if (!this.customer.orders) {
        this.customer.orders = [];
      }

      this.isLoading = true;
      this.error = null;

      try {
        console.log("Iniciando saveOrder com dados:", orderData);

        // Verificar se temos pendingOrders
        if (
          !this.customer?.pendingOrders ||
          this.customer.pendingOrders.length === 0
        ) {
          console.warn(
            "Nenhum pedido pendente encontrado, criando pedido direto"
          );
          debugger;
          // Criar objeto de pedido sem usar pendingOrder
          const order: IOrder = {
            ...orderData,
            id: uuidv4(),
            paymentId: orderData.paymentId,
            collectionId: orderData.collectionId,
            collectionStatus: orderData.collectionStatus,
            paymentType: orderData.paymentType,
            externalReference: orderData.externalReference || "",
            merchantOrderId: orderData.merchantOrderId,
            preferenceId: orderData.preferenceId,
            siteId: orderData.siteId,
            processingMode: orderData.processingMode,
            merchantAccountId: orderData.merchantAccountId || "",
            createdAt: new Date(),
            updatedAt: new Date(),
            productId: orderData.productId || "",
            status: "approved",
            shipping: null,
            quantity: 1,
            customerId: authStore.user.uid,
            paymentProvider: orderData.paymentProvider || "mercadopago",
            stripePaymentIntentId: orderData.stripePaymentIntentId,
          };

          // Salvar pedido usando a API do Nuxt
          const { data, error } = await useFetch<{ order: IOrder }>(
            `/api/customers/${authStore.user.uid}/orders`,
            {
              method: "POST",
              body: order,
            }
          );

          if (error.value) {
            throw new Error(error.value?.message || "Erro ao salvar pedido");
          }

          // Atualizar o estado local
          if (data.value?.order) {
            // O array orders já foi inicializado no início do método
            this.customer.orders.push(data.value.order);
          }

          return data.value?.order || order;
        }

        // Tentar encontrar um pedido pendente correspondente
        let pendingOrder;

        if (
          orderData.paymentProvider === "stripe" &&
          orderData.stripePaymentIntentId
        ) {
          // Para Stripe, procurar pelo ID do PaymentIntent
          pendingOrder = this.customer.pendingOrders.find(
            (order) =>
              order.stripePaymentIntentId === orderData.stripePaymentIntentId
          );
        } else if (orderData.preferenceId) {
          // Para MercadoPago, procurar pelo preferenceId
          pendingOrder = this.customer.pendingOrders.find(
            (order) => order.preferenceId === orderData.preferenceId
          );
        }

        // Se não encontrou um pedido específico, usar o primeiro
        if (!pendingOrder) {
          console.log(
            "Nenhum pedido pendente específico encontrado, usando o primeiro"
          );
          pendingOrder = this.customer.pendingOrders[0];
        }

        console.log("Usando pedido pendente:", pendingOrder);

        // Criar objeto de pedido
        const order: IOrder = {
          id: uuidv4(),
          paymentId: orderData.paymentId,
          collectionId: orderData.collectionId,
          collectionStatus: orderData.collectionStatus,
          paymentType: orderData.paymentType,
          externalReference: orderData.externalReference || "",
          merchantOrderId: orderData.merchantOrderId,
          preferenceId: orderData.preferenceId,
          siteId: orderData.siteId,
          processingMode: orderData.processingMode,
          merchantAccountId: orderData.merchantAccountId || "",
          createdAt: new Date(),
          updatedAt: new Date(),
          productId: pendingOrder.productId || "",
          status: "approved",
          shipping: pendingOrder.shipping,
          quantity: pendingOrder.quantity || 1,
          customerId: authStore.user.uid,
          total: pendingOrder.total || 0,
          paymentProvider: orderData.paymentProvider || "mercadopago",
          stripePaymentIntentId:
            orderData.stripePaymentIntentId ||
            pendingOrder.stripePaymentIntentId,
        };

        // Salvar pedido usando a API do Nuxt
        const { data, error } = await useFetch<{
          success: boolean;
          message: string;
          order: IOrder;
        }>(`/api/customers/${authStore.user.uid}/orders`, {
          method: "POST",
          body: order,
        });

        if (error.value) {
          throw new Error(error.value?.message || "Erro ao salvar pedido");
        }

        // Verificar se a API retornou sucesso=false, o que indica que o produto já foi comprado
        if (data.value && data.value.success === false) {
          console.log(`API retornou: ${data.value.message}`);
          this.error =
            data.value.message || "Este produto já foi comprado anteriormente";

          // Mesmo assim, atualizar o estado local com o pedido existente
          if (data.value.order) {
            // Verificar se o produto já existe localmente
            // O array orders já foi inicializado no início do método
            const existingIndex = this.customer.orders.findIndex(
              (existingOrder) =>
                existingOrder.productId === data.value?.order.productId
            );

            if (existingIndex === -1) {
              // Adicionar o pedido ao estado local se não existir
              this.customer.orders.push(data.value.order);
            }
          }

          return data.value.order;
        }

        // Atualizar o estado local
        if (data.value?.order) {
          // O array orders já foi inicializado no início do método
          this.customer.orders.push(data.value.order);
        }

        return data.value?.order || order;
      } catch (error: any) {
        console.error("Erro ao salvar pedido:", error);
        this.error = "Erro ao salvar pedido";
        return null;
      } finally {
        this.isLoading = false;
      }
    },

    // Salva pedido rejeitado
    async saveRejectedOrder(rejectedOrder: IRejectedOrder) {
      if (!this.customer) return;

      this.isLoading = true;
      this.error = null;

      try {
        // Salvar pedido rejeitado usando a API do Nuxt
        const { data, error } = await useFetch<{
          rejectedOrder: IRejectedOrder;
        }>(`/api/customers/${this.customer.uid}/rejected-orders`, {
          method: "POST",
          body: rejectedOrder,
        });

        if (error.value) {
          throw new Error(
            error.value?.message || "Erro ao salvar pedido rejeitado"
          );
        }

        // Atualizar o estado local
        if (data.value?.rejectedOrder) {
          if (!this.customer.rejectedOrders) this.customer.rejectedOrders = [];
          this.customer.rejectedOrders.push(data.value.rejectedOrder);
        }
      } catch (e: any) {
        console.error("Erro ao salvar pedido rejeitado:", e);
        this.error = "Erro ao salvar pedido rejeitado";
      } finally {
        this.isLoading = false;
      }
    },

    // Salva pedido pendente
    async savePendingOrder(pendingOrder: IPendingOrder) {
      if (!this.customer || !this.customer.uid) {
        console.error(
          "Erro ao salvar pedido pendente: Usuário não autenticado ou sem UID"
        );
        this.error = "Usuário não autenticado";
        return null;
      }

      this.isLoading = true;
      this.error = null;

      try {
        // Verificar se já existe um pedido pendente com o mesmo productId no estado local
        if (this.customer.pendingOrders?.length > 0 && pendingOrder.productId) {
          const existingLocalOrder = this.customer.pendingOrders.find(
            (order) => order.productId === pendingOrder.productId
          );

          if (existingLocalOrder) {
            console.log(
              `Produto com ID ${pendingOrder.productId} já existe em pedidos pendentes localmente`
            );
            this.error = "Este produto já está em um pedido pendente";
            return existingLocalOrder;
          }
        }

        // Verificar se já existe um pedido pendente com o mesmo productId no Firebase
        // Isso é feito diretamente pela API, que retornará o pedido existente se encontrar
        console.log(
          `Salvando pedido pendente no Firebase para produto ID: ${pendingOrder.productId}`
        );

        // Salvar pedido pendente usando a API do Nuxt (Firebase primeiro)
        const { data, error } = await useFetch<{
          success: boolean;
          pendingOrder: IPendingOrder;
          message: string;
        }>(`/api/customers/${this.customer.uid}/pending-orders`, {
          method: "POST",
          body: pendingOrder,
        });

        if (error.value) {
          console.error("Erro retornado pela API:", error.value);
          this.error = error.value?.message || "Erro ao salvar pedido pendente";
          return null;
        }

        // Verificar se a API retornou sucesso=false, o que indica que o produto já existe
        if (data.value && data.value.success === false) {
          console.log(`API retornou: ${data.value.message}`);
          this.error =
            data.value.message || "Este produto já está em um pedido pendente";

          // Mesmo assim, atualizar o estado local com o pedido existente
          if (data.value.pendingOrder) {
            // Verificar se o produto já existe localmente
            const existingIndex =
              this.customer.pendingOrders?.findIndex(
                (order) =>
                  order.productId === data.value?.pendingOrder.productId
              ) ?? -1;

            if (existingIndex >= 0) {
              // Atualizar o pedido existente
              this.customer.pendingOrders[existingIndex] =
                data.value.pendingOrder;
            } else if (this.customer.pendingOrders) {
              // Adicionar o pedido ao estado local se não existir
              this.customer.pendingOrders.push(data.value.pendingOrder);
            } else {
              // Inicializar o array se não existir
              this.customer.pendingOrders = [data.value.pendingOrder];
            }
          }

          return data.value.pendingOrder;
        }

        // Depois de salvar no Firebase, atualizar o estado local
        if (data.value?.pendingOrder) {
          if (!this.customer.pendingOrders) this.customer.pendingOrders = [];

          // Verificar se a mensagem indica que o pedido foi atualizado
          if (data.value.message === "Pedido pendente atualizado") {
            console.log(
              "Pedido atualizado no Firebase, atualizando estado local"
            );
          }

          // Verificar se o produto já existe localmente
          const existingIndex = this.customer.pendingOrders.findIndex(
            (order) => order.productId === data.value?.pendingOrder.productId
          );

          if (existingIndex >= 0) {
            console.log(
              "Produto já existe localmente, atualizando informações"
            );
            this.customer.pendingOrders[existingIndex] =
              data.value.pendingOrder;
          } else {
            console.log("Adicionando novo pedido pendente ao estado local");
            this.customer.pendingOrders.push(data.value.pendingOrder);
          }

          console.log(
            "Pedido pendente salvo com sucesso no Firebase e atualizado localmente"
          );
          return data.value.pendingOrder;
        }

        return null;
      } catch (e: any) {
        console.error("Erro ao salvar pedido pendente:", e);
        this.error = "Erro ao salvar pedido pendente";
        return null;
      } finally {
        this.isLoading = false;
      }
    },

    // Adicionar endereço ao cliente
    async addAddress(address: IAddress) {
      if (!this.customer) return;

      this.isLoading = true;
      this.error = null;

      try {
        // Salvar endereço usando a API do Nuxt
        const { data, error } = await useFetch<{ address: IAddress }>(
          `/api/customers/${this.customer.uid}/addresses`,
          {
            method: "POST",
            body: address,
          }
        );

        if (error.value) {
          throw new Error(error.value?.message || "Erro ao adicionar endereço");
        }

        // Atualizar o estado local
        if (data.value?.address) {
          if (!this.customer.addresses) this.customer.addresses = [];

          // Se o novo endereço for padrão, remover a flag dos outros
          if (data.value.address.isDefault) {
            this.customer.addresses = this.customer.addresses.map((addr) => ({
              ...addr,
              isDefault: false,
            }));
          }

          this.customer.addresses.push(data.value.address);
        }

        return data.value?.address;
      } catch (e: any) {
        console.error("Erro ao adicionar endereço:", e);
        this.error = "Erro ao adicionar endereço";
        return null;
      } finally {
        this.isLoading = false;
      }
    },

    // Definir endereço como padrão
    async setDefaultAddress(addressId: string) {
      if (!this.customer) return;

      this.isLoading = true;
      this.error = null;

      try {
        // Definir endereço como padrão usando a API do Nuxt
        const { error } = await useFetch<{ success: boolean }>(
          `/api/customers/${this.customer.uid}/addresses/${addressId}/default`,
          {
            method: "PUT",
          }
        );

        if (error.value) {
          throw new Error(
            error.value?.message || "Erro ao definir endereço como padrão"
          );
        }

        // Atualizar o estado local
        // Encontrar o índice do endereço
        const addressIndex = this.customer.addresses.findIndex(
          (addr) => addr.id === addressId
        );

        if (addressIndex >= 0) {
          // Atualizar todos os endereços para não serem padrão
          this.customer.addresses = this.customer.addresses.map((addr) => ({
            ...addr,
            isDefault: false,
          }));

          // Definir o endereço selecionado como padrão
          this.customer.addresses[addressIndex].isDefault = true;
          this.customer.defaultAddressIndex = addressIndex;
        }
      } catch (e: any) {
        console.error("Erro ao definir endereço como padrão:", e);
        this.error = "Erro ao definir endereço como padrão";
      } finally {
        this.isLoading = false;
      }
    },

    // Atualizar endereço do cliente
    async updateAddress(addressId: string, addressData: Partial<IAddress>) {
      if (!this.customer || !this.customer.addresses) return null;

      this.isLoading = true;
      this.error = null;

      try {
        // Verificar se o endereço existe
        const addressIndex = this.customer.addresses.findIndex(
          (addr) => addr.id === addressId
        );
        if (addressIndex === -1) {
          throw new Error("Endereço não encontrado");
        }

        // Atualizar endereço usando a API do Nuxt
        const { data, error } = await useFetch<{ address: IAddress }>(
          `/api/customers/${this.customer.uid}/addresses/${addressId}`,
          {
            method: "PUT",
            body: addressData,
          }
        );

        if (error.value) {
          throw new Error(error.value?.message || "Erro ao atualizar endereço");
        }

        // Atualizar o estado local
        if (data.value?.address) {
          // Se o endereço atualizado for padrão, remover a flag dos outros
          if (data.value.address.isDefault) {
            this.customer.addresses = this.customer.addresses.map((addr) => ({
              ...addr,
              isDefault: addr.id === addressId,
            }));
          } else {
            // Atualizar apenas o endereço específico
            this.customer.addresses[addressIndex] = data.value.address;
          }
        }

        return data.value?.address || null;
      } catch (e: any) {
        console.error("Erro ao atualizar endereço:", e);
        this.error = e.message || "Erro ao atualizar endereço";
        return null;
      } finally {
        this.isLoading = false;
      }
    },

    // Remover endereço do cliente
    async removeAddress(addressId: string) {
      if (!this.customer || !this.customer.addresses) return;

      this.isLoading = true;
      this.error = null;

      try {
        // Verificar se o endereço é o padrão
        const addressToRemove = this.customer.addresses.find(
          (addr) => addr.id === addressId
        );
        if (addressToRemove?.isDefault) {
          throw new Error(
            "Não é possível remover um endereço padrão. Defina outro endereço como padrão primeiro."
          );
        }

        // Remover endereço usando a API do Nuxt
        const { error } = await useFetch(
          `/api/customers/${this.customer.uid}/addresses/${addressId}`,
          {
            method: "DELETE",
          }
        );

        if (error.value) {
          throw new Error(error.value?.message || "Erro ao remover endereço");
        }

        // Atualizar o estado local
        this.customer.addresses = this.customer.addresses.filter(
          (addr) => addr.id !== addressId
        );

        return true;
      } catch (e: any) {
        console.error("Erro ao remover endereço:", e);
        this.error = e.message || "Erro ao remover endereço";
        return false;
      } finally {
        this.isLoading = false;
      }
    },

    // Remover pedido pendente do cliente
    async removePendingOrder(orderId: string) {
      if (!this.customer || !this.customer.pendingOrders) return false;

      this.isLoading = true;
      this.error = null;

      try {
        // Verificar se o pedido pendente existe
        const pendingOrderIndex = this.customer.pendingOrders.findIndex(
          (order) => order.id === orderId
        );
        if (pendingOrderIndex === -1) {
          throw new Error("Pedido pendente não encontrado");
        }

        // Remover pedido pendente usando a API do Nuxt
        const { error } = await useFetch(
          `/api/customers/${this.customer.uid}/pending-orders/${orderId}`,
          {
            method: "DELETE",
          }
        );

        if (error.value) {
          throw new Error(
            error.value?.message || "Erro ao remover pedido pendente"
          );
        }

        // Atualizar o estado local
        this.customer.pendingOrders = this.customer.pendingOrders.filter(
          (order) => order.id !== orderId
        );

        return true;
      } catch (e: any) {
        console.error("Erro ao remover pedido pendente:", e);
        this.error = e.message || "Erro ao remover pedido pendente";
        return false;
      } finally {
        this.isLoading = false;
      }
    },

    // Reset do estado da store
    $reset() {
      this.customer = null;
      this.isLoading = false;
      this.error = null;
      this.initialized = false;
      console.log("🔄 [customerStore] Estado resetado");
    },
  },
});

// Para compatibilidade com código existente, mantemos o composable useCustomer
// que simplesmente retorna a store
export function useCustomer() {
  const store = useCustomerStore();
  const authStore = useAuthStore();

  // Inicializar cliente quando o usuário estiver autenticado
  onMounted(async () => {
    if (authStore.isAuthenticated && !store.customer && !store.initialized) {
      await store.initialize();
    }
  });

  // Observar mudanças no estado de autenticação
  watch(
    () => authStore.isAuthenticated,
    async (isAuthenticated) => {
      if (isAuthenticated && !store.customer && !store.initialized) {
        await store.initialize();
      } else if (!isAuthenticated) {
        // Limpar cliente quando o usuário deslogar
        store.$reset();
      }
    }
  );

  return store;
}
