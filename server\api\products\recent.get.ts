import { createError } from "h3";
import { collection, getDocs, query as firebaseQuery, where, orderBy, limit } from "firebase/firestore";
import { db } from "~/server/utils/firebase";
import { FigureData, formatFigureData } from "~/types/figures";
import { Coupon } from "~/types/coupons";
import { CACHE_EXPIRATION_TIME, getCachedCoupons, isCacheValid } from "~/server/utils/cache";

// Cache em memória para produtos recentes
let recentProductsCache = {
  data: null,
  timestamp: 0
};

// Usando a função getCachedCoupons do módulo de cache

export default defineEventHandler(async (event) => {
  try {
    // Obter parâmetros de consulta
    const queryParams = getQuery(event);
    const forceRefresh = queryParams.refresh === 'true';

    // Verificar se podemos usar o cache
    const cacheValid = isCacheValid(recentProductsCache, forceRefresh);

    // Calcular a data de 3 meses atrás
    const threeMonthsAgo = new Date();
    threeMonthsAgo.setMonth(threeMonthsAgo.getMonth() - 2);
    const threeMonthsAgoISOString = threeMonthsAgo.toISOString();

    let products = [];
    let coupons = [];

    // Se o cache for válido, use o cache
    if (cacheValid) {
      console.log('🔍 [API] Usando cache de produtos recentes');
      products = recentProductsCache.data;
      // Buscar cupons do cache compartilhado
      coupons = await getCachedCoupons();
    } else {
      console.log(`🔍 [API] Buscando todos os produtos recentes (após ${threeMonthsAgoISOString})`);

      // Otimização: Usar where para filtrar por data diretamente no Firebase
      // Isso reduz o número de documentos retornados
      const productsRef = collection(db, "products");

      // Criar uma consulta que filtra produtos ocultos e recentes
      // Nota: Isso requer um índice composto no Firebase
      try {
        const productsQuery = firebaseQuery(
          productsRef,
          where("hidden", "!=", true), // Filtrar produtos ocultos
          where("date_created", ">=", threeMonthsAgoISOString) // Filtrar por data
        );

        const querySnapshot = await getDocs(productsQuery);
        console.log(`🔍 [API] Consulta otimizada retornou ${querySnapshot.docs.length} documentos`);

        // Buscar cupons ativos para aplicar aos produtos
        coupons = await getCachedCoupons();

        // Processar os resultados
        products = querySnapshot.docs
          .map((doc) => {
            const figureData = { ...(doc.data() as FigureData) };
            return formatFigureData(figureData, doc.id, {
              coupons,
              ignoreCoupons: coupons.length === 0,
            });
          });
      } catch (indexError) {
        // Se a consulta falhar (provavelmente por falta de índice), usar a abordagem anterior
        console.error("❌ [API] Erro na consulta otimizada, usando fallback:", indexError);

        // Consulta de fallback
        const fallbackQuery = firebaseQuery(
          productsRef,
          where("hidden", "!=", true)
        );

        const querySnapshot = await getDocs(fallbackQuery);

        // Buscar cupons ativos para aplicar aos produtos
        coupons = await getCachedCoupons();

        // Processar os resultados com filtro no cliente
        console.log(`🔍 [API] Processando ${querySnapshot.docs.length} documentos (fallback)`);

        products = querySnapshot.docs
          .filter(doc => {
            const data = doc.data();
            return data.date_created && data.date_created >= threeMonthsAgoISOString;
          })
          .map((doc) => {
            const figureData = { ...(doc.data() as FigureData) };
            return formatFigureData(figureData, doc.id, {
              coupons,
              ignoreCoupons: coupons.length === 0,
            });
          });
      }

      // Atualizar o cache
      recentProductsCache.data = products;
      recentProductsCache.timestamp = Date.now();
      console.log('🔍 [API] Cache de produtos recentes atualizado');
    }

    // Ordenar por data de criação (mais recentes primeiro)
    products.sort((a, b) => {
      const dateA = new Date(a.date_created);
      const dateB = new Date(b.date_created);
      return dateB.getTime() - dateA.getTime();
    });

    console.log(`✅ [API] Encontrados ${products.length} produtos recentes`);

    return {
      products: products
    };
  } catch (error) {
    console.error("❌ [API] Erro ao buscar produtos recentes:", error);

    // Retornar uma resposta de erro mais detalhada
    const errorMessage = error instanceof Error ? error.message : "Erro desconhecido";

    // Retornar uma resposta vazia em vez de lançar um erro
    // Isso evita que a página quebre completamente
    return {
      products: [],
      error: {
        message: "Erro ao buscar produtos recentes: " + errorMessage,
        details: error instanceof Error ? error.stack : null
      }
    };
  }
});
