<template>
  <div class="relative bg-white p-4 rounded-lg border border-gray-200 shadow-sm animate-pulse">
    <div class="flex items-center space-x-4">
      <div class="flex-shrink-0">
        <div class="h-10 w-10 bg-gray-200 rounded"></div>
      </div>
      <div class="flex-1 min-w-0">
        <div class="h-4 bg-gray-200 rounded w-3/4"></div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// Componente de skeleton para categorias
</script>
