<template>
  <div class="bg-white rounded-lg shadow-md p-6 animate-pulse">
    <div class="flex flex-col sm:flex-row items-start gap-4 sm:gap-6">
      <!-- Figure image skeleton -->
      <div class="w-full sm:w-32 h-32 flex-shrink-0 mb-4 sm:mb-0 bg-gray-200 rounded"></div>

      <!-- Figure details skeleton -->
      <div class="flex-grow w-full">
        <div class="flex flex-col sm:flex-row justify-between items-start mb-4 gap-4">
          <div class="w-full">
            <!-- Title skeleton -->
            <div class="h-6 bg-gray-200 rounded w-3/4 mb-3"></div>
            
            <!-- Categories skeleton -->
            <div class="flex flex-wrap gap-2 mb-2">
              <div class="h-6 bg-gray-200 rounded-full w-20"></div>
              <div class="h-6 bg-gray-200 rounded-full w-24"></div>
              <div class="h-6 bg-gray-200 rounded-full w-16"></div>
            </div>
          </div>
          
          <!-- Action buttons skeleton -->
          <div class="flex space-x-2 mt-2 sm:mt-0">
            <div class="h-10 w-20 bg-gray-200 rounded"></div>
            <div class="h-10 w-20 bg-gray-200 rounded"></div>
          </div>
        </div>

        <!-- Price info skeleton -->
        <div class="grid grid-cols-1 sm:grid-cols-2 gap-2 sm:gap-4 mb-3">
          <div>
            <div class="h-4 bg-gray-200 rounded w-24 mb-1"></div>
            <div class="h-5 bg-gray-200 rounded w-16"></div>
          </div>
          <div>
            <div class="h-4 bg-gray-200 rounded w-24 mb-1"></div>
            <div class="h-5 bg-gray-200 rounded w-16"></div>
          </div>
        </div>

        <!-- Status tags skeleton -->
        <div class="flex flex-wrap gap-2 mt-2">
          <div class="h-6 bg-gray-200 rounded-full w-20"></div>
          <div class="h-6 bg-gray-200 rounded-full w-24"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// Componente de skeleton para cards de figures na área de administração
</script>
