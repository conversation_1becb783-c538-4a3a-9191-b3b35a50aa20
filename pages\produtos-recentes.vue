<template>
  <div>
    <main>
      <div class="container mx-auto px-4 py-8">
        <section class="mb-12">
          <h1 class="text-3xl font-bold text-center mb-8">
            PRODUTOS RECENTES
          </h1>

          <div v-if="error" class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6">
            <strong class="font-bold">Erro!</strong>
            <span class="block sm:inline"> {{ error }}</span>
          </div>

          <div v-else>
            <FiguresList
              :figures="recentProducts"
              :loading="loading"
              :error="error"
              :hide-overlay="false"
            />
          </div>
        </section>
      </div>
    </main>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted } from "vue";
import type { FormattedFigure } from "~/types/figures";
import FiguresList from "~/components/FiguresList.vue";
import { useRecentProductsStore } from "~/stores/recentProductsStore";

// SEO
useHead({
  title: "Produtos Recentes | Fidel Figures",
  meta: [
    {
      name: "description",
      content: "Confira os produtos mais recentes da Fidel Figures. Action figures em resina de alta qualidade.",
    },
  ],
  link: [{ rel: "canonical", href: "https://fidelfigures.com.br/produtos-recentes" }],
});

const recentProductsStore = useRecentProductsStore();

// Computed properties
const recentProducts = computed(() => recentProductsStore.products);
const loading = computed(() => recentProductsStore.loading);
const error = computed(() => recentProductsStore.error);

// Buscar produtos recentes
const fetchRecentProducts = async () => {
  await recentProductsStore.fetchRecentProducts();
};

// Inicialização
onMounted(() => {
  // Buscar produtos recentes
  fetchRecentProducts();
});
</script>
