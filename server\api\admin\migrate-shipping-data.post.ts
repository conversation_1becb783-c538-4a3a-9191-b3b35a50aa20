import { collection, getDocs, doc, updateDoc, writeBatch } from "firebase/firestore";
import { db } from "~/server/utils/firebase";
import { DEFAULT_SHIPPING_PROVIDER } from "~/constants/shippingProviders";
import type { IShippingProvider, IOrderShipping } from "~/types/customer";

const USERS_COLLECTION = "users";

/**
 * Rota para migrar dados de rastreamento existentes
 * POST /api/admin/migrate-shipping-data
 */
export default defineEventHandler(async (event) => {
  try {
    console.log('🔄 [API] Iniciando migração de dados de rastreamento');

    // Buscar todos os clientes
    const usersRef = collection(db, USERS_COLLECTION);
    const usersSnapshot = await getDocs(usersRef);

    let totalCustomers = 0;
    let totalOrdersUpdated = 0;
    let totalPendingOrdersUpdated = 0;
    let totalRejectedOrdersUpdated = 0;

    // Preparar transportadora padrão (Correios)
    const defaultProvider: IShippingProvider = {
      ...DEFAULT_SHIPPING_PROVIDER,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    // Usar batch para operações em lote
    const batch = writeBatch(db);

    for (const userDoc of usersSnapshot.docs) {
      const userData = userDoc.data();
      let customerUpdated = false;

      totalCustomers++;

      // Migrar pedidos aprovados
      if (userData.orders && Array.isArray(userData.orders)) {
        for (let i = 0; i < userData.orders.length; i++) {
          const order = userData.orders[i];
          
          // Se tem trackCode mas não tem shippingTracking, migrar
          if (order.trackCode && !order.shippingTracking) {
            const shippingTracking: IOrderShipping = {
              provider: defaultProvider,
              trackCode: order.trackCode,
              trackingUrl: `${defaultProvider.trackingUrl}${order.trackCode}`
            };

            userData.orders[i] = {
              ...order,
              shippingTracking
            };

            customerUpdated = true;
            totalOrdersUpdated++;
          }
        }
      }

      // Migrar pedidos pendentes
      if (userData.pendingOrders && Array.isArray(userData.pendingOrders)) {
        for (let i = 0; i < userData.pendingOrders.length; i++) {
          const order = userData.pendingOrders[i];
          
          // Se tem trackCode mas não tem shippingTracking, migrar
          if (order.trackCode && !order.shippingTracking) {
            const shippingTracking: IOrderShipping = {
              provider: defaultProvider,
              trackCode: order.trackCode,
              trackingUrl: `${defaultProvider.trackingUrl}${order.trackCode}`
            };

            userData.pendingOrders[i] = {
              ...order,
              shippingTracking
            };

            customerUpdated = true;
            totalPendingOrdersUpdated++;
          }
        }
      }

      // Migrar pedidos rejeitados
      if (userData.rejectedOrders && Array.isArray(userData.rejectedOrders)) {
        for (let i = 0; i < userData.rejectedOrders.length; i++) {
          const order = userData.rejectedOrders[i];
          
          // Se tem trackCode mas não tem shippingTracking, migrar
          if (order.trackCode && !order.shippingTracking) {
            const shippingTracking: IOrderShipping = {
              provider: defaultProvider,
              trackCode: order.trackCode,
              trackingUrl: `${defaultProvider.trackingUrl}${order.trackCode}`
            };

            userData.rejectedOrders[i] = {
              ...order,
              shippingTracking
            };

            customerUpdated = true;
            totalRejectedOrdersUpdated++;
          }
        }
      }

      // Se o cliente foi atualizado, adicionar ao batch
      if (customerUpdated) {
        const userRef = doc(db, USERS_COLLECTION, userDoc.id);
        batch.update(userRef, userData);
        console.log(`📦 Cliente ${userDoc.id} marcado para atualização`);
      }
    }

    // Executar todas as atualizações em lote
    if (totalOrdersUpdated > 0 || totalPendingOrdersUpdated > 0 || totalRejectedOrdersUpdated > 0) {
      await batch.commit();
      console.log('✅ [API] Batch de atualizações executado com sucesso');
    }

    const summary = {
      totalCustomers,
      totalOrdersUpdated,
      totalPendingOrdersUpdated,
      totalRejectedOrdersUpdated,
      totalUpdated: totalOrdersUpdated + totalPendingOrdersUpdated + totalRejectedOrdersUpdated
    };

    console.log('✅ [API] Migração concluída:', summary);

    return {
      success: true,
      message: 'Migração de dados de rastreamento concluída com sucesso',
      data: summary
    };

  } catch (error) {
    console.error('❌ [API] Erro durante a migração:', error);
    
    throw createError({
      statusCode: 500,
      statusMessage: "Erro interno do servidor durante a migração",
    });
  }
});
