import { defineStore } from "pinia";
import type { ICustomer, IOrder } from "~/types/customer";
import { Customer } from "~/types/customer";
import { useFetch } from "~/composables/useFetch";

interface CustomerState {
  customers: Customer[];
  loading: boolean;
  error: string | null;
}

export const useCustomerStore = defineStore("customer", {
  state: (): CustomerState => ({
    customers: [],
    loading: false,
    error: null,
  }),

  getters: {
    getCustomerById: (state) => (id: string) => {
      return state.customers.find((customer) => customer.id === id);
    },
    getAllOrders: (state) => {
      // Retorna todos os pedidos de todos os clientes em um único array
      const allOrders: IOrder[] = [];
      state.customers.forEach((customer) => {
        if (customer.orders && customer.orders.length > 0) {
          const ordersWithCustomerId = customer.orders.map((order) => ({
            ...order,
            customerId: customer.id,
          }));
          allOrders.push(...ordersWithCustomerId);
        }
      });
      return allOrders;
    },
    getOrderById: (state) => (orderId: string) => {
      // Procura um pedido específico em todos os clientes
      for (const customer of state.customers) {
        if (customer.orders) {
          const order = customer.orders.find((order) => order.id === orderId);
          if (order) return { order, customerId: customer.id };
        }
      }
      return null;
    },
    getOrdersByCustomerId: (state) => (customerId: string) => {
      const customer = state.customers.find((c) => c.id === customerId);
      return customer?.orders || [];
    },
  },

  actions: {
    async fetchAllCustomers(forceRefresh = false) {
      this.loading = true;
      this.error = null;

      try {
        console.log('🔍 [customerStore] Buscando todos os clientes...');

        // Usar a API do servidor para buscar todos os clientes
        const url = forceRefresh ? '/api/customers?refresh=true' : '/api/customers';
        const data = await $fetch(url);

        if (!data || !Array.isArray(data.customers)) {
          throw new Error('Formato de resposta inválido');
        }

        // Converter os dados para objetos Customer
        const customersData: Customer[] = data.customers.map((customerData: any) => {
          return new Customer({
            ...customerData,
            id: customerData.id || customerData.uid,
          });
        });

        this.customers = customersData;
        console.log(
          `✅ [customerStore] Carregados ${customersData.length} clientes com sucesso`
        );
      } catch (error: any) {
        console.error("❌ [customerStore] Erro ao buscar clientes:", error);
        this.error = "Erro ao carregar clientes. Tente novamente mais tarde.";
      } finally {
        this.loading = false;
      }
    },

    async updateCustomer(customer: Customer) {
      this.loading = true;
      this.error = null;

      try {
        console.log(`🔄 [customerStore] Atualizando cliente ${customer.id || customer.uid}...`);

        // Atualizar o cliente usando a API do Nuxt
        const { data, error } = await useFetch<ICustomer>(
          `/api/customers/${customer.id || customer.uid}`,
          {
            method: "PUT",
            body: {
              ...customer,
              updatedAt: new Date(),
            },
          }
        );

        if (error.value) {
          throw new Error(error.value?.message || "Erro ao atualizar cliente");
        }

        // Atualizar o cliente na lista
        if (data.value) {
          const index = this.customers.findIndex((c) => c.id === customer.id || c.uid === customer.uid);
          if (index !== -1) {
            this.customers[index] = new Customer({
              ...data.value,
              id: customer.id || customer.uid,
            });
          }

          console.log(`✅ [customerStore] Cliente ${customer.id || customer.uid} atualizado com sucesso`);
          return this.customers[index];
        }

        return customer;
      } catch (error) {
        console.error("❌ [customerStore] Erro ao atualizar cliente:", error);
        this.error = "Erro ao atualizar cliente. Tente novamente mais tarde.";
        throw error;
      } finally {
        this.loading = false;
      }
    },

    async fetchCustomerById(customerId: string) {
      this.loading = true;
      this.error = null;

      try {
        console.log(`🔍 [customerStore] Buscando cliente com ID ${customerId}...`);

        // Usar a API do servidor para buscar o cliente pelo ID
        const { data, error } = await useFetch(`/api/customers/${customerId}`);

        if (error.value) {
          throw new Error(error.value.message || `Erro ao buscar cliente ${customerId}`);
        }

        if (!data.value) {
          throw new Error(`Cliente com ID ${customerId} não encontrado`);
        }

        // Converter os dados para um objeto Customer
        const customer = new Customer({
          ...data.value,
          id: customerId,
        });

        // Atualizar ou adicionar o cliente na lista
        const index = this.customers.findIndex((c) => c.id === customerId);
        if (index !== -1) {
          this.customers[index] = customer;
        } else {
          this.customers.push(customer);
        }

        console.log(`✅ [customerStore] Cliente ${customerId} carregado com sucesso`);
        return customer;
      } catch (error: any) {
        console.error(`❌ [customerStore] Erro ao buscar cliente ${customerId}:`, error);
        this.error = "Erro ao carregar dados do cliente. Tente novamente mais tarde.";
        return null;
      } finally {
        this.loading = false;
      }
    },
  },
});
