// Script de debug para verificar status dos pedidos e critérios de avaliação
// Execute este script no console do navegador na página de pedidos

console.log('🔍 DEBUG: Verificando critérios para avaliação de produtos');

// Função para verificar um pedido específico
function debugOrder(order) {
  console.log('\n📦 Analisando pedido:', order);
  
  const status = order.status || order.collectionStatus;
  console.log('📊 Status do pedido:', status);
  
  const isApproved = status === 'approved' || status === 'concluída';
  console.log('✅ Status aprovado/concluído:', isApproved);
  
  const orderDate = new Date(order.createdAt);
  const now = new Date();
  const hoursSinceOrder = (now.getTime() - orderDate.getTime()) / (1000 * 60 * 60);
  
  console.log('📅 Data do pedido:', orderDate.toLocaleString('pt-BR'));
  console.log('⏰ Horas desde o pedido:', hoursSinceOrder.toFixed(2));
  console.log('⏳ Passou 1 hora mínima:', hoursSinceOrder >= 1);
  
  const canReview = isApproved && hoursSinceOrder >= 1;
  console.log('⭐ PODE AVALIAR:', canReview ? '✅ SIM' : '❌ NÃO');
  
  if (!canReview) {
    console.log('🚫 Motivos para não poder avaliar:');
    if (!isApproved) {
      console.log('   - Status não é "approved" ou "concluída"');
    }
    if (hoursSinceOrder < 1) {
      console.log('   - Menos de 1 hora desde a criação do pedido');
      console.log(`   - Faltam ${(1 - hoursSinceOrder).toFixed(2)} horas`);
    }
  }
  
  return canReview;
}

// Verificar todos os pedidos na página
function debugAllOrders() {
  // Tentar encontrar dados dos pedidos no Vue
  const app = document.querySelector('#__nuxt')?.__vue_app__;
  if (app) {
    console.log('🔍 Procurando dados dos pedidos no Vue...');
    
    // Procurar por componentes OrderCard
    const orderCards = document.querySelectorAll('[data-order-id]');
    if (orderCards.length > 0) {
      console.log(`📋 Encontrados ${orderCards.length} cards de pedidos`);
      orderCards.forEach((card, index) => {
        console.log(`\n--- PEDIDO ${index + 1} ---`);
        const orderId = card.getAttribute('data-order-id');
        console.log('🆔 ID do pedido:', orderId);
      });
    } else {
      console.log('⚠️ Nenhum card de pedido encontrado com data-order-id');
    }
  }
  
  // Verificar se existe customerStore
  if (window.$nuxt) {
    try {
      const customer = window.$nuxt.$store?.state?.customer || window.$nuxt.customer;
      if (customer?.orders) {
        console.log(`📋 Encontrados ${customer.orders.length} pedidos no store`);
        customer.orders.forEach((order, index) => {
          console.log(`\n--- PEDIDO ${index + 1} ---`);
          debugOrder(order);
        });
      } else {
        console.log('⚠️ Nenhum pedido encontrado no customer store');
      }
    } catch (error) {
      console.log('❌ Erro ao acessar store:', error);
    }
  }
}

// Função para simular mudança de status (apenas para teste)
function simulateApprovedOrder() {
  console.log('\n🧪 SIMULAÇÃO: Como tornar um pedido elegível para avaliação');
  console.log('1. O status deve ser "approved" ou "concluída"');
  console.log('2. Deve ter passado pelo menos 1 hora desde a criação');
  console.log('3. O usuário deve estar logado');
  console.log('4. O pedido deve ter um productId válido');
  
  console.log('\n💡 Para testar rapidamente, você pode:');
  console.log('1. Alterar temporariamente a validação de tempo no código');
  console.log('2. Ou criar um pedido de teste com status "approved"');
  console.log('3. Ou modificar um pedido existente no Firebase');
}

// Função para verificar se os componentes estão carregados
function checkComponents() {
  console.log('\n🔧 Verificando componentes carregados...');
  
  const hasOrderCard = document.querySelector('.order-card') || document.querySelector('[class*="order"]');
  console.log('📦 OrderCard encontrado:', !!hasOrderCard);
  
  const hasReviewButton = document.querySelector('button[class*="yellow"]') || 
                         document.querySelector('button:contains("Avaliar")');
  console.log('⭐ Botão de avaliação encontrado:', !!hasReviewButton);
  
  const hasReviewSection = document.querySelector('[class*="review"]');
  console.log('📝 Seção de avaliação encontrada:', !!hasReviewSection);
}

// Executar debug
console.log('🚀 Iniciando debug do sistema de avaliações...');
debugAllOrders();
checkComponents();
simulateApprovedOrder();

console.log('\n📋 RESUMO DOS CRITÉRIOS PARA AVALIAÇÃO:');
console.log('✅ Status do pedido: "approved" ou "concluída"');
console.log('✅ Tempo mínimo: 1 hora após a criação do pedido');
console.log('✅ Usuário logado com dados válidos');
console.log('✅ Produto com ID válido');

console.log('\n🔍 Se o botão não aparece, verifique:');
console.log('1. Status do pedido no Firebase');
console.log('2. Data de criação do pedido');
console.log('3. Se o usuário está logado');
console.log('4. Console do navegador para erros JavaScript');

console.log('\n💡 Para forçar a exibição (apenas para teste):');
console.log('1. Abra as ferramentas de desenvolvedor');
console.log('2. Vá para a aba Console');
console.log('3. Execute: localStorage.setItem("debug-reviews", "true")');
console.log('4. Recarregue a página');
