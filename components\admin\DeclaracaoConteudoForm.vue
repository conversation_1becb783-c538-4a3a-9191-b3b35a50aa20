<template>
  <div>
    <!-- Formulário para preenchimento da declaração -->
    <div class="mb-8">
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- Coluna da esquerda - Dados do remetente -->
        <div class="space-y-4">
          <h3 class="text-lg font-semibold text-gray-700 border-b pb-2">
            Dados do Remetente
          </h3>

          <div class="form-group">
            <label class="block text-sm font-medium text-gray-700 mb-1"
              >Nome</label
            >
            <input
              v-model="remetente.nome"
              type="text"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500"
              placeholder="Nome completo"
            />
          </div>

          <div class="form-group">
            <label class="block text-sm font-medium text-gray-700 mb-1"
              >CPF/CNPJ</label
            >
            <input
              v-model="remetente.documento"
              type="text"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500"
              placeholder="CPF ou CNPJ"
              v-mask="documentoMask"
            />
          </div>

          <div class="form-group">
            <label class="block text-sm font-medium text-gray-700 mb-1"
              >Endereço</label
            >
            <input
              v-model="remetente.endereco"
              type="text"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500"
              placeholder="Rua, número, complemento"
            />
          </div>

          <div class="grid grid-cols-2 gap-4">
            <div class="form-group">
              <label class="block text-sm font-medium text-gray-700 mb-1"
                >Cidade</label
              >
              <input
                v-model="remetente.cidade"
                type="text"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500"
                placeholder="Cidade"
              />
            </div>

            <div class="form-group">
              <label class="block text-sm font-medium text-gray-700 mb-1"
                >Estado</label
              >
              <select
                v-model="remetente.estado"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500"
              >
                <option value="">Selecione</option>
                <option v-for="estado in estados" :key="estado" :value="estado">
                  {{ estado }}
                </option>
              </select>
            </div>
          </div>

          <div class="form-group">
            <label class="block text-sm font-medium text-gray-700 mb-1"
              >CEP</label
            >
            <div class="relative">
              <input
                v-model="remetente.cep"
                type="text"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500"
                placeholder="00000-000"
                v-mask="'#####-###'"
                @blur="searchCEP('remetente')"
              />
              <div v-if="loadingCEP.remetente" class="absolute right-3 top-2">
                <i class="fas fa-spinner fa-spin text-gray-400"></i>
              </div>
            </div>
            <p v-if="cepError.remetente" class="mt-1 text-sm text-red-600">{{ cepError.remetente }}</p>
          </div>
        </div>

        <!-- Coluna da direita - Dados do destinatário -->
        <div class="space-y-4">
          <h3 class="text-lg font-semibold text-gray-700 border-b pb-2">
            Dados do Destinatário
          </h3>

          <!-- Seleção de cliente -->
          <div class="form-group">
            <label class="block text-sm font-medium text-gray-700 mb-1">
              Selecionar Cliente
            </label>
            <div class="relative">
              <input
                v-model="searchQuery"
                type="text"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500"
                placeholder="Buscar cliente por nome ou email"
                @input="searchClients"
                @focus="showClientResults = true"
              />
              <div v-if="loadingClients" class="absolute right-3 top-2">
                <i class="fas fa-spinner fa-spin text-gray-400"></i>
              </div>

              <!-- Resultados da busca de clientes -->
              <div
                v-if="showClientResults && filteredClients.length > 0"
                class="absolute z-50 w-full mt-1 bg-white border rounded-lg shadow-lg max-h-60 overflow-y-auto"
              >
                <div
                  v-for="client in filteredClients"
                  :key="client.id || client.uid"
                  @click="selectClient(client)"
                  class="p-3 border-b hover:bg-gray-50 cursor-pointer"
                >
                  <p class="font-medium">{{ client.name }}</p>
                  <p class="text-sm text-gray-500">{{ client.email }}</p>
                </div>
              </div>
              <div
                v-else-if="showClientResults && searchQuery && !loadingClients"
                class="absolute z-50 w-full mt-1 bg-white border rounded-lg shadow-lg p-3 text-center text-gray-500"
              >
                Nenhum cliente encontrado
              </div>
            </div>
          </div>

          <!-- Seleção de endereço (apenas se um cliente estiver selecionado) -->
          <div v-if="selectedClient" class="form-group">
            <label class="block text-sm font-medium text-gray-700 mb-1">
              Selecionar Endereço
            </label>
            <div v-if="selectedClient.addresses && selectedClient.addresses.length > 0">
              <div class="grid grid-cols-1 gap-2">
                <div
                  v-for="(address, index) in selectedClient.addresses"
                  :key="address.id || index"
                  @click="selectAddress(address)"
                  class="p-3 border rounded-md hover:bg-gray-50 cursor-pointer"
                  :class="{ 'border-2 border-red-500': selectedAddress && selectedAddress.id === address.id }"
                >
                  <div class="flex justify-between">
                    <p class="font-medium">{{ address.name || `Endereço ${index + 1}` }}</p>
                    <span v-if="address.isDefault" class="px-2 py-0.5 bg-green-100 text-green-800 text-xs rounded-full">
                      Padrão
                    </span>
                  </div>
                  <p class="text-sm">
                    {{ address.street }}, {{ address.number }}
                    {{ address.complement ? `, ${address.complement}` : '' }}
                  </p>
                  <p class="text-sm text-gray-500">
                    {{ address.neighborhood }}, {{ address.city }} - {{ address.state }}
                  </p>
                  <p class="text-sm text-gray-500">CEP: {{ address.zipCode }}</p>
                </div>
              </div>
            </div>
            <div v-else class="p-3 border rounded-md text-center text-gray-500">
              Este cliente não possui endereços cadastrados
            </div>
          </div>

          <div class="form-group">
            <label class="block text-sm font-medium text-gray-700 mb-1"
              >Nome</label
            >
            <input
              v-model="destinatario.nome"
              type="text"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500"
              placeholder="Nome completo"
            />
          </div>

          <div class="form-group">
            <label class="block text-sm font-medium text-gray-700 mb-1"
              >CPF/CNPJ</label
            >
            <input
              v-model="destinatario.documento"
              type="text"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500"
              placeholder="CPF ou CNPJ"
              v-mask="documentoMask"
            />
          </div>

          <div class="form-group">
            <label class="block text-sm font-medium text-gray-700 mb-1"
              >Endereço</label
            >
            <input
              v-model="destinatario.endereco"
              type="text"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500"
              placeholder="Rua, número, complemento"
            />
          </div>

          <div class="grid grid-cols-2 gap-4">
            <div class="form-group">
              <label class="block text-sm font-medium text-gray-700 mb-1"
                >Cidade</label
              >
              <input
                v-model="destinatario.cidade"
                type="text"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500"
                placeholder="Cidade"
              />
            </div>

            <div class="form-group">
              <label class="block text-sm font-medium text-gray-700 mb-1"
                >Estado</label
              >
              <select
                v-model="destinatario.estado"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500"
              >
                <option value="">Selecione</option>
                <option v-for="estado in estados" :key="estado" :value="estado">
                  {{ estado }}
                </option>
              </select>
            </div>
          </div>

          <div class="form-group">
            <label class="block text-sm font-medium text-gray-700 mb-1"
              >CEP</label
            >
            <div class="relative">
              <input
                v-model="destinatario.cep"
                type="text"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500"
                placeholder="00000-000"
                v-mask="'#####-###'"
                @blur="searchCEP('destinatario')"
              />
              <div v-if="loadingCEP.destinatario" class="absolute right-3 top-2">
                <i class="fas fa-spinner fa-spin text-gray-400"></i>
              </div>
            </div>
            <p v-if="cepError.destinatario" class="mt-1 text-sm text-red-600">{{ cepError.destinatario }}</p>
          </div>
        </div>
      </div>

      <!-- Conteúdo da declaração -->
      <div class="mt-8">
        <h3 class="text-lg font-semibold text-gray-700 border-b pb-2 mb-4">
          Conteúdo da Declaração
        </h3>

        <div class="overflow-x-auto">
          <table class="min-w-full bg-white border border-gray-200">
            <thead>
              <tr>
                <th
                  class="px-4 py-2 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  Descrição
                </th>
                <th
                  class="px-4 py-2 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  Quantidade
                </th>
                <th
                  class="px-4 py-2 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  Valor Unitário (R$)
                </th>
                <th
                  class="px-4 py-2 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  Valor Total (R$)
                </th>
                <th class="px-4 py-2 border-b border-gray-200 bg-gray-50"></th>
              </tr>
            </thead>
            <tbody>
              <tr
                v-for="(item, index) in itens"
                :key="index"
                class="border-b border-gray-200"
              >
                <td class="px-4 py-2">
                  <input
                    v-model="item.descricao"
                    type="text"
                    class="w-full px-2 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-red-500"
                  />
                </td>
                <td class="px-4 py-2">
                  <input
                    v-model.number="item.quantidade"
                    type="number"
                    min="1"
                    class="w-full px-2 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-red-500"
                    @input="calcularTotal(index)"
                  />
                </td>
                <td class="px-4 py-2">
                  <input
                    v-model.number="item.valorUnitario"
                    type="number"
                    min="0"
                    step="0.01"
                    class="w-full px-2 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-red-500"
                    @input="calcularTotal(index)"
                  />
                </td>
                <td class="px-4 py-2">
                  <input
                    v-model.number="item.valorTotal"
                    type="number"
                    readonly
                    class="w-full px-2 py-1 bg-gray-100 border border-gray-300 rounded-md"
                  />
                </td>
                <td class="px-4 py-2">
                  <button
                    @click="removerItem(index)"
                    class="text-red-500 hover:text-red-700"
                    title="Remover item"
                  >
                    <i class="fas fa-trash"></i>
                  </button>
                </td>
              </tr>
            </tbody>
            <tfoot>
              <tr>
                <td colspan="5" class="px-4 py-2">
                  <button
                    @click="adicionarItem"
                    class="text-sm bg-gray-200 hover:bg-gray-300 text-gray-700 font-semibold py-1 px-3 rounded-md flex items-center"
                  >
                    <i class="fas fa-plus mr-1"></i> Adicionar Item
                  </button>
                </td>
              </tr>
              <tr>
                <td colspan="3" class="px-4 py-2 text-right font-semibold">
                  Valor Total da Declaração:
                </td>
                <td class="px-4 py-2">
                  <input
                    v-model="valorTotalDeclaracao"
                    type="text"
                    readonly
                    class="w-full px-2 py-1 bg-gray-100 border border-gray-300 rounded-md font-semibold"
                  />
                </td>
                <td></td>
              </tr>
            </tfoot>
          </table>
        </div>
      </div>

      <!-- Observações -->
      <div class="mt-6">
        <label class="block text-sm font-medium text-gray-700 mb-1"
          >Observações</label
        >
        <textarea
          v-model="observacoes"
          rows="3"
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500"
          placeholder="Observações adicionais (opcional)"
        ></textarea>
      </div>

      <!-- Botões de ação -->
      <div class="mt-8 flex justify-end space-x-4">
        <button
          @click="limparFormulario"
          class="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition-colors"
        >
          Limpar
        </button>
        <button
          @click="preencherDadosExemplo"
          class="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors"
        >
          Preencher Exemplo
        </button>
        <button
          @click="visualizarDeclaracao"
          class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
          :disabled="!formularioValido"
        >
          Visualizar Declaração
        </button>
      </div>
    </div>

    <!-- Área de visualização da declaração (será convertida em PDF) -->
    <div
      v-if="mostrarPreview"
      class="mt-8 p-4 border border-gray-300 rounded-md bg-white"
    >
      <div class="flex justify-between mb-4">
        <h3 class="text-lg font-semibold">Pré-visualização da Declaração</h3>
        <button
          @click="gerarPDF"
          class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
        >
          Baixar PDF
        </button>
      </div>

      <div
        id="declaracao-conteudo"
        class="declaracao-pdf border border-gray-300 p-1"
      >
        <!-- Título -->
        <table class="w-full border border-black">
          <tr>
            <td class="text-center font-bold border border-black py-1 text-sm">
              DECLARAÇÃO DE CONTEÚDO
            </td>
          </tr>
        </table>

        <!-- Dados do remetente e destinatário -->
        <table class="w-full border border-black">
          <tr>
            <td
              class="w-1/2 text-center font-bold  border border-black py-0.5 text-xs"
            >
              REMETENTE
            </td>
            <td
              class="w-1/2 text-center font-bold  border border-black py-0.5 text-xs"
            >
              DESTINATÁRIO
            </td>
          </tr>
          <tr>
            <td class="border border-black py-2">
              <div class="flex items-center">
                <span class="font-bold px-1 text-xs">NOME:</span>
                <span class="text-xs">{{ remetente.nome }}</span>
              </div>
            </td>
            <td class="border border-black py-2">
              <div class="flex items-center">
                <span class="font-bold px-1 text-xs">NOME:</span>
                <span class="text-xs">{{ destinatario.nome }}</span>
              </div>
            </td>
          </tr>
          <tr>
            <td class="border border-black py-2">
              <div class="flex items-center">
                <span class="font-bold px-1 text-xs">ENDEREÇO:</span>
                <span class="text-xs">{{ remetente.endereco }}</span>
              </div>
            </td>
            <td class="border border-black py-2">
              <div class="flex items-center">
                <span class="font-bold px-1 text-xs">ENDEREÇO:</span>
                <span class="text-xs">{{ destinatario.endereco }}</span>
              </div>
            </td>
          </tr>
          <tr>
            <td class="border border-black py-2">
              <div class="flex items-center">
                <div class="w-3/4">
                  <span class="font-bold px-1 text-xs">CIDADE:</span>
                  <span class="text-xs">{{ remetente.cidade }}</span>
                </div>
                <div class="w-1/4 border-l border-black pl-1">
                  <span class="font-bold px-1 text-xs">UF:</span>
                  <span class="text-xs">{{ remetente.estado }}</span>
                </div>
              </div>
            </td>
            <td class="border border-black py-2">
              <div class="flex items-center">
                <div class="w-3/4">
                  <span class="font-bold px-1 text-xs">CIDADE:</span>
                  <span class="text-xs">{{ destinatario.cidade }}</span>
                </div>
                <div class="w-1/4 border-l border-black pl-1">
                  <span class="font-bold px-1 text-xs">UF:</span>
                  <span class="text-xs">{{ destinatario.estado }}</span>
                </div>
              </div>
            </td>
          </tr>
          <tr>
            <td class="border border-black py-2">
              <div class="flex items-center">
                <div class="w-1/3">
                  <span class="font-bold px-1 text-xs">CEP:</span>
                  <span class="text-xs">{{ remetente.cep }}</span>
                </div>
                <div class="w-2/3 border-l border-black pl-1">
                  <span class="font-bold px-1 text-xs">CPF/CNPJ:</span>
                  <span class="text-xs">{{ remetente.documento }}</span>
                </div>
              </div>
            </td>
            <td class="border border-black py-2">
              <div class="flex items-center">
                <div class="w-1/3">
                  <span class="font-bold px-1 text-xs">CEP:</span>
                  <span class="text-xs">{{ destinatario.cep }}</span>
                </div>
                <div class="w-2/3 border-l border-black pl-1">
                  <span class="font-bold px-1 text-xs">CPF/CNPJ:</span>
                  <span class="text-xs">{{ destinatario.documento }}</span>
                </div>
              </div>
            </td>
          </tr>
        </table>

        <!-- Identificação dos bens -->
        <table class="w-full border border-black">
          <tr>
            <td
              colspan="4"
              class="text-center font-bold border border-black py-1 text-xs"
            >
              IDENTIFICAÇÃO DOS BENS
            </td>
          </tr>
          <tr>
            <td
              class="w-1/12 text-center font-bold border border-black py-1 text-xs"
            >
              ITEM
            </td>
            <td
              class="w-7/12 text-center font-bold border border-black py-1 text-xs"
            >
              CONTEÚDO
            </td>
            <td
              class="w-2/12 text-center font-bold border border-black py-1 text-xs"
            >
              QUANT.
            </td>
            <td
              class="w-2/12 text-center font-bold border border-black py-1 text-xs"
            >
              VALOR
            </td>
          </tr>
          <tr
            v-for="(item, index) in itens"
            :key="index"
            class="border border-black"
          >
            <td class="border border-black text-center text-xs py-2">
              {{ index + 1 }}
            </td>
            <td class="border border-black px-1 text-xs py-2">
              {{ item.descricao }}
            </td>
            <td class="border border-black text-center text-xs py-2">
              {{ item.quantidade }}
            </td>
            <td class="border border-black text-right px-1 text-xs py-2">
              {{ formatarValor(item.valorTotal) }}
            </td>
          </tr>
          <!-- Linhas vazias para completar o formulário -->
          <tr
            v-for="i in Math.max(0, 5 - itens.length)"
            :key="'empty-' + i"
            class="border border-black"
          >
            <td class="border border-black text-center py-2">&nbsp;</td>
            <td class="border border-black py-2">&nbsp;</td>
            <td class="border border-black text-center py-2">&nbsp;</td>
            <td class="border border-black text-right py-2">&nbsp;</td>
          </tr>
          <tr>
            <td
              colspan="2"
              class="border border-black text-right font-bold pr-2 text-xs py-2"
            >
              TOTAIS
            </td>
            <td class="border border-black text-center text-xs py-2">
              {{ itens.reduce((sum, item) => sum + item.quantidade, 0) }}
            </td>
            <td
              class="border border-black text-right px-1 font-bold text-xs py-2"
            >
              {{ formatarValor(parseFloat(valorTotalDeclaracao)) }}
            </td>
          </tr>
          <tr>
            <td
              colspan="3"
              class="border border-black text-right font-bold pr-2 text-xs py-2"
            >
              PESO TOTAL (kg)
            </td>
            <td class="border border-black text-center py-2">&nbsp;</td>
          </tr>
        </table>

        <!-- Declaração -->
        <table class="w-full border border-black">
          <tr>
            <td
              class="text-center font-bold border border-black py-1 text-xs"
            >
              DECLARAÇÃO
            </td>
          </tr>
          <tr>
            <td class="border border-black p-2 text-justify text-xs">
              <p class="leading-relaxed">
                Declaro que não me enquadro no conceito de contribuinte previsto
                no art. 4º da Lei Complementar nº 87/1996, uma vez que não
                realizo, com habitualidade ou em volume que caracterize intuito
                comercial, operações de circulação de mercadoria, ainda que se
                iniciem no exterior, ou estou dispensado da emissão da nota
                fiscal por força da legislação tributária vigente,
                responsabilizando-me, nos termos da lei e a quem de direito, por
                informações inverídicas.
              </p>
              <p class="mt-2 leading-relaxed">
                Declaro ainda que não estou postando conteúdo inflamável,
                explosivo, causador de combustão espontânea, tóxico, corrosivo,
                gás ou qualquer outro conteúdo que conste na lista de proibições
                e restrições disponível no site dos Correios:
                <a
                  href="https://www.correios.com.br/enviar/proibicoes-e-restricoes/proibicoes-e-restricoes"
                  class="text-blue-600 underline"
                  >https://www.correios.com.br/enviar/proibicoes-e-restricoes/proibicoes-e-restricoes</a
                >.
              </p>
            </td>
          </tr>
          <tr>
            <td class="border border-black p-1">
              <div class="flex px-4 items-center text-xs mt-4">
                <span
                  >___________________________, _______ de
                  __________________________ de _________</span
                >
                <div class="flex justify-center items-center mt-4 ml-2">
                  <div class="border-t border-black text-xs mt-4 mb-1 ml-4">
                    <span class="">Assinatura do Declarante/Remetente</span>
                  </div>
                </div>
              </div>
            </td>
          </tr>
        </table>

        <!-- Observação -->
        <table class="w-full border border-black mt-1">
          <tr>
            <td class="border border-black p-2">
              <div class="flex">
                <span class="font-bold text-xs">OBSERVAÇÃO:</span>
              </div>
              <div class="text-xs text-justify">
                <p class="leading-relaxed">
                  Constitui crime contra a ordem tributária suprimir ou reduzir
                  tributo, ou contribuição social e qualquer acessório (Lei
                  8.137/90 Art. 1º, V).
                </p>
              </div>
            </td>
          </tr>
        </table>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from "vue";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { useCustomerStore } from "~/stores/customerStore";
import debounce from "lodash/debounce";

// Flag para controlar a exibição da pré-visualização
const mostrarPreview = ref(false);

// Estado para controle de busca de CEP
const loadingCEP = ref({
  remetente: false,
  destinatario: false
});

// Mensagens de erro para busca de CEP
const cepError = ref({
  remetente: '',
  destinatario: ''
});

// Estado para seleção de cliente
const customerStore = useCustomerStore();
const searchQuery = ref('');
const filteredClients = ref([]);
const selectedClient = ref(null);
const showClientResults = ref(false);
const loadingClients = ref(false);

// Estado para seleção de endereço
const selectedAddress = ref(null);

// Lista de estados brasileiros
const estados = [
  "AC",
  "AL",
  "AP",
  "AM",
  "BA",
  "CE",
  "DF",
  "ES",
  "GO",
  "MA",
  "MT",
  "MS",
  "MG",
  "PA",
  "PB",
  "PR",
  "PE",
  "PI",
  "RJ",
  "RN",
  "RS",
  "RO",
  "RR",
  "SC",
  "SP",
  "SE",
  "TO",
];

// Dados do remetente
const remetente = ref({
  nome: "Danillo Fidel Gomes",
  documento: "09297644635",
  endereco: "Avenida Estrela do Sul, 662. ap 403",
  cidade: "Uberlândia",
  estado: "MG",
  cep: "38400-339",
});

// Dados do destinatário
const destinatario = ref({
  nome: "",
  documento: "",
  endereco: "",
  cidade: "",
  estado: "",
  cep: "",
});

// Itens da declaração
const itens = ref([
  { descricao: "", quantidade: 1, valorUnitario: 0, valorTotal: 0 },
]);

// Observações
const observacoes = ref("");

// Valor total da declaração
const valorTotalDeclaracao = ref("0.00");

// Formatador de data
const formatDate = (date) => {
  return format(date, "dd/MM/yyyy", { locale: ptBR });
};

// Máscara para CPF/CNPJ
const documentoMask = computed(() => {
  return "###.###.###-##";
});

// Verificar se o formulário está válido
const formularioValido = computed(() => {
  // Verificar dados do remetente
  const remetenteValido =
    remetente.value.nome &&
    remetente.value.documento &&
    remetente.value.endereco &&
    remetente.value.cidade &&
    remetente.value.estado &&
    remetente.value.cep;

  // Verificar dados do destinatário
  const destinatarioValido =
    destinatario.value.nome &&
    destinatario.value.documento &&
    destinatario.value.endereco &&
    destinatario.value.cidade &&
    destinatario.value.estado &&
    destinatario.value.cep;

  // Verificar se há pelo menos um item com descrição e valores
  const itensValidos =
    itens.value.length > 0 &&
    itens.value.every((item) => item.descricao && item.quantidade > 0);

  return remetenteValido && destinatarioValido && itensValidos;
});

// Métodos
const calcularTotal = (index) => {
  const item = itens.value[index];
  item.valorTotal = parseFloat(
    (item.quantidade * item.valorUnitario).toFixed(2)
  );

  // Recalcular o valor total da declaração
  valorTotalDeclaracao.value = itens.value
    .reduce((total, item) => total + item.valorTotal, 0)
    .toFixed(2);
};

const adicionarItem = () => {
  itens.value.push({
    descricao: "",
    quantidade: 1,
    valorUnitario: 0,
    valorTotal: 0,
  });
};

const removerItem = (index) => {
  if (itens.value.length > 1) {
    itens.value.splice(index, 1);

    // Recalcular o valor total da declaração
    valorTotalDeclaracao.value = itens.value
      .reduce((total, item) => total + item.valorTotal, 0)
      .toFixed(2);
  }
};

// Função para buscar endereço pelo CEP usando a API ViaCEP
const searchCEP = async (tipo) => {
  // Limpar mensagem de erro anterior
  cepError.value[tipo] = '';

  // Verificar se o CEP tem o formato correto
  const cep = tipo === 'remetente' ? remetente.value.cep : destinatario.value.cep;
  const cepNumerico = cep.replace(/\D/g, '');

  if (cepNumerico.length !== 8) {
    return;
  }

  try {
    // Ativar indicador de carregamento
    loadingCEP.value[tipo] = true;

    // Fazer a requisição para a API ViaCEP
    const response = await fetch(`https://viacep.com.br/ws/${cepNumerico}/json/`);
    const data = await response.json();

    // Verificar se a API retornou erro
    if (data.erro) {
      cepError.value[tipo] = 'CEP não encontrado';
      return;
    }

    // Preencher os campos do endereço com os dados retornados
    if (tipo === 'remetente') {
      remetente.value.endereco = data.logradouro || '';
      remetente.value.cidade = data.localidade || '';
      remetente.value.estado = data.uf || '';
    } else {
      destinatario.value.endereco = data.logradouro || '';
      destinatario.value.cidade = data.localidade || '';
      destinatario.value.estado = data.uf || '';
    }

  } catch (error) {
    console.error('Erro ao buscar CEP:', error);
    cepError.value[tipo] = 'Erro ao buscar CEP. Tente novamente.';
  } finally {
    // Desativar indicador de carregamento
    loadingCEP.value[tipo] = false;
  }
};

const formatarValor = (valor) => {
  if (valor === undefined || isNaN(valor)) {
    return "0,00";
  }
  return valor.toLocaleString("pt-BR", {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  });
};

const limparFormulario = () => {
  // Limpar dados do destinatário
  destinatario.value = {
    nome: "",
    documento: "",
    endereco: "",
    cidade: "",
    estado: "",
    cep: "",
  };

  // Limpar itens
  itens.value = [
    { descricao: "", quantidade: 1, valorUnitario: 0, valorTotal: 0 },
  ];

  // Limpar observações
  observacoes.value = "";

  // Resetar valor total
  valorTotalDeclaracao.value = "0.00";
};

const preencherDadosExemplo = () => {
  // Preencher dados do remetente
  remetente.value = {
    nome: "Fidel Figures",
    documento: "123.456.789-00",
    endereco: "Rua Exemplo, 123",
    cidade: "São Paulo",
    estado: "SP",
    cep: "01234-567",
  };

  // Preencher dados do destinatário
  destinatario.value = {
    nome: "Cliente Exemplo",
    documento: "987.654.321-00",
    endereco: "Av. Cliente, 456",
    cidade: "Rio de Janeiro",
    estado: "RJ",
    cep: "20000-000",
  };

  // Preencher itens
  itens.value = [
    {
      descricao: "Action Figure Colecionável",
      quantidade: 1,
      valorUnitario: 299.9,
      valorTotal: 299.9,
    },
  ];

  // Preencher observações
  observacoes.value = "Produto frágil, manusear com cuidado.";

  // Atualizar valor total
  valorTotalDeclaracao.value = "299.90";
};

// Função removida para evitar duplicação

// Função para visualizar a declaração
const visualizarDeclaracao = () => {
  mostrarPreview.value = true;

  // Rolar para a pré-visualização
  setTimeout(() => {
    const previewElement = document.getElementById("declaracao-conteudo");
    if (previewElement) {
      previewElement.scrollIntoView({ behavior: "smooth" });
    }
  }, 100);
};

// Função para gerar o PDF com duas páginas
const gerarPDF = async () => {
  try {
    console.log("Iniciando geração do PDF com duas páginas...");

    // Importar html2pdf dinamicamente
    const html2pdfModule = await import("html2pdf.js");
    const html2pdf = html2pdfModule.default || html2pdfModule;

    console.log("Biblioteca html2pdf carregada com sucesso");

    // Criar um container temporário para as duas páginas
    const container = document.createElement('div');
    container.className = 'pdf-container';

    // Obter o elemento original
    const originalElement = document.getElementById("declaracao-conteudo");

    if (originalElement) {
      console.log("Elemento encontrado, preparando PDF de duas páginas...");

      // Primeira página - clonar o conteúdo original completo
      const pagina1 = originalElement.cloneNode(true);
      pagina1.className = 'pdf-page';
      container.appendChild(pagina1);

      // Segunda página - apenas com cabeçalho, remetente e destinatário
      const pagina2 = document.createElement('div');
      pagina2.className = 'pdf-page';

      // Clonar apenas as partes do cabeçalho, remetente e destinatário
      const tabelas = originalElement.querySelectorAll('table');

      // Título (primeira tabela)
      if (tabelas.length > 0) {
        const tituloClone = tabelas[0].cloneNode(true);
        pagina2.appendChild(tituloClone);
      }

      // Dados do remetente e destinatário (segunda tabela)
      if (tabelas.length > 1) {
        const dadosClone = tabelas[1].cloneNode(true);
        pagina2.appendChild(dadosClone);
      }

      // Adicionar a segunda página ao container
      container.appendChild(pagina2);

      // Adicionar o container temporário ao documento
      document.body.appendChild(container);

      // Configurações do PDF
      const options = {
        margin: 5,
        filename: `declaracao_conteudo_${format(new Date(), "dd-MM-yyyy")}.pdf`,
        image: { type: "jpeg", quality: 0.98 },
        html2canvas: {
          scale: 1.5,
          useCORS: true,
          logging: true,
          letterRendering: true,
          allowTaint: true,
        },
        jsPDF: {
          unit: "mm",
          format: "a4",
          orientation: "portrait",
          compress: true,
          precision: 2,
        },
        pagebreak: { mode: ['avoid-all', 'css', 'legacy'] }
      };

      try {
        // Gerar o PDF
        await html2pdf().from(container).set(options).save();
        console.log("PDF com duas páginas gerado com sucesso!");

        // Remover o container temporário após gerar o PDF
        document.body.removeChild(container);
      } catch (pdfError) {
        console.error("Erro ao gerar o PDF:", pdfError);
        // Remover o container temporário em caso de erro
        if (document.body.contains(container)) {
          document.body.removeChild(container);
        }
      }
    } else {
      console.error("Elemento original não encontrado");
    }
  } catch (error) {
    console.error("Erro ao gerar PDF:", error);
  }
};

// Função para buscar clientes
const searchClients = debounce(async () => {
  if (!searchQuery.value.trim()) {
    filteredClients.value = [];
    showClientResults.value = false;
    return;
  }

  loadingClients.value = true;
  showClientResults.value = true;

  try {
    // Carregar clientes se ainda não estiverem carregados
    if (!customerStore.initialized) {
      await customerStore.fetchAllCustomers();
    }

    // Filtrar clientes com base na busca
    const query = searchQuery.value.toLowerCase();
    filteredClients.value = customerStore.customers.filter(
      client =>
        client.name?.toLowerCase().includes(query) ||
        client.email?.toLowerCase().includes(query)
    );
  } catch (error) {
    console.error("Erro ao buscar clientes:", error);
    filteredClients.value = [];
  } finally {
    loadingClients.value = false;
  }
}, 300);

// Função para selecionar um cliente
const selectClient = (client) => {
  selectedClient.value = client;
  searchQuery.value = client.name;
  showClientResults.value = false;

  // Resetar endereço selecionado
  selectedAddress.value = null;
};

// Função para selecionar um endereço
const selectAddress = (address) => {
  selectedAddress.value = address;

  // Preencher os dados do destinatário com o endereço selecionado
  if (address) {
    destinatario.value = {
      nome: selectedClient.value.name,
      documento: selectedClient.value.cpf,
      endereco: `${address.street}, ${address.number}${address.complement ? `, ${address.complement}` : ''}, ${address.neighborhood}`,
      cidade: address.city,
      estado: address.state,
      cep: address.zipCode
    };
  }
};

// Função para fechar o dropdown de clientes quando clicar fora
const closeClientResults = (e) => {
  const target = e.target;
  if (!target.closest('.relative')) {
    showClientResults.value = false;
  }
};

// Inicializar dados quando o componente for montado
onMounted(async () => {
  console.log("Componente DeclaracaoConteudoForm montado com sucesso");

  // Inicializar store de clientes
  if (!customerStore.initialized) {
    try {
      await customerStore.fetchAllCustomers();
    } catch (error) {
      console.error("Erro ao carregar clientes:", error);
    }
  }

  // Adicionar evento para fechar dropdown ao clicar fora
  document.addEventListener('click', closeClientResults);
});

// Remover event listener quando o componente for desmontado
onUnmounted(() => {
  document.removeEventListener('click', closeClientResults);
});
</script>

<style scoped>
/* Estilos para o PDF */
.declaracao-pdf {
  font-family: Arial, sans-serif;
  padding: 5px;
  max-width: 100%; /* Usar toda a largura disponível */
  margin: 0 auto;
  background-color: white;
  font-size: 11px; /* Reduzir o tamanho da fonte base */
}

/* Estilos para o container de múltiplas páginas */
.pdf-container {
  position: absolute;
  left: -9999px;
  width: 210mm; /* Largura A4 */
}

/* Estilos para cada página do PDF */
.pdf-page {
  font-family: Arial, sans-serif;
  padding: 5px;
  width: 100%;
  margin: 0 auto;
  background-color: white;
  font-size: 11px;
  page-break-after: always;
  box-sizing: border-box;
}

/* Garantir que a última página não tenha quebra de página após */
.pdf-page:last-child {
  page-break-after: avoid;
}

/* Estilos para as tabelas */
table {
  border-collapse: collapse;
  margin-bottom: 0;
  width: 100%;
}

/* Estilos para células vazias */
td:empty {
  height: 24px;
}

/* Garantir que todas as células tenham altura mínima */
td {
  min-height: 24px;
  vertical-align: middle;
}

table,
th,
td {
  border: 1px solid black;
  padding: 2px;
}

/* Ajustar espaçamento entre elementos */
td {
  padding: 4px 2px;
  line-height: 1.4;
  vertical-align: middle;
}

/* Ajustar tamanho das fontes */
.font-bold {
  font-size: 11px;
}

/* Garantir que o texto não seja cortado e esteja centralizado verticalmente */
.text-xs {
  line-height: 1.5;
  margin-top: 2px;
  margin-bottom: 2px;
}

/* Ajustar alinhamento vertical dos elementos flex */
.flex.items-center {
  min-height: 24px;
}

/* Estilos para o PDF */
@media print {
  .declaracao-pdf,
  .pdf-page {
    padding: 0;
    margin: 0;
  }

  body {
    margin: 0;
    padding: 0;
  }

  @page {
    size: A4;
    margin: 0;
  }
}
</style>
