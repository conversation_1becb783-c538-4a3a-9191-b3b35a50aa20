import { collection, getDocs, doc, updateDoc } from "firebase/firestore";
import { db } from "~/server/utils/firebase";
import { FILTER_WORDS_TO_REMOVE, FILTER_REGEX_PATTERNS } from "~/config/constants";

const COLLECTION_NAME = "products";

// Gerar tokens de busca para um título
const generateSearchTokens = (title: string): string[] => {
  if (!title) return [];

  const tokens = new Set<string>();
  const normalizedTitle = title.toLowerCase().trim();

  // Adiciona o título completo após remover palavras filtradas
  let processedTitle = normalizedTitle;

  // Remove padrões de regex primeiro
  FILTER_REGEX_PATTERNS.forEach((pattern) => {
    processedTitle = processedTitle.replace(pattern, "");
  });

  // Remove palavras específicas
  FILTER_WORDS_TO_REMOVE.forEach((word) => {
    processedTitle = processedTitle.replace(
      new RegExp(`\\b${word}\\b`, "gi"),
      ""
    );
  });

  processedTitle = processedTitle.trim();

  // Adiciona cada palavra individual, excluindo as palavras filtradas
  const words = processedTitle.split(/\s+/);
  words.forEach((word) => {
    const normalizedWord = word.toLowerCase();
    if (
      word.length >= 2 &&
      !FILTER_WORDS_TO_REMOVE.includes(normalizedWord) &&
      !FILTER_REGEX_PATTERNS.some((pattern) => pattern.test(normalizedWord))
    ) {
      tokens.add(normalizedWord);
    }
  });

  return Array.from(tokens);
};

// Remover campos undefined de um objeto
const removeUndefinedFields = (obj: any) => {
  const clean: any = {};
  Object.entries(obj).forEach(([key, value]) => {
    if (value !== undefined && value !== null) {
      clean[key] = value;
    }
  });
  return clean;
};

export default defineEventHandler(async (event) => {
  try {
    console.log("🔄 [API] Iniciando busca por figures sem tokens...");

    let totalProcessado = 0;
    let totalAtualizado = 0;
    let totalPulado = 0;

    // Primeiro, buscar todas as figuras para contar o total
    const allSnapshot = await getDocs(collection(db, COLLECTION_NAME));
    const totalFigures = allSnapshot.size;
    console.log("📊 [API] Total de figures no banco:", totalFigures);

    // Buscar apenas figuras que não têm search_tokens ou têm array vazio
    // Infelizmente o Firestore não permite query complexa para "não existe" ou "array vazio"
    // então vamos filtrar no lado do cliente, mas de forma mais eficiente
    const figuresWithoutTokens: Array<{ doc: any, data: any }> = [];

    allSnapshot.docs.forEach(doc => {
      const data = doc.data();
      const needsUpdate =
        data.title && ( // Tem título
          !data.search_tokens || // Não tem tokens
          !Array.isArray(data.search_tokens) || // Tokens não é um array
          data.search_tokens.length === 0 // Array vazio
        );

      if (needsUpdate) {
        figuresWithoutTokens.push({ doc, data });
      }
    });

    console.log(`🎯 [API] Figures que precisam de tokens: ${figuresWithoutTokens.length} de ${totalFigures}`);

    // Se não há nada para atualizar, retornar rapidamente
    if (figuresWithoutTokens.length === 0) {
      return {
        success: true,
        message: "🎉 Todas as figuras já possuem tokens de busca atualizados!",
        stats: {
          total: totalFigures,
          processado: totalFigures,
          atualizado: 0,
          pulado: totalFigures,
        },
      };
    }

    // Processar apenas as que precisam de atualização
    console.log("🚀 [API] Iniciando atualização das figures sem tokens...");

    for (const { doc: document, data } of figuresWithoutTokens) {
      totalProcessado++;

      console.log(
        `[API] [${totalProcessado}/${figuresWithoutTokens.length}] Atualizando: ${data.title}`
      );

      try {
        // Gerar novos tokens
        const newTokens = generateSearchTokens(data.title);
        console.log(`[API] - Tokens gerados:`, newTokens);

        // Atualizar no Firebase
        const update = removeUndefinedFields({ search_tokens: newTokens });
        await updateDoc(doc(db, COLLECTION_NAME, document.id), update);

        totalAtualizado++;
        console.log(`✅ [API] Tokens adicionados para: ${data.title}`);

      } catch (error) {
        console.error(`❌ [API] Erro ao atualizar ${data.title}:`, error);
        totalPulado++;
      }

      // Pequena pausa a cada 10 figuras para não sobrecarregar
      if (totalProcessado % 10 === 0) {
        console.log(`⏸️ [API] Pausa após ${totalProcessado} figuras...`);
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }

    // Figuras que já tinham tokens
    totalPulado += (totalFigures - figuresWithoutTokens.length);

    const mensagem = `
🎉 Verificação concluída!
- Total de figures encontradas: ${totalFigures}
- Total processado: ${totalProcessado}
- Total atualizado: ${totalAtualizado}
- Total pulado: ${totalPulado} (já tinham palavras ou sem título)
    `;

    console.log(mensagem);

    return {
      success: true,
      message: mensagem,
      stats: {
        total: totalFigures,
        processado: totalProcessado,
        atualizado: totalAtualizado,
        pulado: totalPulado,
      },
    };
  } catch (error: any) {
    console.error("❌ [API] Erro ao atualizar figures:", error);

    return {
      success: false,
      message: "Ocorreu um erro ao atualizar as figures.",
      error: error.message || "Erro desconhecido",
    };
  }
});
