import { SitemapStream, streamToPromise } from "sitemap";
import { Readable } from "stream";
import { collection, getDocs } from "firebase/firestore";
import { initializeApp } from "firebase/app";
import { getFirestore } from "firebase/firestore";
import type { Figure } from "~/types/figures";
import type { FormattedCoupon } from "~/types/coupons";
import { formatFigureData } from "~/types/figures";

// Configuração do Firebase
const firebaseConfig = {
  apiKey: "AIzaSyBvRYjBiAmn2mv97Sh8gCBBRawteL7sXu0",
  authDomain: "fidel-figures.firebaseapp.com",
  projectId: "fidel-figures",
  storageBucket: "fidel-figures.appspot.com",
  messagingSenderId: "49286327657",
  appId: "1:49286327657:web:f85b11f90e9c4166bc21f8",
};

// Inicializar Firebase
const app = initializeApp(firebaseConfig);
const db = getFirestore(app);

export async function generateSitemap() {
  console.log("Iniciando geração do sitemap...");

  const sitemap = new SitemapStream({
    hostname: "https://fidelfigures.com.br",
  });

  console.log("Stream do sitemap criado");
  const links = [];

  // URLs estáticas
  links.push({ url: "/", changefreq: "daily", priority: 1 });

  try {
    console.log("Buscando categorias...");
    const categoriesRef = collection(db, "categories");
    const categoriesSnapshot = await getDocs(categoriesRef);
    console.log(`${categoriesSnapshot.size} categorias encontradas`);

    categoriesSnapshot.forEach((doc) => {
      const categorySlug = encodeURIComponent(doc.id.trim().toLowerCase());
      links.push({
        url: `/collection?category=${categorySlug}`,
        changefreq: "weekly",
        priority: 0.8,
        lastmod: new Date().toISOString(),
      });
    });

    console.log("Buscando figuras...");
    const productsRef = collection(db, "products");
    const productsSnapshot = await getDocs(productsRef);
    console.log(`${productsSnapshot.size} figuras encontradas`);

    productsSnapshot.forEach((doc) => {
      const figureData = doc.data();
      const figure = formatFigureData(figureData, doc.id, {
        ignoreCoupons: true,
      });
      links.push({
        url: `/produto/${doc.id}`,
        changefreq: "weekly",
        priority: 0.9,
        lastmod: figure.date_created,
      });
    });

    console.log("Gerando sitemap final...");
    const stream = Readable.from(links);
    const xml = await streamToPromise(stream.pipe(sitemap)).then((data) =>
      data.toString()
    );

    console.log("Sitemap gerado com sucesso!");
    return xml;
  } catch (error) {
    console.error("Erro ao gerar sitemap:", error);
    throw error;
  }
}
