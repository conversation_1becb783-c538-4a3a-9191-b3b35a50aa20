import { collection, getDocs, query as firebaseQuery, where } from "firebase/firestore";
import { db } from "~/server/utils/firebase";
import type { IProductReview } from "~/types/customer";

const USERS_COLLECTION = "users";

/**
 * Rota para buscar todas as avaliações de um produto
 * GET /api/reviews/product/:productId
 */
export default defineEventHandler(async (event) => {
  try {
    // Obter o ID do produto da URL
    const productId = getRouterParam(event, "productId");
    
    if (!productId) {
      throw createError({
        statusCode: 400,
        statusMessage: "ID do produto não fornecido",
      });
    }
    
    console.log(`🔍 [API] Buscando avaliações para produto ${productId}`);
    
    // Buscar todos os usuários que têm avaliações
    const usersRef = collection(db, USERS_COLLECTION);
    const usersSnapshot = await getDocs(usersRef);
    
    const reviews: (IProductReview & { userName: string; userPhoto?: string })[] = [];
    
    // Iterar pelos usuários e buscar avaliações do produto específico
    usersSnapshot.forEach((userDoc) => {
      const userData = userDoc.data();
      
      if (userData.reviews && Array.isArray(userData.reviews)) {
        const userReviews = userData.reviews.filter((review: any) => 
          review.productId === productId
        );
        
        userReviews.forEach((review: any) => {
          reviews.push({
            ...review,
            userName: userData.name || "Usuário Anônimo",
            userPhoto: userData.photoURL || null,
            // Converter datas se necessário
            createdAt: review.createdAt?.toDate ? review.createdAt.toDate() : new Date(review.createdAt),
            updatedAt: review.updatedAt?.toDate ? review.updatedAt.toDate() : new Date(review.updatedAt),
          });
        });
      }
    });
    
    // Ordenar por data de criação (mais recentes primeiro)
    reviews.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
    
    console.log(`✅ [API] Encontradas ${reviews.length} avaliações para produto ${productId}`);
    
    // Calcular estatísticas
    const totalReviews = reviews.length;
    const averageRating = totalReviews > 0 
      ? reviews.reduce((sum, review) => sum + review.rating, 0) / totalReviews 
      : 0;
    
    const ratingDistribution = {
      5: reviews.filter(r => r.rating === 5).length,
      4: reviews.filter(r => r.rating === 4).length,
      3: reviews.filter(r => r.rating === 3).length,
      2: reviews.filter(r => r.rating === 2).length,
      1: reviews.filter(r => r.rating === 1).length,
    };
    
    return {
      success: true,
      data: {
        reviews: reviews.map(review => ({
          ...review,
          createdAt: review.createdAt.toISOString(),
          updatedAt: review.updatedAt.toISOString(),
        })),
        statistics: {
          totalReviews,
          averageRating: Math.round(averageRating * 10) / 10, // Arredondar para 1 casa decimal
          ratingDistribution,
        },
      },
    };
    
  } catch (error: any) {
    console.error("❌ [API] Erro ao buscar avaliações:", error);
    
    // Se for um erro já tratado, repassar
    if (error.statusCode) {
      throw error;
    }
    
    // Erro genérico
    throw createError({
      statusCode: 500,
      statusMessage: "Erro interno do servidor ao buscar avaliações",
    });
  }
});
