<template>
  <div>


    <main class="min-h-screen bg-gray-50">
      <div class="container mx-auto px-4 py-8">
        <!-- Breadcrumb -->
        <nav class="flex mb-6" aria-label="Breadcrumb">
          <ol class="inline-flex items-center space-x-1 md:space-x-3">
            <!-- <PERSON><PERSON><PERSON> de Início -->
            <li class="inline-flex items-center">
              <a href="#" @click.prevent="navigateToCategory('')"
                class="text-sm font-medium text-gray-700 hover:text-red-600">
                <i class="fas fa-home mr-1"></i> Início
              </a>
            </li>

            <!-- Separador após o Início quando há categorias -->
            <li v-if="breadcrumb.length > 0" class="flex items-center">
              <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd"
                  d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                  clip-rule="evenodd" />
              </svg>
            </li>

            <!-- Categorias do breadcrumb -->
            <li v-for="(category, index) in breadcrumb" :key="category.id" class="inline-flex items-center">
              <div v-if="index > 0" class="flex items-center">
                <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd"
                    d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                    clip-rule="evenodd" />
                </svg>
              </div>
              <a href="#" @click.prevent="navigateToCategory(category.id)" :class="[
                'ml-1 text-sm font-medium hover:text-red-600',
                index === breadcrumb.length - 1 ? 'text-red-600' : 'text-gray-700'
              ]">
                {{ category.name }}
              </a>
            </li>
          </ol>
        </nav>


        <!-- Grid de Categorias -->
        <div v-if="isLoading" class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 py-4">
          <!-- Skeletons para categorias -->
          <template v-if="!currentCategoryId">
            <CategorySkeleton v-for="i in 8" :key="i" />
          </template>
          <!-- Skeletons para produtos -->
          <template v-else>
            <!-- Skeletons para subcategorias -->
            <CategorySkeleton v-for="i in 2" :key="'cat-'+i" />
            <!-- Skeletons para produtos -->
            <CategoryProductSkeleton v-for="i in 6" :key="'prod-'+i" />
          </template>
        </div>
        <div v-else-if="error" class="text-red-600 py-4">{{ error }}</div>
        <div v-else class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          <template v-if="!currentCategoryId">
            <div v-for="category in mainCategories" :key="category.id" @click="navigateToCategory(category.id)"
              class="relative group bg-white p-4 rounded-lg border border-gray-200 hover:border-red-500 cursor-pointer transition-all duration-200 shadow-sm hover:shadow-md">
              <div class="flex items-center space-x-4">
                <div class="flex-shrink-0">
                  <svg class="h-10 w-10 text-gray-400 group-hover:text-red-500" xmlns="http://www.w3.org/2000/svg"
                    fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z" />
                  </svg>
                </div>
                <div class="flex-1 min-w-0">
                  <p class="text-sm font-medium text-gray-900 truncate group-hover:text-red-600">
                    {{ category.name }}
                  </p>
                </div>
              </div>
            </div>
          </template>

          <template v-else>
            <!-- Subcategorias -->
            <div v-for="category in subcategories" :key="'cat-' + category.id" @click="navigateToCategory(category.id)"
              class="relative group bg-white p-4 rounded-lg border border-gray-200 hover:border-red-500 cursor-pointer transition-all duration-200 shadow-sm hover:shadow-md">
              <div class="flex items-center space-x-4">
                <div class="flex-shrink-0">
                  <svg class="h-10 w-10 text-gray-400 group-hover:text-red-500" xmlns="http://www.w3.org/2000/svg"
                    fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z" />
                  </svg>
                </div>
                <div class="flex-1 min-w-0">
                  <p class="text-sm font-medium text-gray-900 truncate group-hover:text-red-600">
                    {{ category.name }}
                  </p>
                </div>
              </div>
            </div>

            <!-- Figures -->
            <div v-for="figure in categoryFigures" :key="'fig-' + figure.id"
              class="relative group bg-white rounded-lg border border-gray-200 hover:border-red-500 transition-all duration-200 shadow-sm hover:shadow-md overflow-hidden">
              <NuxtLink :to="'/produto/' + figure.id + '?fromCategory=' + currentCategoryId" class="block">
                <div class="aspect-w-1 aspect-h-1 w-full bg-gray-100">
                  <img v-if="figure.gallery_imgs && figure.gallery_imgs.length > 0" :src="figure.gallery_imgs[0]"
                    :alt="figure.alias" class="w-full h-[100px] object-cover object-center" />
                  <div v-else class="flex items-center justify-center h-full">
                    <svg class="w-12 h-12 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                  </div>
                </div>
                <div class="p-4">
                  <h3 class="text-sm font-medium text-gray-900 truncate group-hover:text-red-600">
                    {{ figure.alias }}
                  </h3>
                </div>
              </NuxtLink>
            </div>
          </template>
        </div>

        <!-- Modal para Nova Categoria -->
        <div v-if="showNewCategoryModal"
          class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center">
          <div class="bg-white rounded-lg p-6 max-w-md w-full">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Nova Categoria</h3>
            <form @submit.prevent="addNewCategory">
              <div class="mb-4">
                <label for="categoryName" class="block text-sm font-medium text-gray-700">Nome</label>
                <input type="text" id="categoryName" v-model="newCategoryName"
                  class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-red-500 focus:ring-red-500" />
              </div>
              <div class="flex justify-end space-x-3">
                <button type="button" @click="showNewCategoryModal = false"
                  class="inline-flex justify-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                  Cancelar
                </button>
                <button type="submit"
                  class="inline-flex justify-center px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                  Criar
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </main>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useCategoriesStore } from '~/stores/categoriesStore';
import { useProductsStore } from '~/stores/productsStore';
import HeaderBar from '~/components/HeaderBar.vue';
import CategorySkeleton from '~/components/CategorySkeleton.vue';
import CategoryProductSkeleton from '~/components/CategoryProductSkeleton.vue';
import { useRoute } from 'vue-router';
import { useAuth } from '~/composables/useAuth';

interface Category {
  id: string;
  name: string;
  fatherId?: string;
}

const route = useRoute();
const { isAuthenticated } = useAuth();
const categoriesStore = useCategoriesStore();
const productsStore = useProductsStore();
const currentCategoryId = ref<string | undefined>(route.query.fromCategory as string | undefined);
const showNewCategoryModal = ref(false);
const newCategoryName = ref('');
const categoryFigures = ref<any[]>([]);
const isLoading = ref(false);
const error = ref<string | null>(null);

// Carregar categoria inicial se existir na URL
onMounted(async () => {
  // Verificar se a store de categorias já foi inicializada
  if (!categoriesStore.isInitialized) {
    console.log('[categorias] Store de categorias não inicializada, carregando categorias...');
    await categoriesStore.fetchAllCategories();
  } else {
    console.log('[categorias] Store de categorias já inicializada, usando cache');
  }

  const categoryFromUrl = route.query.category;
  if (categoryFromUrl && typeof categoryFromUrl === 'string') {
    await navigateToCategory(categoryFromUrl);
  }
});

// Categorias principais (sem pai)
const mainCategories = computed(() => {
  return categoriesStore.getCategories.filter(c => !c.fatherId);
});

// Subcategorias da categoria atual
const subcategories = computed(() => {
  if (!currentCategoryId.value) return [];
  return categoriesStore.getSubcategories(currentCategoryId.value);
});

// Categoria atual
const currentCategory = computed(() => {
  if (!currentCategoryId.value) return null;
  return categoriesStore.getCategoryById(currentCategoryId.value);
});

// Categoria pai da categoria atual
const parentCategory = computed(() => {
  if (!currentCategoryId.value) return null;
  return categoriesStore.getParentCategory(currentCategoryId.value);
});

// Caminho de navegação (breadcrumb)
const breadcrumb = computed(() => {
  const path = [];

  if (currentCategory.value) {
    path.push(currentCategory.value);

    let parent = parentCategory.value;
    while (parent) {
      path.unshift(parent);
      parent = categoriesStore.getParentCategory(parent.id);
    }
  }

  return path;
});

// Navegar para uma categoria
async function navigateToCategory(categoryId: string) {
  currentCategoryId.value = categoryId === '' ? undefined : categoryId;
  error.value = null;

  // Se estiver em uma categoria específica, carrega as figures
  if (currentCategoryId.value) {
    isLoading.value = true;
    try {
      console.log('[categorias] Navegando para categoria:', currentCategoryId.value);

      // Verificar se a store já foi inicializada
      if (!productsStore.isInitialized) {
        console.log('[categorias] Store de produtos não inicializada, carregando produtos...');
        await productsStore.fetchAllProducts();
      } else {
        console.log('[categorias] Store de produtos já inicializada, usando cache');
      }

      // Buscar produtos da categoria usando a store
      categoryFigures.value = productsStore.getProductsByCategory(currentCategoryId.value);
      console.log(`[categorias] Encontrados ${categoryFigures.value.length} produtos na categoria ${currentCategoryId.value}`);
    } catch (err) {
      console.error('[categorias] Erro ao carregar figuras da categoria:', err);
      error.value = 'Erro ao carregar figuras. Por favor, tente novamente mais tarde.';
    } finally {
      isLoading.value = false;
    }
  } else {
    categoryFigures.value = [];
  }
}

// Formatar preço
function formatPrice(price: number) {
  return new Intl.NumberFormat('pt-BR', {
    style: 'currency',
    currency: 'BRL'
  }).format(price);
}

// Abrir modal de nova categoria
function openNewCategoryModal() {
  showNewCategoryModal.value = true;
  newCategoryName.value = '';
}

// Adicionar nova categoria
async function addNewCategory() {
  if (!newCategoryName.value.trim()) return;

  try {
    await categoriesStore.addCategory({
      name: newCategoryName.value.trim(),
      fatherId: currentCategoryId.value
    });

    showNewCategoryModal.value = false;
    newCategoryName.value = '';
  } catch (err) {
    console.error('Erro ao adicionar categoria:', err);
  }
}
</script>