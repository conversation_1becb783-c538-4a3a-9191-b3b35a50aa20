import { doc, getDoc, updateDoc, arrayUnion, increment, Timestamp, collection, setDoc } from "firebase/firestore";
import { db } from "~/server/utils/firebase";
import { v4 as uuidv4 } from "uuid";

const USERS_COLLECTION = "users";

/**
 * Rota para adicionar um pedido ao cliente
 * POST /api/customers/:id/orders
 */
export default defineEventHandler(async (event) => {
  try {
    // Obter o ID do cliente da URL
    const id = getRouterParam(event, "id");

    if (!id) {
      throw createError({
        statusCode: 400,
        statusMessage: "ID do cliente nu00e3o fornecido",
      });
    }

    // Obter os dados da requisiu00e7u00e3o
    const body = await readBody(event);

    console.log(`ud83dudd0d [API] Salvando pedido para cliente ${id}`);
    console.log('Dados do pedido recebidos:', JSON.stringify(body, null, 2));

    // Verificar se o cliente existe
    const customerRef = doc(db, USERS_COLLECTION, id);
    const customerDoc = await getDoc(customerRef);

    if (!customerDoc.exists()) {
      console.log(`ud83dudd0d [API] Cliente com ID ${id} nu00e3o encontrado`);
      throw createError({
        statusCode: 404,
        statusMessage: "Cliente nu00e3o encontrado",
      });
    }

    // Limpar e validar os dados do pedido
    const cleanedBody = {
      paymentId: body.paymentId || '',
      collectionId: body.collectionId || '',
      collectionStatus: body.collectionStatus || 'approved',
      paymentType: body.paymentType || '',
      externalReference: body.externalReference || '',
      merchantOrderId: body.merchantOrderId || '',
      preferenceId: body.preferenceId || '',
      siteId: body.siteId || '',
      processingMode: body.processingMode || '',
      merchantAccountId: body.merchantAccountId || '',
      productId: body.productId || '',
      status: body.status || 'approved',
      shipping: body.shipping || null,
      quantity: body.quantity || 1,
      customerId: id,
      total: typeof body.total === 'number' ? body.total : 0,
      paymentProvider: body.paymentProvider || 'mercadopago',
      stripePaymentIntentId: body.stripePaymentIntentId || null,
      stripeClientSecret: body.stripeClientSecret || null,
      isTest: body.isTest || false,
      trackCode: body.trackCode || null
    };

    // Criar objeto de pedido
    const order = {
      id: body.id || uuidv4(),
      ...cleanedBody,
      // Garantir que as datas sejam Timestamps do Firestore
      createdAt: body.createdAt ? Timestamp.fromDate(new Date(body.createdAt)) : Timestamp.now(),
      updatedAt: body.updatedAt ? Timestamp.fromDate(new Date(body.updatedAt)) : Timestamp.now()
    };

    console.log('Objeto de pedido criado:', JSON.stringify({
      id: order.id,
      status: order.status,
      paymentProvider: order.paymentProvider,
      paymentId: order.paymentId,
      preferenceId: order.preferenceId,
      productId: order.productId,
      total: order.total
    }, null, 2));

    try {
      // Atualizar o cliente com o novo pedido
      console.log('Tentando atualizar o cliente com o novo pedido...');
      // Obter os dados atuais do cliente
      const customerData = customerDoc.data();

      // Preparar o array de pedidos
      const orders = customerData.orders || [];

      // Verificar se já existe um pedido com o mesmo productId
      if (order.productId && order.paymentProvider !== 'admin') {
        const existingOrderIndex = orders.findIndex((existingOrder: any) =>
          existingOrder.productId === order.productId &&
          existingOrder.status === 'approved'
        );

        if (existingOrderIndex >= 0) {
          console.log(`Produto com ID ${order.productId} já existe em pedidos aprovados`);

          // Retornar o pedido existente sem modificar o Firebase
          return {
            success: false,
            message: "Este produto já foi comprado anteriormente",
            order: {
              ...orders[existingOrderIndex],
              createdAt: orders[existingOrderIndex].createdAt.toDate().toISOString(),
              updatedAt: orders[existingOrderIndex].updatedAt.toDate().toISOString()
            }
          };
        }
      } else if (order.paymentProvider === 'admin') {
        console.log('Pedido administrativo: permitindo duplicação de produto');
      }

      // Se não existe um pedido com o mesmo productId, adicionar o novo pedido
      orders.push(order);

      // Preparar o histórico de pedidos
      const orderHistory = customerData.orderHistory || [];
      orderHistory.push(order.id);

      // Calcular o total de compras
      const totalPurchases = (customerData.totalPurchases || 0) + 1;

      // Atualizar o cliente
      await updateDoc(customerRef, {
        orders: orders,
        orderHistory: orderHistory,
        totalPurchases: totalPurchases,
        lastPurchaseDate: Timestamp.now(),
        updatedAt: Timestamp.now()
      });
      console.log('Cliente atualizado com sucesso!');
    } catch (updateError) {
      console.error('Erro ao atualizar cliente:', updateError);
      throw createError({
        statusCode: 500,
        statusMessage: "Erro ao atualizar cliente com o novo pedido",
      });
    }

    // Adicionar o pedido à coleção de pedidos
    try {
      // Criar um documento na coleção 'orders'
      const ordersCollection = collection(db, "orders");
      const orderRef = doc(ordersCollection);

      // Dados para o documento de pedido
      const orderData = {
        ...order,
        userId: id,
        createdAt: order.createdAt,
        updatedAt: order.updatedAt
      };

      // Salvar o documento
      await setDoc(orderRef, orderData);
      console.log(`✅ [API] Pedido ${order.id} adicionado à coleção de pedidos`);
    } catch (orderError) {
      console.error('Erro ao adicionar pedido à coleção orders:', orderError);
      // Não lançamos erro aqui para não interromper o fluxo principal
    }

    console.log(`✅ [API] Pedido salvo com sucesso, ID: ${order.id}`);

    return {
      success: true,
      message: "Pedido salvo com sucesso",
      order: {
        ...order,
        createdAt: order.createdAt.toDate().toISOString(),
        updatedAt: order.updatedAt.toDate().toISOString()
      }
    };
  } catch (error: any) {
    console.error("u274c [API] Erro ao salvar pedido:", error);
    throw createError({
      statusCode: error.statusCode || 500,
      statusMessage: error.statusMessage || "Erro ao salvar pedido",
    });
  }
});
