<template>
  <div class="bg-white rounded-lg shadow-md overflow-hidden animate-pulse">
    <!-- Imagem skeleton -->
    <div class="relative aspect-square bg-gray-200">
      <div class="absolute inset-0 flex items-center justify-center">
        <svg class="w-12 h-12 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
            d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
        </svg>
      </div>
    </div>

    <!-- Conteúdo skeleton -->
    <div class="p-4">
      <!-- T<PERSON><PERSON><PERSON> skeleton -->
      <div class="h-5 bg-gray-200 rounded w-3/4 mb-2"></div>
      
      <!-- Preço skeleton -->
      <div class="h-6 bg-gray-200 rounded w-1/2 mb-3"></div>
      
      <!-- Botão skeleton -->
      <div class="h-10 bg-gray-200 rounded w-full"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
// Componente de skeleton para cards de produtos
</script>
