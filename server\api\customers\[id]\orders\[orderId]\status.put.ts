import { doc, getDoc, updateDoc, Timestamp } from "firebase/firestore";
import { db } from "~/server/utils/firebase";
import type { IOrder, IPendingOrder, IRejectedOrder } from "~/types/customer";

const USERS_COLLECTION = "users";

/**
 * Rota para atualizar o status de um pedido específico
 * PUT /api/customers/:id/orders/:orderId/status
 */
export default defineEventHandler(async (event) => {
  try {
    // Obter ID do cliente e ID do pedido dos parâmetros da rota
    const id = getRouterParam(event, "id");
    const orderId = getRouterParam(event, "orderId");

    if (!id || !orderId) {
      throw createError({
        statusCode: 400,
        statusMessage: 'ID do cliente ou ID do pedido não fornecido',
      });
    }

    // Obter dados da requisição
    const body = await readBody(event);
    
    if (!body.newStatus) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Novo status não fornecido',
      });
    }

    const { newStatus, currentStatus } = body;

    console.log(`🔄 [API] Atualizando status do pedido ${orderId} de "${currentStatus}" para "${newStatus}"`);

    // Verificar se o cliente existe
    const customerRef = doc(db, USERS_COLLECTION, id);
    const customerDoc = await getDoc(customerRef);

    if (!customerDoc.exists()) {
      throw createError({
        statusCode: 404,
        statusMessage: 'Cliente não encontrado',
      });
    }

    // Obter dados do cliente
    const customerData = customerDoc.data();
    
    // Inicializar arrays se não existirem
    if (!customerData.orders) customerData.orders = [];
    if (!customerData.pendingOrders) customerData.pendingOrders = [];
    if (!customerData.rejectedOrders) customerData.rejectedOrders = [];

    let orderFound = false;
    let updatedCustomerData = { ...customerData };

    // Função para encontrar e remover pedido de um array
    const findAndRemoveOrder = (array: any[], orderId: string) => {
      const index = array.findIndex((order: any) => order.id === orderId);
      if (index !== -1) {
        const order = array[index];
        array.splice(index, 1);
        return order;
      }
      return null;
    };

    // Função para criar pedido com novo status
    const createOrderWithNewStatus = (originalOrder: any, newStatus: string): IOrder | IPendingOrder | IRejectedOrder => {
      const baseOrder = {
        id: originalOrder.id,
        createdAt: originalOrder.createdAt,
        updatedAt: Timestamp.now(),
        paymentId: originalOrder.paymentId,
        collectionId: originalOrder.collectionId,
        paymentType: originalOrder.paymentType,
        externalReference: originalOrder.externalReference,
        merchantOrderId: originalOrder.merchantOrderId,
        preferenceId: originalOrder.preferenceId,
        siteId: originalOrder.siteId,
        processingMode: originalOrder.processingMode,
        merchantAccountId: originalOrder.merchantAccountId,
        customerId: originalOrder.customerId || id,
        status: newStatus,
        productId: originalOrder.productId || '',
        total: originalOrder.total,
        shipping: originalOrder.shipping,
        quantity: originalOrder.quantity,
        paymentProvider: originalOrder.paymentProvider,
        isTest: originalOrder.isTest || false,
        trackCode: originalOrder.trackCode || ''
      };

      // Para pedidos aprovados, adicionar campos específicos
      if (newStatus === 'approved' || newStatus === 'em produção' || newStatus === 'em fila de produção' || newStatus === 'enviado' || newStatus === 'concluída') {
        return {
          ...baseOrder,
          collectionStatus: newStatus === 'approved' ? 'approved' : originalOrder.collectionStatus || 'approved'
        } as IOrder;
      }

      return baseOrder;
    };

    // Lógica para mudança de status
    if (newStatus === 'approved') {
      // Aprovar pedido: mover de pendingOrders ou rejectedOrders para orders
      let originalOrder = findAndRemoveOrder(updatedCustomerData.pendingOrders, orderId);
      if (!originalOrder) {
        originalOrder = findAndRemoveOrder(updatedCustomerData.rejectedOrders, orderId);
      }
      
      if (originalOrder) {
        const approvedOrder = createOrderWithNewStatus(originalOrder, 'approved') as IOrder;
        updatedCustomerData.orders.push(approvedOrder);
        orderFound = true;
        console.log(`✅ [API] Pedido ${orderId} movido para orders com status approved`);
      }
    } else if (newStatus === 'rejected') {
      // Rejeitar pedido: mover de pendingOrders ou orders para rejectedOrders
      let originalOrder = findAndRemoveOrder(updatedCustomerData.pendingOrders, orderId);
      if (!originalOrder) {
        originalOrder = findAndRemoveOrder(updatedCustomerData.orders, orderId);
      }
      
      if (originalOrder) {
        const rejectedOrder = createOrderWithNewStatus(originalOrder, 'rejected') as IRejectedOrder;
        rejectedOrder.collectionStatus = 'rejected';
        updatedCustomerData.rejectedOrders.push(rejectedOrder);
        orderFound = true;
        console.log(`✅ [API] Pedido ${orderId} movido para rejectedOrders com status rejected`);
      }
    } else if (newStatus === 'pending') {
      // Voltar para pendente: mover de orders ou rejectedOrders para pendingOrders
      let originalOrder = findAndRemoveOrder(updatedCustomerData.orders, orderId);
      if (!originalOrder) {
        originalOrder = findAndRemoveOrder(updatedCustomerData.rejectedOrders, orderId);
      }
      
      if (originalOrder) {
        const pendingOrder = createOrderWithNewStatus(originalOrder, 'pending') as IPendingOrder;
        updatedCustomerData.pendingOrders.push(pendingOrder);
        orderFound = true;
        console.log(`✅ [API] Pedido ${orderId} movido para pendingOrders com status pending`);
      }
    } else {
      // Para outros status (em produção, em fila, enviado, concluída), apenas atualizar o status sem mover
      // Procurar em orders primeiro
      const orderIndex = updatedCustomerData.orders.findIndex((order: any) => order.id === orderId);
      if (orderIndex !== -1) {
        updatedCustomerData.orders[orderIndex].status = newStatus;
        updatedCustomerData.orders[orderIndex].updatedAt = Timestamp.now();
        orderFound = true;
        console.log(`✅ [API] Status do pedido ${orderId} atualizado em orders para ${newStatus}`);
      } else {
        // Se não encontrou em orders, procurar em pendingOrders
        const pendingIndex = updatedCustomerData.pendingOrders.findIndex((order: any) => order.id === orderId);
        if (pendingIndex !== -1) {
          updatedCustomerData.pendingOrders[pendingIndex].status = newStatus;
          updatedCustomerData.pendingOrders[pendingIndex].updatedAt = Timestamp.now();
          orderFound = true;
          console.log(`✅ [API] Status do pedido ${orderId} atualizado em pendingOrders para ${newStatus}`);
        } else {
          // Se não encontrou em pendingOrders, procurar em rejectedOrders
          const rejectedIndex = updatedCustomerData.rejectedOrders.findIndex((order: any) => order.id === orderId);
          if (rejectedIndex !== -1) {
            updatedCustomerData.rejectedOrders[rejectedIndex].status = newStatus;
            updatedCustomerData.rejectedOrders[rejectedIndex].updatedAt = Timestamp.now();
            orderFound = true;
            console.log(`✅ [API] Status do pedido ${orderId} atualizado em rejectedOrders para ${newStatus}`);
          }
        }
      }
    }

    if (!orderFound) {
      throw createError({
        statusCode: 404,
        statusMessage: 'Pedido não encontrado',
      });
    }

    // Atualizar o cliente no Firestore
    await updateDoc(customerRef, {
      orders: updatedCustomerData.orders,
      pendingOrders: updatedCustomerData.pendingOrders,
      rejectedOrders: updatedCustomerData.rejectedOrders,
      updatedAt: Timestamp.now()
    });

    console.log(`✅ [API] Status do pedido ${orderId} atualizado com sucesso para ${newStatus}`);

    return {
      success: true,
      message: 'Status do pedido atualizado com sucesso',
      orderId: orderId,
      newStatus: newStatus
    };

  } catch (error: any) {
    console.error("❌ [API] Erro ao atualizar status do pedido:", error);
    throw createError({
      statusCode: error.statusCode || 500,
      statusMessage: error.statusMessage || "Erro ao atualizar status do pedido",
    });
  }
});
