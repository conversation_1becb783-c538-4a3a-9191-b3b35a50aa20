/**
 * Tipos e interfaces para o sistema de avaliações otimizado
 * Nova arquitetura com coleção separada para melhor performance
 */

// Constantes para o sistema de avaliações
export const REVIEWS_COLLECTION = 'reviews';
export const REVIEW_MEDIA_PATH = 'reviews';

// Enum para tipos de mídia
export enum ReviewMediaType {
  IMAGE = 'image',
  VIDEO = 'video'
}

// Interface para mídia de avaliação
export interface IReviewMedia {
  id: string;
  reviewId: string;
  type: ReviewMediaType;
  url: string;
  filename: string;
  size: number;
  uploadedAt: Date;
}

// Interface principal para avaliações (nova arquitetura)
export interface IReview {
  id: string;
  userId: string;
  productId: string; // ÍNDICE PRINCIPAL
  orderId: string;
  rating: number; // 1-5 estrelas
  comment: string;
  images?: string[]; // URLs das imagens
  videos?: string[]; // URLs dos vídeos
  createdAt: Date;
  updatedAt: Date;
  isVerifiedPurchase: boolean;
  
  // Dados desnormalizados para performance
  userName: string;
  userPhoto?: string;
  productTitle: string;
  productImage?: string;
  
  // Metadados adicionais
  helpfulCount?: number; // Quantas pessoas acharam útil
  reportCount?: number; // Quantas vezes foi reportada
  isHidden?: boolean; // Se foi ocultada por moderação
  moderatedAt?: Date;
  moderatedBy?: string;
}

// Interface para estatísticas de avaliações
export interface IReviewStatistics {
  totalReviews: number;
  averageRating: number;
  ratingDistribution: {
    1: number;
    2: number;
    3: number;
    4: number;
    5: number;
  };
  verifiedPurchaseCount: number;
  withMediaCount: number;
}

// Interface para filtros de busca de avaliações
export interface IReviewFilters {
  productId?: string;
  userId?: string;
  rating?: number;
  minRating?: number;
  maxRating?: number;
  hasMedia?: boolean;
  isVerifiedPurchase?: boolean;
  isHidden?: boolean;
  dateFrom?: Date;
  dateTo?: Date;
  limit?: number;
  offset?: number;
  sortBy?: 'newest' | 'oldest' | 'rating_high' | 'rating_low' | 'helpful';
}

// Interface para resposta da API de avaliações
export interface IReviewsApiResponse {
  success: boolean;
  data: {
    reviews: IReview[];
    statistics: IReviewStatistics;
    pagination?: {
      total: number;
      page: number;
      limit: number;
      hasNext: boolean;
      hasPrev: boolean;
    };
  };
  message?: string;
}

// Interface para criação de nova avaliação
export interface ICreateReviewRequest {
  userId: string;
  productId: string;
  orderId: string;
  rating: number;
  comment: string;
  images?: string[];
  videos?: string[];
}

// Interface para atualização de avaliação
export interface IUpdateReviewRequest {
  rating?: number;
  comment?: string;
  images?: string[];
  videos?: string[];
}

// Interface para dados de produto necessários para avaliação
export interface IReviewProductData {
  id: string;
  title: string;
  mainImage?: string;
}

// Interface para dados de usuário necessários para avaliação
export interface IReviewUserData {
  uid: string;
  name: string;
  photoURL?: string;
}

// Interface para validação de avaliação
export interface IReviewValidation {
  isValid: boolean;
  errors: {
    rating?: string;
    comment?: string;
    userId?: string;
    productId?: string;
    orderId?: string;
  };
}

// Tipos para ordenação
export type ReviewSortOption = 'newest' | 'oldest' | 'rating_high' | 'rating_low' | 'helpful';

// Tipos para status de moderação
export type ReviewModerationStatus = 'pending' | 'approved' | 'rejected' | 'hidden';

// Interface para ações de moderação
export interface IReviewModerationAction {
  reviewId: string;
  action: ReviewModerationStatus;
  reason?: string;
  moderatorId: string;
  moderatedAt: Date;
}

// Interface para relatório de avaliação
export interface IReviewReport {
  id: string;
  reviewId: string;
  reportedBy: string;
  reason: string;
  description?: string;
  createdAt: Date;
  status: 'pending' | 'resolved' | 'dismissed';
  resolvedBy?: string;
  resolvedAt?: Date;
}

// Interface para agregação de avaliações por produto
export interface IProductReviewAggregate {
  productId: string;
  totalReviews: number;
  averageRating: number;
  ratingDistribution: Record<number, number>;
  lastReviewDate: Date;
  updatedAt: Date;
}

// Constantes de validação
export const REVIEW_VALIDATION = {
  MIN_RATING: 1,
  MAX_RATING: 5,
  MIN_COMMENT_LENGTH: 10,
  MAX_COMMENT_LENGTH: 1000,
  MAX_IMAGES: 8,
  MAX_VIDEOS: 3,
  MAX_IMAGE_SIZE: 10 * 1024 * 1024, // 10MB
  MAX_VIDEO_SIZE: 50 * 1024 * 1024, // 50MB
} as const;

// Tipos de erro para o sistema de avaliações
export enum ReviewErrorType {
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  USER_NOT_FOUND = 'USER_NOT_FOUND',
  PRODUCT_NOT_FOUND = 'PRODUCT_NOT_FOUND',
  ORDER_NOT_FOUND = 'ORDER_NOT_FOUND',
  REVIEW_NOT_FOUND = 'REVIEW_NOT_FOUND',
  DUPLICATE_REVIEW = 'DUPLICATE_REVIEW',
  UNAUTHORIZED = 'UNAUTHORIZED',
  MEDIA_UPLOAD_ERROR = 'MEDIA_UPLOAD_ERROR',
  DATABASE_ERROR = 'DATABASE_ERROR',
}

// Interface para erros do sistema de avaliações
export interface IReviewError {
  type: ReviewErrorType;
  message: string;
  details?: any;
  statusCode?: number;
}
