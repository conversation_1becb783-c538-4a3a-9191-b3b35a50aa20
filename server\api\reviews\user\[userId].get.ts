import { doc, getDoc } from "firebase/firestore";
import { db } from "~/server/utils/firebase";
import type { IProductReview } from "~/types/customer";

const USERS_COLLECTION = "users";

/**
 * Rota para buscar todas as avaliações de um usuário
 * GET /api/reviews/user/:userId
 */
export default defineEventHandler(async (event) => {
  try {
    // Obter o ID do usuário da URL
    const userId = getRouterParam(event, "userId");
    
    if (!userId) {
      throw createError({
        statusCode: 400,
        statusMessage: "ID do usuário não fornecido",
      });
    }
    
    console.log(`🔍 [API] Buscando avaliações do usuário ${userId}`);
    
    // Verificar se o usuário existe
    const userRef = doc(db, USERS_COLLECTION, userId);
    const userDoc = await getDoc(userRef);
    
    if (!userDoc.exists()) {
      console.log(`❌ [API] Usuário com ID ${userId} não encontrado`);
      throw createError({
        statusCode: 404,
        statusMessage: "Usuário não encontrado",
      });
    }
    
    const userData = userDoc.data();
    const reviews = userData.reviews || [];
    
    // Ordenar por data de criação (mais recentes primeiro)
    const sortedReviews = reviews
      .map((review: any) => ({
        ...review,
        // Converter datas se necessário
        createdAt: review.createdAt?.toDate ? review.createdAt.toDate() : new Date(review.createdAt),
        updatedAt: review.updatedAt?.toDate ? review.updatedAt.toDate() : new Date(review.updatedAt),
      }))
      .sort((a: any, b: any) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
    
    console.log(`✅ [API] Encontradas ${sortedReviews.length} avaliações do usuário ${userId}`);
    
    return {
      success: true,
      data: {
        reviews: sortedReviews.map((review: any) => ({
          ...review,
          createdAt: review.createdAt.toISOString(),
          updatedAt: review.updatedAt.toISOString(),
        })),
        totalReviews: sortedReviews.length,
      },
    };
    
  } catch (error: any) {
    console.error("❌ [API] Erro ao buscar avaliações do usuário:", error);
    
    // Se for um erro já tratado, repassar
    if (error.statusCode) {
      throw error;
    }
    
    // Erro genérico
    throw createError({
      statusCode: 500,
      statusMessage: "Erro interno do servidor ao buscar avaliações do usuário",
    });
  }
});
