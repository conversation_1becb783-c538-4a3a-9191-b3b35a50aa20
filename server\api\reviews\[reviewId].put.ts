import { doc, getDoc, updateDoc, Timestamp } from "firebase/firestore";
import { db } from "~/server/utils/firebase";
import type { IProductReview } from "~/types/customer";

const USERS_COLLECTION = "users";

/**
 * Rota para atualizar uma avaliação existente
 * PUT /api/reviews/:reviewId
 */
export default defineEventHandler(async (event) => {
  try {
    // Obter o ID da avaliação da URL
    const reviewId = getRouterParam(event, "reviewId");
    
    if (!reviewId) {
      throw createError({
        statusCode: 400,
        statusMessage: "ID da avaliação não fornecido",
      });
    }
    
    // Obter os dados da requisição
    const body = await readBody(event);
    const { userId, rating, comment, images, videos } = body;
    
    // Validações básicas
    if (!userId) {
      throw createError({
        statusCode: 400,
        statusMessage: "ID do usuário é obrigatório",
      });
    }
    
    if (rating && (rating < 1 || rating > 5)) {
      throw createError({
        statusCode: 400,
        statusMessage: "Avaliação deve ser entre 1 e 5 estrelas",
      });
    }
    
    if (comment !== undefined && comment.trim().length === 0) {
      throw createError({
        statusCode: 400,
        statusMessage: "Comentário não pode estar vazio",
      });
    }
    
    console.log(`🔍 [API] Atualizando avaliação ${reviewId} do usuário ${userId}`);
    
    // Verificar se o usuário existe
    const userRef = doc(db, USERS_COLLECTION, userId);
    const userDoc = await getDoc(userRef);
    
    if (!userDoc.exists()) {
      console.log(`❌ [API] Usuário com ID ${userId} não encontrado`);
      throw createError({
        statusCode: 404,
        statusMessage: "Usuário não encontrado",
      });
    }
    
    const userData = userDoc.data();
    const reviews = userData.reviews || [];
    
    // Encontrar a avaliação
    const reviewIndex = reviews.findIndex((review: any) => review.id === reviewId);
    
    if (reviewIndex === -1) {
      console.log(`❌ [API] Avaliação ${reviewId} não encontrada`);
      throw createError({
        statusCode: 404,
        statusMessage: "Avaliação não encontrada",
      });
    }
    
    // Verificar se a avaliação pertence ao usuário
    if (reviews[reviewIndex].userId !== userId) {
      console.log(`❌ [API] Usuário ${userId} não tem permissão para editar avaliação ${reviewId}`);
      throw createError({
        statusCode: 403,
        statusMessage: "Você só pode editar suas próprias avaliações",
      });
    }
    
    // Atualizar os campos fornecidos
    const updatedReview = {
      ...reviews[reviewIndex],
      updatedAt: new Date(),
    };
    
    if (rating !== undefined) {
      updatedReview.rating = Number(rating);
    }
    
    if (comment !== undefined) {
      updatedReview.comment = comment.trim();
    }
    
    if (images !== undefined) {
      updatedReview.images = Array.isArray(images) ? images : [];
    }
    
    if (videos !== undefined) {
      updatedReview.videos = Array.isArray(videos) ? videos : [];
    }
    
    // Atualizar o array de avaliações
    const updatedReviews = [...reviews];
    updatedReviews[reviewIndex] = updatedReview;
    
    // Salvar no Firebase
    await updateDoc(userRef, {
      reviews: updatedReviews,
      updatedAt: Timestamp.now(),
    });
    
    console.log(`✅ [API] Avaliação ${reviewId} atualizada com sucesso`);
    
    return {
      success: true,
      message: "Avaliação atualizada com sucesso",
      review: {
        ...updatedReview,
        createdAt: updatedReview.createdAt.toISOString ? updatedReview.createdAt.toISOString() : updatedReview.createdAt,
        updatedAt: updatedReview.updatedAt.toISOString(),
      },
    };
    
  } catch (error: any) {
    console.error("❌ [API] Erro ao atualizar avaliação:", error);
    
    // Se for um erro já tratado, repassar
    if (error.statusCode) {
      throw error;
    }
    
    // Erro genérico
    throw createError({
      statusCode: 500,
      statusMessage: "Erro interno do servidor ao atualizar avaliação",
    });
  }
});
