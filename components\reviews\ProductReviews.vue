<template>
  <div class="space-y-6">
    <!-- Cabeçalho com estatísticas -->
    <div class="bg-white rounded-lg border border-gray-200 p-6">
      <div class="flex items-center justify-between mb-4">
        <h2 class="text-xl font-semibold text-gray-900">
          Avaliações dos Clientes
        </h2>
        
        <div v-if="statistics" class="text-right">
          <div class="flex items-center space-x-2">
            <div class="flex items-center">
              <i
                v-for="star in 5"
                :key="star"
                class="fas fa-star text-lg"
                :class="star <= Math.round(statistics.averageRating) ? 'text-yellow-400' : 'text-gray-300'"
              ></i>
            </div>
            <span class="text-lg font-medium text-gray-900">
              {{ statistics.averageRating.toFixed(1) }}
            </span>
          </div>
          <p class="text-sm text-gray-600">
            {{ statistics.totalReviews }} avaliação{{ statistics.totalReviews !== 1 ? 'ões' : '' }}
          </p>
        </div>
      </div>

      <!-- Distribuição de estrelas -->
      <div v-if="statistics && statistics.totalReviews > 0" class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div class="space-y-2">
          <div
            v-for="rating in [5, 4, 3, 2, 1]"
            :key="rating"
            class="flex items-center space-x-3"
          >
            <span class="text-sm font-medium text-gray-700 w-8">{{ rating }}★</span>
            <div class="flex-1 bg-gray-200 rounded-full h-2">
              <div
                class="bg-yellow-400 h-2 rounded-full transition-all duration-300"
                :style="{ width: `${(statistics.ratingDistribution[rating] / statistics.totalReviews) * 100}%` }"
              ></div>
            </div>
            <span class="text-sm text-gray-600 w-8">{{ statistics.ratingDistribution[rating] }}</span>
          </div>
        </div>

        <div class="space-y-2 text-sm text-gray-600">
          <div class="flex justify-between">
            <span>Compras verificadas:</span>
            <span class="font-medium">{{ statistics.verifiedPurchaseCount }}</span>
          </div>
          <div class="flex justify-between">
            <span>Com fotos/vídeos:</span>
            <span class="font-medium">{{ statistics.withMediaCount }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Filtros -->
    <div class="bg-white rounded-lg border border-gray-200 p-4">
      <div class="flex flex-wrap items-center gap-4">
        <div class="flex items-center space-x-2">
          <label class="text-sm font-medium text-gray-700">Filtrar por:</label>
          <select
            v-model="selectedRating"
            @change="applyFilters"
            class="border border-gray-300 rounded-md px-3 py-1 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="">Todas as estrelas</option>
            <option value="5">5 estrelas</option>
            <option value="4">4 estrelas</option>
            <option value="3">3 estrelas</option>
            <option value="2">2 estrelas</option>
            <option value="1">1 estrela</option>
          </select>
        </div>

        <div class="flex items-center space-x-2">
          <label class="text-sm font-medium text-gray-700">Ordenar por:</label>
          <select
            v-model="sortBy"
            @change="applyFilters"
            class="border border-gray-300 rounded-md px-3 py-1 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="newest">Mais recentes</option>
            <option value="oldest">Mais antigas</option>
            <option value="rating_high">Maior avaliação</option>
            <option value="rating_low">Menor avaliação</option>
            <option value="helpful">Mais úteis</option>
          </select>
        </div>

        <label class="flex items-center space-x-2 text-sm">
          <input
            v-model="showOnlyWithMedia"
            @change="applyFilters"
            type="checkbox"
            class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
          />
          <span class="text-gray-700">Apenas com fotos/vídeos</span>
        </label>

        <label class="flex items-center space-x-2 text-sm">
          <input
            v-model="showOnlyVerified"
            @change="applyFilters"
            type="checkbox"
            class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
          />
          <span class="text-gray-700">Apenas compras verificadas</span>
        </label>
      </div>
    </div>

    <!-- Loading -->
    <div v-if="loading" class="flex justify-center py-8">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
    </div>

    <!-- Lista de avaliações -->
    <div v-else-if="reviews.length > 0" class="space-y-4">
      <ReviewCard
        v-for="review in reviews"
        :key="review.id"
        :review="review"
        :current-user-id="currentUserId"
        @edit="handleEditReview"
        @helpful="handleHelpfulReview"
      />
      <!-- @delete removido - apenas admins podem deletar -->

      <!-- Paginação -->
      <div v-if="pagination && pagination.total > pagination.limit" class="flex justify-center mt-6">
        <div class="flex items-center space-x-2">
          <button
            v-if="pagination.hasPrev"
            @click="loadPreviousPage"
            class="px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
          >
            Anterior
          </button>
          
          <span class="px-3 py-2 text-sm text-gray-700">
            Página {{ pagination.page }} de {{ Math.ceil(pagination.total / pagination.limit) }}
          </span>
          
          <button
            v-if="pagination.hasNext"
            @click="loadNextPage"
            class="px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
          >
            Próxima
          </button>
        </div>
      </div>
    </div>

    <!-- Estado vazio -->
    <div v-else class="text-center py-12">
      <i class="fas fa-star text-4xl text-gray-300 mb-4"></i>
      <h3 class="text-lg font-medium text-gray-900 mb-2">Nenhuma avaliação encontrada</h3>
      <p class="text-gray-600">
        {{ hasFilters ? 'Tente ajustar os filtros para ver mais avaliações.' : 'Seja o primeiro a avaliar este produto!' }}
      </p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import type { IReview, IReviewStatistics, IReviewFilters } from '~/types/reviews'
import { useReviews } from '~/composables/useReviews'
import ReviewCard from './ReviewCard.vue'

interface Props {
  productId: string
  currentUserId?: string
}

const props = defineProps<Props>()

// Composable
const { getProductReviews } = useReviews()

// Estado
const reviews = ref<IReview[]>([])
const statistics = ref<IReviewStatistics | null>(null)
const loading = ref(true)
const pagination = ref<any>(null)

// Filtros
const selectedRating = ref('')
const sortBy = ref<'newest' | 'oldest' | 'rating_high' | 'rating_low' | 'helpful'>('newest')
const showOnlyWithMedia = ref(false)
const showOnlyVerified = ref(false)
const currentPage = ref(1)
const pageSize = ref(10)

// Computed
const hasFilters = computed(() => {
  return selectedRating.value || showOnlyWithMedia.value || showOnlyVerified.value
})

// Métodos
const loadReviews = async () => {
  loading.value = true

  // Debug: verificar productId antes de fazer a requisição
  console.log('🔍 [ProductReviews] loadReviews chamado:', {
    productId: props.productId,
    productIdType: typeof props.productId,
    productIdLength: props.productId?.length
  });

  if (!props.productId) {
    console.error('❌ [ProductReviews] productId está undefined/null');
    loading.value = false;
    return;
  }

  try {
    const filters: Partial<IReviewFilters> = {
      limit: pageSize.value,
      offset: (currentPage.value - 1) * pageSize.value,
      sortBy: sortBy.value,
    }

    if (selectedRating.value) {
      filters.minRating = Number(selectedRating.value)
    }

    if (showOnlyWithMedia.value) {
      filters.hasMedia = true
    }

    if (showOnlyVerified.value) {
      filters.isVerifiedPurchase = true
    }

    const response = await getProductReviews(props.productId, filters)
    
    if (response.success) {
      reviews.value = response.data.reviews
      statistics.value = response.data.statistics
      pagination.value = response.data.pagination
    }
  } catch (error) {
    console.error('Erro ao carregar avaliações:', error)
  } finally {
    loading.value = false
  }
}

const applyFilters = () => {
  currentPage.value = 1
  loadReviews()
}

const loadNextPage = () => {
  if (pagination.value?.hasNext) {
    currentPage.value++
    loadReviews()
  }
}

const loadPreviousPage = () => {
  if (pagination.value?.hasPrev) {
    currentPage.value--
    loadReviews()
  }
}

const handleEditReview = (review: IReview) => {
  // Implementar edição se necessário
  console.log('Editar avaliação:', review.id)
}

// handleDeleteReview removido - apenas admins podem deletar avaliações

const handleHelpfulReview = (data: { reviewId: string, helpful: boolean }) => {
  // Implementar funcionalidade de "útil" se necessário
  console.log('Marcar como útil:', data)
}

// Lifecycle
onMounted(() => {
  // Debug: verificar props recebidas
  console.log('🔍 [ProductReviews] Props recebidas no onMounted:', {
    productId: props.productId,
    currentUserId: props.currentUserId,
    productIdType: typeof props.productId
  });

  loadReviews()
})

// Watchers
watch(() => props.productId, () => {
  currentPage.value = 1
  loadReviews()
})
</script>
