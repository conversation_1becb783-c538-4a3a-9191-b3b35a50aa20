import { defineStore } from 'pinia'
import type { FormattedFigure } from '~/types/figures'
import { formatFigureData } from '~/types/figures'
import type { Section } from '~/types/sections'

// Tempo de expiração do cache em milissegundos (1 hora)
const CACHE_EXPIRATION_TIME = 60 * 60 * 1000

interface ProductsState {
  products: FormattedFigure[];
  loading: boolean;
  error: string | null;
  initialized: boolean;
  lastFetchTime: number | null;
}

export const useProductsStore = defineStore('products', {
  state: (): ProductsState => ({
    products: [],
    loading: false,
    error: null,
    initialized: false,
    lastFetchTime: null
  }),

  persist: true,

  getters: {
    getProducts: (state) => state.products,
    getProductById: (state) => (id: string) => state.products.find(p => p.id === id),
    getProductsByCategory: (state) => (categoryId: string) => {
      return state.products.filter(product => {
        if (!product.categories) return false
        return product.categories.includes(categoryId)
      })
    },
    getExclusiveProducts: (state) => {
      return state.products.filter(product => {
        return product.is_exclusive === true
      })
    },
    getNewReleases: (state) => {
      return state.products.filter(product => {
        return product.release === true
      })
    },
    isLoading: (state) => state.loading,
    getError: (state) => state.error,
    isInitialized: (state) => state.initialized,
    isCacheExpired: (state) => {
      if (!state.initialized || state.products.length === 0 || state.lastFetchTime === null) {
        return true
      }
      return (Date.now() - state.lastFetchTime) >= CACHE_EXPIRATION_TIME
    }
  },

  actions: {
    async fetchAllProducts() {
      // Verificar se o cache está válido
      const isCacheValid = this.initialized &&
                          this.products.length > 0 &&
                          this.lastFetchTime !== null &&
                          (Date.now() - this.lastFetchTime) < CACHE_EXPIRATION_TIME

      if (isCacheValid) {
        console.log(' [API] [productsStore] Produtos já carregados, usando cache')
        return this.products
      }

      // Se o cache expirou, mas temos produtos, informar que estamos atualizando
      if (this.initialized && this.products.length > 0) {
        console.log(` [API] [productsStore] Cache expirado (${Math.round((Date.now() - (this.lastFetchTime || 0)) / 60000)} minutos), atualizando produtos...`)
      }

      this.loading = true
      this.error = null

      try {
        console.log(' [API] [productsStore] Buscando todos os produtos...')

        const response = await fetch('/api/products')

        if (!response.ok) {
          throw new Error(`Erro ao buscar produtos: ${response.status}`)
        }

        const data = await response.json()
        const products: FormattedFigure[] = data.products.map((productData: any) => formatFigureData(productData, productData.id))

        // Atualizar o estado
        this.products = products
        this.initialized = true
        this.lastFetchTime = Date.now()

        console.log(` [API] [productsStore] Carregados ${products.length} produtos`)
        return products
      } catch (err: any) {
        this.error = err.message
        console.error(' [API] [productsStore] Erro ao carregar produtos:', err)
        return []
      } finally {
        this.loading = false
      }
    },

    async fetchProductById(id: string) {
      console.log(` [API] [productsStore] Buscando produto com ID: ${id}`)

      // Verificar se o produto já está no cache
      const cachedProduct = this.getProductById(id)
      if (cachedProduct) {
        console.log(` [API] [productsStore] Produto ${id} encontrado no cache`)
        return cachedProduct
      }

      this.loading = true
      this.error = null

      try {
        const response = await fetch(`/api/products/${id}`)

        if (!response.ok) {
          throw new Error(`Erro ao buscar produto: ${response.status}`)
        }

        const productData = await response.json()
        const product = formatFigureData(productData, id)

        // Adicionar ao cache
        this.products.push(product)

        console.log(` [API] [productsStore] Produto ${id} carregado com sucesso`)
        return product
      } catch (err: any) {
        this.error = err.message
        console.error(` [API] [productsStore] Erro ao carregar produto ${id}:`, err)
        return null
      } finally {
        this.loading = false
      }
    },

    async fetchProductsByCategory(categoryId: string) {
      console.log(` [API] [productsStore] Buscando produtos da categoria: ${categoryId}`)

      // Verificar se já temos produtos desta categoria no cache
      if (this.initialized) {
        const cachedProducts = this.getProductsByCategory(categoryId)
        if (cachedProducts.length > 0) {
          console.log(` [API] [productsStore] Usando cache para categoria ${categoryId}, ${cachedProducts.length} produtos encontrados`)
          return cachedProducts
        }
      }

      // Se não temos produtos no cache, buscar todos os produtos
      return this.fetchAllProducts().then(() => {
        return this.getProductsByCategory(categoryId)
      })
    },

    async fetchExclusiveProducts() {
      console.log(' [API] [productsStore] Buscando produtos exclusivos')

      // Verificar se já temos produtos exclusivos no cache
      if (this.initialized) {
        const cachedProducts = this.getExclusiveProducts
        if (cachedProducts.length > 0) {
          console.log(` [API] [productsStore] Usando cache para produtos exclusivos, ${cachedProducts.length} produtos encontrados`)
          return cachedProducts
        }
      }

      // Se não temos produtos no cache, buscar todos os produtos
      return this.fetchAllProducts().then(() => {
        return this.getExclusiveProducts
      })
    },

    async getFigures({ page = 1, search = '', dragonBall = false } = {}) {
      this.loading = true
      this.error = null

      try {
        console.log(` [API] [productsStore] Buscando figuras: página ${page}, busca: "${search}", dragonBall: ${dragonBall}`)

        // Construir a URL com os parâmetros de consulta
        const params = new URLSearchParams()
        if (page) params.append('page', page.toString())
        if (search) params.append('search', search)
        if (dragonBall) params.append('dragonBall', 'true')

        const queryString = params.toString()
        const url = `/api/products${queryString ? `?${queryString}` : ''}`

        const response = await fetch(url)

        if (!response.ok) {
          throw new Error(`Erro ao buscar figuras: ${response.status}`)
        }

        const data = await response.json()
        const products: FormattedFigure[] = data.products.map((productData: any) => formatFigureData(productData, productData.id))

        // Atualizar o cache com os novos produtos
        if (!this.initialized) {
          this.products = products
          this.initialized = true
        } else {
          // Adicionar apenas produtos que ainda não estão no cache
          const existingIds = new Set(this.products.map(p => p.id))
          const newProducts = products.filter(p => !existingIds.has(p.id))

          if (newProducts.length > 0) {
            this.products = [...this.products, ...newProducts]
          }
        }

        console.log(` [API] [productsStore] Carregados ${products.length} produtos na página ${page}`)
        return {
          products,
          total: data.total || products.length,
          page: data.page || page,
          pages: data.pages || 1
        }
      } catch (err: any) {
        this.error = err.message
        console.error(' [API] [productsStore] Erro ao carregar figuras:', err)
        return {
          products: [],
          total: 0,
          page: page,
          pages: 1
        }
      } finally {
        this.loading = false
      }
    },

    async fetchAllSections() {
      console.log(' [API] [productsStore] Buscando todas as seções')
      this.loading = true
      this.error = null

      try {
        // Chamar a API para buscar todas as seções
        const response = await fetch('/api/sections')

        if (!response.ok) {
          throw new Error(`Erro ao buscar seções: ${response.status}`)
        }

        const data = await response.json()
        const sections: Section[] = data.sections

        console.log(` [API] [productsStore] Carregadas ${sections.length} seções`)
        return sections
      } catch (err: any) {
        this.error = err.message
        console.error(' [API] [productsStore] Erro ao carregar seções:', err)
        return []
      } finally {
        this.loading = false
      }
    },

    async deleteProduct(id: string, deleteImages = false) {
      console.log(` [API] [productsStore] Excluindo produto ${id} (deleteImages: ${deleteImages})`)
      this.loading = true
      this.error = null

      try {
        // Chamar a API para excluir o produto
        const url = `/api/products/${id}?deleteImages=${deleteImages}`
        const response = await fetch(url, {
          method: 'DELETE'
        })

        if (!response.ok) {
          throw new Error(`Erro ao excluir produto: ${response.status}`)
        }

        // Remover do estado local
        this.products = this.products.filter(p => p.id !== id)

        console.log(` [API] [productsStore] Produto ${id} excluído com sucesso`)
        return true
      } catch (err: any) {
        this.error = err.message
        console.error(` [API] [productsStore] Erro ao excluir produto ${id}:`, err)
        throw err
      } finally {
        this.loading = false
      }
    }
  }
})
