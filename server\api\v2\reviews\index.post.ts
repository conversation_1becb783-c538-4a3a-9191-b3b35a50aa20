import { collection, doc, getDoc, addDoc, query, where, getDocs, Timestamp } from "firebase/firestore";
import { db } from "~/server/utils/firebase";
import type { IReview, ICreateReviewRequest, ReviewErrorType } from "~/types/reviews";
import { REVIEW_VALIDATION } from "~/types/reviews";
import { v4 as uuidv4 } from "uuid";

const REVIEWS_COLLECTION = "reviews";
const USERS_COLLECTION = "users";
const PRODUCTS_COLLECTION = "products"; // Nome correto da coleção de produtos

/**
 * Endpoint otimizado para criar avaliações
 * POST /api/v2/reviews
 */
export default defineEventHandler(async (event) => {
  try {
    const body = await readBody(event) as ICreateReviewRequest;
    
    // Validação dos dados de entrada
    const validation = validateReviewData(body);
    if (!validation.isValid) {
      throw createError({
        statusCode: 400,
        statusMessage: "Dados inválidos",
        data: validation.errors,
      });
    }

    const { userId, productId, orderId, rating, comment, images = [], videos = [] } = body;

    console.log(`🔍 [API v2] Criando avaliação:`, {
      userId,
      productId,
      orderId,
      rating,
      commentLength: comment?.length || 0,
      imagesCount: images?.length || 0,
      videosCount: videos?.length || 0
    });

    // Verificar se o usuário existe e obter dados
    const userRef = doc(db, USERS_COLLECTION, userId);
    const userDoc = await getDoc(userRef);
    
    if (!userDoc.exists()) {
      throw createError({
        statusCode: 404,
        statusMessage: "Usuário não encontrado",
      });
    }

    const userData = userDoc.data();

    // Verificar se o produto existe e obter dados
    console.log(`🔍 [API v2] Verificando se produto existe: ${productId}`);
    const productRef = doc(db, PRODUCTS_COLLECTION, productId);
    const productDoc = await getDoc(productRef);

    if (!productDoc.exists()) {
      console.log(`❌ [API v2] Produto não encontrado: ${productId}`);
      throw createError({
        statusCode: 404,
        statusMessage: "Produto não encontrado",
      });
    }

    console.log(`✅ [API v2] Produto encontrado: ${productId}`);

    const productData = productDoc.data();

    // Verificar se já existe uma avaliação deste usuário para este produto
    const existingReviewQuery = query(
      collection(db, REVIEWS_COLLECTION),
      where("userId", "==", userId),
      where("productId", "==", productId)
    );
    
    const existingReviewSnapshot = await getDocs(existingReviewQuery);
    
    if (!existingReviewSnapshot.empty) {
      throw createError({
        statusCode: 409,
        statusMessage: "Você já avaliou este produto",
      });
    }

    // Verificar se o usuário realmente comprou este produto (verificar no pedido)
    const isVerifiedPurchase = await verifyPurchase(userId, productId, orderId);

    // Criar a nova avaliação
    const newReview: Omit<IReview, 'id'> = {
      userId,
      productId,
      orderId,
      rating: Number(rating),
      comment: comment.trim(),
      images: Array.isArray(images) ? images : [],
      videos: Array.isArray(videos) ? videos : [],
      createdAt: new Date(),
      updatedAt: new Date(),
      isVerifiedPurchase,
      
      // Dados desnormalizados para performance
      userName: userData.name || "Usuário Anônimo",
      userPhoto: userData.photoURL || null,
      productTitle: productData.title || productData.alias || "Produto",
      productImage: productData.mainImage || productData.gallery_imgs?.[0] || null,
      
      // Metadados iniciais
      helpfulCount: 0,
      reportCount: 0,
      isHidden: false,
    };

    // Salvar na coleção de avaliações
    const reviewsRef = collection(db, REVIEWS_COLLECTION);
    const docRef = await addDoc(reviewsRef, {
      ...newReview,
      createdAt: Timestamp.fromDate(newReview.createdAt),
      updatedAt: Timestamp.fromDate(newReview.updatedAt),
    });

    const savedReview: IReview = {
      ...newReview,
      id: docRef.id,
    };

    console.log(`✅ [API v2] Avaliação criada com sucesso: ${docRef.id}`);

    return {
      success: true,
      message: "Avaliação criada com sucesso",
      data: {
        review: {
          ...savedReview,
          createdAt: savedReview.createdAt.toISOString(),
          updatedAt: savedReview.updatedAt.toISOString(),
        },
      },
    };

  } catch (error: any) {
    console.error("❌ [API v2] Erro ao criar avaliação:", error);
    
    if (error.statusCode) {
      throw error;
    }
    
    throw createError({
      statusCode: 500,
      statusMessage: "Erro interno do servidor ao criar avaliação",
    });
  }
});

/**
 * Valida os dados da avaliação
 */
function validateReviewData(data: ICreateReviewRequest) {
  const errors: Record<string, string> = {};

  if (!data.userId) {
    errors.userId = "ID do usuário é obrigatório";
  }

  if (!data.productId) {
    errors.productId = "ID do produto é obrigatório";
  }

  if (!data.orderId) {
    errors.orderId = "ID do pedido é obrigatório";
  }

  if (!data.rating || data.rating < REVIEW_VALIDATION.MIN_RATING || data.rating > REVIEW_VALIDATION.MAX_RATING) {
    errors.rating = `Avaliação deve ser entre ${REVIEW_VALIDATION.MIN_RATING} e ${REVIEW_VALIDATION.MAX_RATING} estrelas`;
  }

  if (!data.comment || data.comment.trim().length < REVIEW_VALIDATION.MIN_COMMENT_LENGTH) {
    errors.comment = `Comentário deve ter pelo menos ${REVIEW_VALIDATION.MIN_COMMENT_LENGTH} caracteres`;
  }

  if (data.comment && data.comment.length > REVIEW_VALIDATION.MAX_COMMENT_LENGTH) {
    errors.comment = `Comentário deve ter no máximo ${REVIEW_VALIDATION.MAX_COMMENT_LENGTH} caracteres`;
  }

  if (data.images && data.images.length > REVIEW_VALIDATION.MAX_IMAGES) {
    errors.images = `Máximo de ${REVIEW_VALIDATION.MAX_IMAGES} imagens permitidas`;
  }

  if (data.videos && data.videos.length > REVIEW_VALIDATION.MAX_VIDEOS) {
    errors.videos = `Máximo de ${REVIEW_VALIDATION.MAX_VIDEOS} vídeos permitidos`;
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors,
  };
}

/**
 * Verifica se o usuário realmente comprou o produto
 */
async function verifyPurchase(userId: string, productId: string, orderId: string): Promise<boolean> {
  try {
    // Buscar o usuário e verificar se tem o pedido
    const userRef = doc(db, USERS_COLLECTION, userId);
    const userDoc = await getDoc(userRef);
    
    if (!userDoc.exists()) {
      return false;
    }

    const userData = userDoc.data();
    const orders = userData.orders || [];
    
    // Verificar se existe um pedido com o ID fornecido e o produto correto
    const order = orders.find((order: any) => 
      order.id === orderId && order.productId === productId
    );

    return !!order;
  } catch (error) {
    console.error("Erro ao verificar compra:", error);
    return false;
  }
}
