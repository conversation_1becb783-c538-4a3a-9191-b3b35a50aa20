<template>
  <div>
    <!-- Formulário para preenchimento da declaração -->
    <div class="mb-8">
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- Coluna da esquerda - Dados do remetente -->
        <div class="space-y-4">
          <h3 class="text-lg font-semibold text-gray-700 border-b pb-2">
            Dados do Remetente
          </h3>

          <div class="form-group">
            <label class="block text-sm font-medium text-gray-700 mb-1"
              >Nome</label
            >
            <input
              v-model="remetente.nome"
              type="text"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500"
              placeholder="Nome completo"
            />
          </div>

          <div class="form-group">
            <label class="block text-sm font-medium text-gray-700 mb-1"
              >CPF/CNPJ</label
            >
            <input
              v-model="remetente.documento"
              type="text"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500"
              placeholder="CPF ou CNPJ"
              v-mask="documentoMask"
            />
          </div>

          <div class="form-group">
            <label class="block text-sm font-medium text-gray-700 mb-1"
              >Endereço</label
            >
            <input
              v-model="remetente.endereco"
              type="text"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500"
              placeholder="Rua, número, complemento"
            />
          </div>

          <div class="grid grid-cols-2 gap-4">
            <div class="form-group">
              <label class="block text-sm font-medium text-gray-700 mb-1"
                >Cidade</label
              >
              <input
                v-model="remetente.cidade"
                type="text"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500"
                placeholder="Cidade"
              />
            </div>

            <div class="form-group">
              <label class="block text-sm font-medium text-gray-700 mb-1"
                >Estado</label
              >
              <select
                v-model="remetente.estado"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500"
              >
                <option value="">Selecione</option>
                <option v-for="estado in estados" :key="estado" :value="estado">
                  {{ estado }}
                </option>
              </select>
            </div>
          </div>

          <div class="form-group">
            <label class="block text-sm font-medium text-gray-700 mb-1"
              >CEP</label
            >
            <input
              v-model="remetente.cep"
              type="text"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500"
              placeholder="00000-000"
              v-mask="'#####-###'"
            />
          </div>
        </div>

        <!-- Coluna da direita - Dados do destinatário -->
        <div class="space-y-4">
          <h3 class="text-lg font-semibold text-gray-700 border-b pb-2">
            Dados do Destinatário
          </h3>

          <div class="form-group">
            <label class="block text-sm font-medium text-gray-700 mb-1"
              >Nome</label
            >
            <input
              v-model="destinatario.nome"
              type="text"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500"
              placeholder="Nome completo"
            />
          </div>

          <div class="form-group">
            <label class="block text-sm font-medium text-gray-700 mb-1"
              >CPF/CNPJ</label
            >
            <input
              v-model="destinatario.documento"
              type="text"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500"
              placeholder="CPF ou CNPJ"
              v-mask="documentoMask"
            />
          </div>

          <div class="form-group">
            <label class="block text-sm font-medium text-gray-700 mb-1"
              >Endereço</label
            >
            <input
              v-model="destinatario.endereco"
              type="text"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500"
              placeholder="Rua, número, complemento"
            />
          </div>

          <div class="grid grid-cols-2 gap-4">
            <div class="form-group">
              <label class="block text-sm font-medium text-gray-700 mb-1"
                >Cidade</label
              >
              <input
                v-model="destinatario.cidade"
                type="text"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500"
                placeholder="Cidade"
              />
            </div>

            <div class="form-group">
              <label class="block text-sm font-medium text-gray-700 mb-1"
                >Estado</label
              >
              <select
                v-model="destinatario.estado"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500"
              >
                <option value="">Selecione</option>
                <option v-for="estado in estados" :key="estado" :value="estado">
                  {{ estado }}
                </option>
              </select>
            </div>
          </div>

          <div class="form-group">
            <label class="block text-sm font-medium text-gray-700 mb-1"
              >CEP</label
            >
            <input
              v-model="destinatario.cep"
              type="text"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500"
              placeholder="00000-000"
              v-mask="'#####-###'"
            />
          </div>
        </div>
      </div>

      <!-- Conteúdo da declaração -->
      <div class="mt-8">
        <h3 class="text-lg font-semibold text-gray-700 border-b pb-2 mb-4">
          Conteúdo da Declaração
        </h3>

        <div class="overflow-x-auto">
          <table class="min-w-full bg-white border border-gray-200">
            <thead>
              <tr>
                <th
                  class="px-4 py-2 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  Descrição
                </th>
                <th
                  class="px-4 py-2 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  Quantidade
                </th>
                <th
                  class="px-4 py-2 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  Valor Unitário (R$)
                </th>
                <th
                  class="px-4 py-2 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  Valor Total (R$)
                </th>
                <th class="px-4 py-2 border-b border-gray-200 bg-gray-50"></th>
              </tr>
            </thead>
            <tbody>
              <tr
                v-for="(item, index) in itens"
                :key="index"
                class="border-b border-gray-200"
              >
                <td class="px-4 py-2">
                  <input
                    v-model="item.descricao"
                    type="text"
                    class="w-full px-2 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-red-500"
                  />
                </td>
                <td class="px-4 py-2">
                  <input
                    v-model.number="item.quantidade"
                    type="number"
                    min="1"
                    class="w-full px-2 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-red-500"
                    @input="calcularTotal(index)"
                  />
                </td>
                <td class="px-4 py-2">
                  <input
                    v-model.number="item.valorUnitario"
                    type="number"
                    min="0"
                    step="0.01"
                    class="w-full px-2 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-red-500"
                    @input="calcularTotal(index)"
                  />
                </td>
                <td class="px-4 py-2">
                  <input
                    v-model.number="item.valorTotal"
                    type="number"
                    readonly
                    class="w-full px-2 py-1 bg-gray-100 border border-gray-300 rounded-md"
                  />
                </td>
                <td class="px-4 py-2">
                  <button
                    @click="removerItem(index)"
                    class="text-red-500 hover:text-red-700"
                    title="Remover item"
                  >
                    <i class="fas fa-trash"></i>
                  </button>
                </td>
              </tr>
            </tbody>
            <tfoot>
              <tr>
                <td colspan="5" class="px-4 py-2">
                  <button
                    @click="adicionarItem"
                    class="text-sm bg-gray-200 hover:bg-gray-300 text-gray-700 font-semibold py-1 px-3 rounded-md flex items-center"
                  >
                    <i class="fas fa-plus mr-1"></i> Adicionar Item
                  </button>
                </td>
              </tr>
              <tr>
                <td colspan="3" class="px-4 py-2 text-right font-semibold">
                  Valor Total da Declaração:
                </td>
                <td class="px-4 py-2">
                  <input
                    v-model="valorTotalDeclaracao"
                    type="text"
                    readonly
                    class="w-full px-2 py-1 bg-gray-100 border border-gray-300 rounded-md font-semibold"
                  />
                </td>
                <td></td>
              </tr>
            </tfoot>
          </table>
        </div>
      </div>

      <!-- Observações -->
      <div class="mt-6">
        <label class="block text-sm font-medium text-gray-700 mb-1"
          >Observações</label
        >
        <textarea
          v-model="observacoes"
          rows="3"
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500"
          placeholder="Observações adicionais (opcional)"
        ></textarea>
      </div>

      <!-- Botões de ação -->
      <div class="mt-8 flex justify-end space-x-4">
        <button
          @click="limparFormulario"
          class="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition-colors"
        >
          Limpar
        </button>
        <button
          @click="preencherDadosExemplo"
          class="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors"
        >
          Preencher Exemplo
        </button>
        <button
          @click="gerarPDF"
          class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
          :disabled="!formularioValido"
        >
          Gerar PDF
        </button>
      </div>
    </div>

    <!-- Área de visualização da declaração (será convertida em PDF) -->
    <div id="declaracao-conteudo" class="hidden">
      <div class="declaracao-pdf">
        <div class="cabecalho">
          <h1>DECLARAÇÃO DE CONTEÚDO</h1>
        </div>

        <div class="dados-envio">
          <div class="remetente">
            <h2>REMETENTE</h2>
            <p><strong>Nome:</strong> {{ remetente.nome }}</p>
            <p><strong>CPF/CNPJ:</strong> {{ remetente.documento }}</p>
            <p><strong>Endereço:</strong> {{ remetente.endereco }}</p>
            <p>
              <strong>Cidade/UF:</strong> {{ remetente.cidade }}/{{
                remetente.estado
              }}
            </p>
            <p><strong>CEP:</strong> {{ remetente.cep }}</p>
          </div>

          <div class="destinatario">
            <h2>DESTINATÁRIO</h2>
            <p><strong>Nome:</strong> {{ destinatario.nome }}</p>
            <p><strong>CPF/CNPJ:</strong> {{ destinatario.documento }}</p>
            <p><strong>Endereço:</strong> {{ destinatario.endereco }}</p>
            <p>
              <strong>Cidade/UF:</strong> {{ destinatario.cidade }}/{{
                destinatario.estado
              }}
            </p>
            <p><strong>CEP:</strong> {{ destinatario.cep }}</p>
          </div>
        </div>

        <div class="conteudo">
          <h2>CONTEÚDO DA REMESSA</h2>
          <table>
            <thead>
              <tr>
                <th>Discriminação do Conteúdo</th>
                <th>Quantidade</th>
                <th>Valor Unitário (R$)</th>
                <th>Valor Total (R$)</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="(item, index) in itens" :key="index">
                <td>{{ item.descricao }}</td>
                <td>{{ item.quantidade }}</td>
                <td>{{ formatarValor(item.valorUnitario) }}</td>
                <td>{{ formatarValor(item.valorTotal) }}</td>
              </tr>
            </tbody>
            <tfoot>
              <tr>
                <td colspan="3" class="text-right">
                  <strong>Valor Total (R$):</strong>
                </td>
                <td>
                  <strong>{{
                    formatarValor(parseFloat(valorTotalDeclaracao))
                  }}</strong>
                </td>
              </tr>
            </tfoot>
          </table>
        </div>

        <div class="observacoes" v-if="observacoes">
          <h2>OBSERVAÇÕES</h2>
          <p>{{ observacoes }}</p>
        </div>

        <div class="declaracao-texto">
          <p>
            Declaro que não estou postando conteúdo perigoso, proibido ou
            sujeito a restrições especiais, conforme legislação vigente. Estou
            ciente de que declaração falsa está sujeita às penalidades da lei.
          </p>
        </div>

        <div class="assinatura">
          <div class="data">
            <p>{{ dataAtual }}</p>
            <p>Data</p>
          </div>

          <div class="linha-assinatura">
            <div class="linha"></div>
            <p>Assinatura do Declarante</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from "vue";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import html2pdf from "html2pdf.js";

// Lista de estados brasileiros
const estados = [
  "AC",
  "AL",
  "AP",
  "AM",
  "BA",
  "CE",
  "DF",
  "ES",
  "GO",
  "MA",
  "MT",
  "MS",
  "MG",
  "PA",
  "PB",
  "PR",
  "PE",
  "PI",
  "RJ",
  "RN",
  "RS",
  "RO",
  "RR",
  "SC",
  "SP",
  "SE",
  "TO",
];

// Dados do remetente
const remetente = ref({
  nome: "Danillo Fidel Gomes",
  documento: "09297644635",
  endereco: "Avenida Estrela do Sul 662, ap 403",
  cidade: "Uberlândia",
  estado: "MG",
  cep: "38400-339",
});

// Dados do destinatário
const destinatario = ref({
  nome: "",
  documento: "",
  endereco: "",
  cidade: "",
  estado: "",
  cep: "",
});

// Itens da declaração
const itens = ref([
  { descricao: "", quantidade: 1, valorUnitario: 0, valorTotal: 0 },
]);

// Observações
const observacoes = ref("");

// Valor total da declaração
const valorTotalDeclaracao = ref("0.00");

// Data atual formatada
const dataAtual = computed(() => {
  return format(new Date(), "dd/MM/yyyy", { locale: ptBR });
});

// Máscara para CPF/CNPJ
const documentoMask = computed(() => {
  return "###.###.###-##";
});

// Verificar se o formulário está válido
const formularioValido = computed(() => {
  // Verificar dados do remetente
  const remetenteValido =
    remetente.value.nome &&
    remetente.value.documento &&
    remetente.value.endereco &&
    remetente.value.cidade &&
    remetente.value.estado &&
    remetente.value.cep;

  // Verificar dados do destinatário
  const destinatarioValido =
    destinatario.value.nome &&
    destinatario.value.documento &&
    destinatario.value.endereco &&
    destinatario.value.cidade &&
    destinatario.value.estado &&
    destinatario.value.cep;

  // Verificar se há pelo menos um item com descrição e valores
  const itensValidos =
    itens.value.length > 0 &&
    itens.value.every((item) => item.descricao && item.quantidade > 0);

  return remetenteValido && destinatarioValido && itensValidos;
});

// Métodos
const calcularTotal = (index) => {
  const item = itens.value[index];
  item.valorTotal = parseFloat(
    (item.quantidade * item.valorUnitario).toFixed(2)
  );

  // Recalcular o valor total da declaração
  valorTotalDeclaracao.value = itens.value
    .reduce((total, item) => total + item.valorTotal, 0)
    .toFixed(2);
};

const adicionarItem = () => {
  itens.value.push({
    descricao: "",
    quantidade: 1,
    valorUnitario: 0,
    valorTotal: 0,
  });
};

const removerItem = (index) => {
  if (itens.value.length > 1) {
    itens.value.splice(index, 1);

    // Recalcular o valor total da declaração
    valorTotalDeclaracao.value = itens.value
      .reduce((total, item) => total + item.valorTotal, 0)
      .toFixed(2);
  }
};

const formatarValor = (valor) => {
  if (valor === undefined || isNaN(valor)) {
    return "0,00";
  }
  return valor.toLocaleString("pt-BR", {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  });
};

const limparFormulario = () => {
  // Limpar dados do remetente
  remetente.value = {
    nome: "",
    documento: "",
    endereco: "",
    cidade: "",
    estado: "",
    cep: "",
  };

  // Limpar dados do destinatário
  destinatario.value = {
    nome: "",
    documento: "",
    endereco: "",
    cidade: "",
    estado: "",
    cep: "",
  };

  // Limpar itens
  itens.value = [
    { descricao: "", quantidade: 1, valorUnitario: 0, valorTotal: 0 },
  ];

  // Limpar observações
  observacoes.value = "";

  // Resetar valor total
  valorTotalDeclaracao.value = "0.00";
};

const preencherDadosExemplo = () => {
  // Preencher dados do remetente
  remetente.value = {
    nome: "Danillo Fidel Gomes",
    documento: "09297644635",
    endereco: "Avenida Estrela do Sul 662, ap 403",
    cidade: "Uberlândia",
    estado: "MG",
    cep: "38400-339",
  };

  // Preencher dados do destinatário
  destinatario.value = {
    nome: "Cliente Exemplo",
    documento: "987.654.321-00",
    endereco: "Av. Cliente, 456",
    cidade: "Rio de Janeiro",
    estado: "RJ",
    cep: "20000-000",
  };

  // Preencher itens
  itens.value = [
    {
      descricao: "Action Figure Colecionável",
      quantidade: 1,
      valorUnitario: 299.9,
      valorTotal: 299.9,
    },
  ];

  // Preencher observações
  observacoes.value = "Produto frágil, manusear com cuidado.";

  // Atualizar valor total
  valorTotalDeclaracao.value = "299.90";
};

const gerarPDF = () => {
  const element = document.getElementById("declaracao-conteudo");

  if (element) {
    console.log("Gerando PDF...");

    const options = {
      margin: 10,
      filename: `declaracao_conteudo_${format(new Date(), "dd-MM-yyyy")}.pdf`,
      image: { type: "jpeg", quality: 0.98 },
      html2canvas: { scale: 2 },
      jsPDF: { unit: "mm", format: "a4", orientation: "portrait" },
    };

    html2pdf().from(element).set(options).save();
    console.log("PDF gerado com sucesso!");
  } else {
    console.error("Elemento não encontrado");
  }
};

// Inicializar dados quando o componente for montado
onMounted(() => {
  console.log("Componente montado, inicializando dados...");
});
</script>

<style scoped>
/* Estilos para o PDF */
.declaracao-pdf {
  font-family: Arial, sans-serif;
  padding: 20px;
  max-width: 210mm; /* Largura A4 */
  margin: 0 auto;
}

.cabecalho {
  text-align: center;
  margin-bottom: 20px;
}

.cabecalho h1 {
  font-size: 18px;
  font-weight: bold;
  text-transform: uppercase;
}

.dados-envio {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
}

.remetente,
.destinatario {
  width: 48%;
  border: 1px solid #000;
  padding: 10px;
}

.remetente h2,
.destinatario h2,
.conteudo h2,
.observacoes h2 {
  font-size: 14px;
  font-weight: bold;
  margin-bottom: 10px;
  text-transform: uppercase;
}

.conteudo {
  margin-bottom: 20px;
}

.conteudo table {
  width: 100%;
  border-collapse: collapse;
}

.conteudo table th,
.conteudo table td {
  border: 1px solid #000;
  padding: 8px;
  text-align: left;
}

.conteudo table th {
  background-color: #f2f2f2;
}

.text-right {
  text-align: right;
}

.observacoes {
  margin-bottom: 20px;
}

.declaracao-texto {
  margin-bottom: 30px;
}

.assinatura {
  display: flex;
  justify-content: space-between;
  margin-top: 50px;
}

.data,
.linha-assinatura {
  width: 45%;
  text-align: center;
}

.linha {
  border-bottom: 1px solid #000;
  margin-bottom: 5px;
}

/* Esconder o elemento que será convertido em PDF */
.hidden {
  position: absolute;
  left: -9999px;
  top: -9999px;
}
</style>
