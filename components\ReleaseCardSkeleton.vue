<template>
  <div class="min-w-[250px] max-w-[250px] flex-shrink-0 snap-start">
    <div class="bg-white rounded-lg shadow-lg overflow-hidden animate-pulse">
      <div class="relative aspect-[4/5] bg-gray-200">
        <div class="absolute inset-0 flex items-center justify-center">
          <svg class="w-12 h-12 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
          </svg>
        </div>
        <div class="absolute bottom-0 left-0 right-0 p-3">
          <div class="h-5 bg-gray-300 rounded w-3/4 mb-2"></div>
          <div class="h-3 bg-gray-300 rounded w-1/2 mb-3"></div>
          <div class="h-8 bg-gray-300 rounded w-full"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// Componente de skeleton para cards de lançamentos
</script>
