import { collection, query, where, orderBy, limit as firestoreLimit, getDocs, startAfter, DocumentSnapshot } from "firebase/firestore";
import { db } from "~/server/utils/firebase";
import type { IReview, IReviewStatistics, IReviewsApiResponse, IReviewFilters } from "~/types/reviews";

const REVIEWS_COLLECTION = "reviews";

/**
 * Endpoint otimizado para buscar avaliações de um produto
 * GET /api/v2/reviews/product/:productId
 * 
 * Query params opcionais:
 * - limit: número máximo de avaliações (default: 20)
 * - offset: número de avaliações para pular (paginação)
 * - sortBy: ordenação (newest, oldest, rating_high, rating_low, helpful)
 * - minRating: filtro por avaliação mínima
 * - hasMedia: filtrar apenas avaliações com mídia
 * - verified: filtrar apenas compras verificadas
 */
export default defineEventHandler(async (event) => {
  try {
    const productId = getRouterParam(event, "productId");
    
    if (!productId) {
      throw createError({
        statusCode: 400,
        statusMessage: "ID do produto é obrigatório",
      });
    }

    // Extrair parâmetros de query
    const queryParams = getQuery(event);
    const filters: IReviewFilters = {
      productId,
      limit: Number(queryParams.limit) || 20,
      offset: Number(queryParams.offset) || 0,
      sortBy: (queryParams.sortBy as any) || 'newest',
      minRating: queryParams.minRating ? Number(queryParams.minRating) : undefined,
      hasMedia: queryParams.hasMedia === 'true',
      isVerifiedPurchase: queryParams.verified === 'true',
    };

    console.log(`🔍 [API v2] Buscando avaliações para produto ${productId} com filtros:`, filters);

    // Construir query otimizada
    const reviewsRef = collection(db, REVIEWS_COLLECTION);
    let reviewQuery = query(
      reviewsRef,
      where("productId", "==", productId),
      where("isHidden", "!=", true) // Excluir avaliações ocultas
    );

    // Aplicar filtros adicionais
    if (filters.minRating) {
      reviewQuery = query(reviewQuery, where("rating", ">=", filters.minRating));
    }

    if (filters.isVerifiedPurchase) {
      reviewQuery = query(reviewQuery, where("isVerifiedPurchase", "==", true));
    }

    // Aplicar ordenação
    switch (filters.sortBy) {
      case 'newest':
        reviewQuery = query(reviewQuery, orderBy("createdAt", "desc"));
        break;
      case 'oldest':
        reviewQuery = query(reviewQuery, orderBy("createdAt", "asc"));
        break;
      case 'rating_high':
        reviewQuery = query(reviewQuery, orderBy("rating", "desc"), orderBy("createdAt", "desc"));
        break;
      case 'rating_low':
        reviewQuery = query(reviewQuery, orderBy("rating", "asc"), orderBy("createdAt", "desc"));
        break;
      case 'helpful':
        reviewQuery = query(reviewQuery, orderBy("helpfulCount", "desc"), orderBy("createdAt", "desc"));
        break;
      default:
        reviewQuery = query(reviewQuery, orderBy("createdAt", "desc"));
    }

    // Aplicar limite
    if (filters.limit) {
      reviewQuery = query(reviewQuery, firestoreLimit(filters.limit + (filters.offset || 0)));
    }

    // Executar query
    const querySnapshot = await getDocs(reviewQuery);
    const allReviews: IReview[] = [];

    querySnapshot.forEach((doc) => {
      const data = doc.data();
      allReviews.push({
        ...data,
        id: doc.id,
        createdAt: data.createdAt?.toDate() || new Date(data.createdAt),
        updatedAt: data.updatedAt?.toDate() || new Date(data.updatedAt),
      } as IReview);
    });

    // Aplicar offset (paginação)
    const reviews = filters.offset ? allReviews.slice(filters.offset) : allReviews;

    // Filtrar por mídia se solicitado
    const filteredReviews = filters.hasMedia 
      ? reviews.filter(review => (review.images && review.images.length > 0) || (review.videos && review.videos.length > 0))
      : reviews;

    console.log(`✅ [API v2] Encontradas ${filteredReviews.length} avaliações para produto ${productId}`);

    // Calcular estatísticas
    const statistics: IReviewStatistics = calculateReviewStatistics(allReviews);

    const response: IReviewsApiResponse = {
      success: true,
      data: {
        reviews: filteredReviews.map(review => ({
          ...review,
          createdAt: review.createdAt.toISOString(),
          updatedAt: review.updatedAt.toISOString(),
        } as any)),
        statistics,
        pagination: {
          total: allReviews.length,
          page: Math.floor((filters.offset || 0) / (filters.limit || 20)) + 1,
          limit: filters.limit || 20,
          hasNext: allReviews.length > (filters.offset || 0) + (filters.limit || 20),
          hasPrev: (filters.offset || 0) > 0,
        },
      },
    };

    return response;

  } catch (error: any) {
    console.error("❌ [API v2] Erro ao buscar avaliações:", error);
    
    if (error.statusCode) {
      throw error;
    }
    
    throw createError({
      statusCode: 500,
      statusMessage: "Erro interno do servidor ao buscar avaliações",
    });
  }
});

/**
 * Calcula estatísticas das avaliações
 */
function calculateReviewStatistics(reviews: IReview[]): IReviewStatistics {
  const totalReviews = reviews.length;
  
  if (totalReviews === 0) {
    return {
      totalReviews: 0,
      averageRating: 0,
      ratingDistribution: { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 },
      verifiedPurchaseCount: 0,
      withMediaCount: 0,
    };
  }

  const averageRating = reviews.reduce((sum, review) => sum + review.rating, 0) / totalReviews;
  
  const ratingDistribution = {
    1: reviews.filter(r => r.rating === 1).length,
    2: reviews.filter(r => r.rating === 2).length,
    3: reviews.filter(r => r.rating === 3).length,
    4: reviews.filter(r => r.rating === 4).length,
    5: reviews.filter(r => r.rating === 5).length,
  };

  const verifiedPurchaseCount = reviews.filter(r => r.isVerifiedPurchase).length;
  const withMediaCount = reviews.filter(r => 
    (r.images && r.images.length > 0) || (r.videos && r.videos.length > 0)
  ).length;

  return {
    totalReviews,
    averageRating: Math.round(averageRating * 10) / 10,
    ratingDistribution,
    verifiedPurchaseCount,
    withMediaCount,
  };
}
