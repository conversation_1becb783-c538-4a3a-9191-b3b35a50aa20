import { collection, query, where, getDocs } from "firebase/firestore";
import { db } from "~/server/utils/firebase";
import type { IReview, IReviewStatistics, IReviewsApiResponse, IReviewFilters } from "~/types/reviews";

const REVIEWS_COLLECTION = "reviews";

/**
 * Endpoint otimizado para buscar avaliações de um produto
 * GET /api/v2/reviews/product/:productId
 * 
 * Query params opcionais:
 * - limit: número máximo de avaliações (default: 20)
 * - offset: número de avaliações para pular (paginação)
 * - sortBy: ordenação (newest, oldest, rating_high, rating_low, helpful)
 * - minRating: filtro por avaliação mínima
 * - hasMedia: filtrar apenas avaliações com mídia
 * - verified: filtrar apenas compras verificadas
 */
export default defineEventHandler(async (event) => {
  try {
    const productId = getRouterParam(event, "productId");
    
    if (!productId) {
      throw createError({
        statusCode: 400,
        statusMessage: "ID do produto é obrigatório",
      });
    }

    // Extrair parâmetros de query
    const queryParams = getQuery(event);
    const filters: IReviewFilters = {
      productId,
      limit: Number(queryParams.limit) || 20,
      offset: Number(queryParams.offset) || 0,
      sortBy: (queryParams.sortBy as any) || 'newest',
      minRating: queryParams.minRating ? Number(queryParams.minRating) : undefined,
      hasMedia: queryParams.hasMedia === 'true',
      isVerifiedPurchase: queryParams.verified === 'true',
    };

    console.log(`🔍 [API v2] Buscando avaliações para produto ${productId} com filtros:`, filters);

    // Query ultra-simplificada - APENAS filtro por productId
    // Sem orderBy para evitar índice composto (productId + createdAt)
    const reviewsRef = collection(db, REVIEWS_COLLECTION);
    const reviewQuery = query(
      reviewsRef,
      where("productId", "==", productId)
      // SEM orderBy - faremos ordenação no código JavaScript
      // SEM outros filtros - faremos filtragem no código JavaScript
      // SEM limit - faremos paginação no código JavaScript
    );

    console.log(`🔍 [API v2] Executando query simples: productId == ${productId}`);

    // Executar query ultra-simples (apenas productId)
    const querySnapshot = await getDocs(reviewQuery);
    let allReviews: IReview[] = [];

    console.log(`🔍 [API v2] Query retornou ${querySnapshot.docs.length} documentos`);

    querySnapshot.forEach((doc) => {
      const data = doc.data();
      allReviews.push({
        ...data,
        id: doc.id,
        createdAt: data.createdAt?.toDate() || new Date(data.createdAt),
        updatedAt: data.updatedAt?.toDate() || new Date(data.updatedAt),
      } as IReview);
    });

    console.log(`🔍 [API v2] Processados ${allReviews.length} reviews antes da filtragem`);

    // Aplicar filtros no código JavaScript (evita índices compostos)

    // 1. Filtrar avaliações ocultas
    allReviews = allReviews.filter(review => !review.isHidden);

    // 2. Filtrar por rating mínimo
    if (filters.minRating) {
      allReviews = allReviews.filter(review => review.rating >= filters.minRating!);
    }

    // 3. Filtrar por compra verificada
    if (filters.isVerifiedPurchase) {
      allReviews = allReviews.filter(review => review.isVerifiedPurchase);
    }

    // 4. Filtrar por mídia
    if (filters.hasMedia) {
      allReviews = allReviews.filter(review =>
        (review.images && review.images.length > 0) ||
        (review.videos && review.videos.length > 0)
      );
    }

    // 5. Aplicar ordenação no código JavaScript (obrigatório agora)
    console.log(`🔍 [API v2] Aplicando ordenação: ${filters.sortBy || 'newest'}`);

    switch (filters.sortBy) {
      case 'oldest':
        allReviews.sort((a, b) => a.createdAt.getTime() - b.createdAt.getTime());
        break;
      case 'rating_high':
        allReviews.sort((a, b) => {
          if (b.rating !== a.rating) return b.rating - a.rating;
          return b.createdAt.getTime() - a.createdAt.getTime();
        });
        break;
      case 'rating_low':
        allReviews.sort((a, b) => {
          if (a.rating !== b.rating) return a.rating - b.rating;
          return b.createdAt.getTime() - a.createdAt.getTime();
        });
        break;
      case 'helpful':
        allReviews.sort((a, b) => {
          const aHelpful = a.helpfulCount || 0;
          const bHelpful = b.helpfulCount || 0;
          if (bHelpful !== aHelpful) return bHelpful - aHelpful;
          return b.createdAt.getTime() - a.createdAt.getTime();
        });
        break;
      case 'newest':
      default:
        // Ordenação padrão por data (mais recente primeiro)
        allReviews.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
        break;
    }

    // 6. Aplicar paginação simples
    const totalReviews = allReviews.length;
    const startIndex = filters.offset || 0;
    const endIndex = startIndex + (filters.limit || 20);
    const paginatedReviews = allReviews.slice(startIndex, endIndex);

    console.log(`✅ [API v2] Encontradas ${paginatedReviews.length} avaliações para produto ${productId} (total: ${totalReviews})`);

    // Calcular estatísticas baseadas em todas as avaliações filtradas (antes da paginação)
    const statistics: IReviewStatistics = calculateReviewStatistics(allReviews);

    const response: IReviewsApiResponse = {
      success: true,
      data: {
        reviews: paginatedReviews.map(review => ({
          ...review,
          createdAt: review.createdAt.toISOString(),
          updatedAt: review.updatedAt.toISOString(),
        } as any)),
        statistics,
        pagination: {
          total: totalReviews,
          page: Math.floor((filters.offset || 0) / (filters.limit || 20)) + 1,
          limit: filters.limit || 20,
          hasNext: endIndex < totalReviews,
          hasPrev: (filters.offset || 0) > 0,
        },
      },
    };

    return response;

  } catch (error: any) {
    console.error("❌ [API v2] Erro ao buscar avaliações:", error);
    
    if (error.statusCode) {
      throw error;
    }
    
    throw createError({
      statusCode: 500,
      statusMessage: "Erro interno do servidor ao buscar avaliações",
    });
  }
});

/**
 * Calcula estatísticas das avaliações
 */
function calculateReviewStatistics(reviews: IReview[]): IReviewStatistics {
  const totalReviews = reviews.length;
  
  if (totalReviews === 0) {
    return {
      totalReviews: 0,
      averageRating: 0,
      ratingDistribution: { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 },
      verifiedPurchaseCount: 0,
      withMediaCount: 0,
    };
  }

  const averageRating = reviews.reduce((sum, review) => sum + review.rating, 0) / totalReviews;
  
  const ratingDistribution = {
    1: reviews.filter(r => r.rating === 1).length,
    2: reviews.filter(r => r.rating === 2).length,
    3: reviews.filter(r => r.rating === 3).length,
    4: reviews.filter(r => r.rating === 4).length,
    5: reviews.filter(r => r.rating === 5).length,
  };

  const verifiedPurchaseCount = reviews.filter(r => r.isVerifiedPurchase).length;
  const withMediaCount = reviews.filter(r => 
    (r.images && r.images.length > 0) || (r.videos && r.videos.length > 0)
  ).length;

  return {
    totalReviews,
    averageRating: Math.round(averageRating * 10) / 10,
    ratingDistribution,
    verifiedPurchaseCount,
    withMediaCount,
  };
}
