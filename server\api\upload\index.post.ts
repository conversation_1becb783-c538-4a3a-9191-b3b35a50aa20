import { defineEventHand<PERSON>, readMultipartFormData, H3Event } from 'h3';
import { ref as storageRef, uploadBytes, getDownloadURL, uploadBytesResumable } from 'firebase/storage';
import { db, storage } from '~/server/utils/firebase';

export default defineEventHandler(async (event: H3Event) => {
  try {
    // Ler os dados do formulário
    const formData = await readMultipartFormData(event);
    if (!formData) {
      return createError({
        statusCode: 400,
        statusMessage: 'Nenhum arquivo enviado'
      });
    }

    // Extrair o arquivo e o caminho
    const fileField = formData.find(field => field.name === 'file');
    const pathField = formData.find(field => field.name === 'path');

    if (!fileField || !fileField.data || !pathField || !pathField.data) {
      return createError({
        statusCode: 400,
        statusMessage: 'Arquivo e/ou caminho não fornecidos'
      });
    }

    const file = fileField;
    const path = Buffer.from(pathField.data).toString('utf-8');

    console.log(`📤 Iniciando upload para ${path}, tamanho: ${file.data.length} bytes, tipo: ${file.type || 'desconhecido'}`);

    // Verificar se é um vídeo
    const isVideo = file.type?.startsWith('video/') || path.includes('/videos/');

    // Usar uploadBytesResumable para arquivos grandes (vídeos)
    const fileRef = storageRef(storage, path);

    if (isVideo) {
      // Para vídeos, usar uploadBytesResumable que suporta arquivos maiores
      const uploadTask = uploadBytesResumable(fileRef, file.data);

      // Aguardar a conclusão do upload
      await new Promise<void>((resolve, reject) => {
        uploadTask.on(
          'state_changed',
          (snapshot) => {
            const progress = (snapshot.bytesTransferred / snapshot.totalBytes) * 100;
            console.log(`Upload em progresso: ${progress.toFixed(2)}%`);
          },
          (error) => {
            console.error('Erro durante o upload:', error);
            reject(error);
          },
          () => {
            console.log('Upload concluído com sucesso!');
            resolve();
          }
        );
      });
    } else {
      // Para outros arquivos, usar o método padrão
      await uploadBytes(fileRef, file.data);
    }

    // Obter URL de download
    const downloadURL = await getDownloadURL(fileRef);
    console.log(`✅ Upload concluído. URL: ${downloadURL}`);

    return {
      url: downloadURL
    };
  } catch (error: any) {
    console.error('Erro ao fazer upload do arquivo:', error);
    throw createError({
      statusCode: error.statusCode || 500,
      statusMessage: error.statusMessage || 'Erro ao fazer upload do arquivo'
    });
  }
});
