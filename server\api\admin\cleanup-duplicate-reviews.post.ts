import { collection, getDocs, doc, deleteDoc, query, orderBy } from "firebase/firestore";
import { db } from "~/server/utils/firebase";

const REVIEWS_COLLECTION = "reviews";

/**
 * Endpoint para limpar avaliações duplicadas
 * POST /api/admin/cleanup-duplicate-reviews
 * 
 * Remove avaliações duplicadas mantendo a mais recente para cada usuário+produto
 */
export default defineEventHandler(async (event) => {
  try {
    const body = await readBody(event);
    const { adminKey, dryRun = true } = body;
    
    // Chave de segurança
    if (adminKey !== "CLEANUP_REVIEWS_2024") {
      throw createError({
        statusCode: 403,
        statusMessage: "Chave de admin inválida",
      });
    }

    console.log(`🧹 [Cleanup] Iniciando limpeza de duplicatas (dry-run: ${dryRun})`);

    // Buscar todas as avaliações ordenadas por data de criação
    const reviewsRef = collection(db, REVIEWS_COLLECTION);
    const reviewsQuery = query(reviewsRef, orderBy("createdAt", "desc"));
    const reviewsSnapshot = await getDocs(reviewsQuery);

    const reviewMap = new Map<string, any[]>();
    const duplicates: any[] = [];
    let totalReviews = 0;

    // Agrupar avaliações por usuário+produto
    reviewsSnapshot.forEach((doc) => {
      totalReviews++;
      const data = doc.data();
      const key = `${data.userId}-${data.productId}`;
      
      if (!reviewMap.has(key)) {
        reviewMap.set(key, []);
      }
      
      reviewMap.get(key)!.push({
        id: doc.id,
        ...data,
        createdAt: data.createdAt?.toDate() || new Date(data.createdAt),
      });
    });

    // Identificar duplicatas
    reviewMap.forEach((reviews, key) => {
      if (reviews.length > 1) {
        // Ordenar por data de criação (mais recente primeiro)
        reviews.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
        
        // Manter o primeiro (mais recente), marcar os outros como duplicatas
        const toKeep = reviews[0];
        const toDelete = reviews.slice(1);
        
        duplicates.push({
          key,
          userId: toKeep.userId,
          productId: toKeep.productId,
          productTitle: toKeep.productTitle,
          toKeep: {
            id: toKeep.id,
            rating: toKeep.rating,
            createdAt: toKeep.createdAt.toISOString(),
          },
          toDelete: toDelete.map(review => ({
            id: review.id,
            rating: review.rating,
            createdAt: review.createdAt.toISOString(),
          })),
          duplicateCount: toDelete.length,
        });
      }
    });

    let deletedCount = 0;
    const deletionLog: string[] = [];

    // Executar limpeza se não for dry-run
    if (!dryRun && duplicates.length > 0) {
      for (const duplicate of duplicates) {
        for (const reviewToDelete of duplicate.toDelete) {
          try {
            const reviewRef = doc(db, REVIEWS_COLLECTION, reviewToDelete.id);
            await deleteDoc(reviewRef);
            deletedCount++;
            deletionLog.push(`Deletada: ${reviewToDelete.id} (${duplicate.productTitle})`);
            console.log(`🗑️ [Cleanup] Avaliação duplicada deletada: ${reviewToDelete.id}`);
          } catch (error: any) {
            const errorMsg = `Erro ao deletar ${reviewToDelete.id}: ${error.message}`;
            deletionLog.push(errorMsg);
            console.error(`❌ [Cleanup] ${errorMsg}`);
          }
        }
      }
    }

    const summary = {
      dryRun,
      totalReviews,
      uniqueUserProductPairs: reviewMap.size,
      duplicateGroups: duplicates.length,
      totalDuplicateReviews: duplicates.reduce((sum, dup) => sum + dup.duplicateCount, 0),
      deletedCount,
      duplicates: dryRun ? duplicates.slice(0, 10) : duplicates, // Limitar em dry-run
      deletionLog: deletionLog.slice(0, 20), // Primeiros 20 logs
    };

    console.log(`🎉 [Cleanup] Limpeza concluída:`, {
      duplicateGroups: summary.duplicateGroups,
      totalDuplicates: summary.totalDuplicateReviews,
      deleted: summary.deletedCount,
    });

    return {
      success: true,
      message: dryRun 
        ? "Simulação de limpeza concluída" 
        : `Limpeza concluída: ${deletedCount} avaliações duplicadas removidas`,
      data: summary,
    };

  } catch (error: any) {
    console.error("❌ [Cleanup] Erro durante limpeza:", error);
    
    if (error.statusCode) {
      throw error;
    }
    
    throw createError({
      statusCode: 500,
      statusMessage: "Erro interno durante limpeza de duplicatas",
    });
  }
});

/**
 * Exemplo de uso:
 * 
 * 1. Primeiro, verificar duplicatas:
 * POST /api/admin/cleanup-duplicate-reviews
 * {
 *   "adminKey": "CLEANUP_REVIEWS_2024",
 *   "dryRun": true
 * }
 * 
 * 2. Executar limpeza:
 * POST /api/admin/cleanup-duplicate-reviews
 * {
 *   "adminKey": "CLEANUP_REVIEWS_2024",
 *   "dryRun": false
 * }
 */
