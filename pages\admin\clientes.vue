<template>
  <div class="min-h-screen bg-gray-100">
    <HeaderAdmin />
    <main class="container mx-auto px-4 py-8">
      <div class="bg-white shadow-md rounded-lg p-6">
        <h1 class="text-2xl font-bold mb-6 text-gray-800">Gerenciamento de Clientes</h1>

        <!-- Filtros e busca -->
        <div class="mb-6 flex flex-wrap gap-4">
          <div class="relative flex-grow max-w-md">
            <input
              v-model="searchQuery"
              type="text"
              placeholder="Buscar por nome, email ou CPF"
              class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              @input="filterClients"
            />
            <i class="fas fa-search absolute right-3 top-3 text-gray-400"></i>
          </div>

          <div>
            <select
              v-model="sortOption"
              class="px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              @change="sortClients"
            >
              <option value="name_asc">Nome (A-Z)</option>
              <option value="name_desc">Nome (Z-A)</option>
              <option value="date_asc">Data de cadastro (Antiga-Nova)</option>
              <option value="date_desc">Data de cadastro (Nova-Antiga)</option>
            </select>
          </div>
        </div>

        <!-- Tabela de clientes -->
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Nome</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">CPF</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Telefone</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Data de cadastro</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Pedidos</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ações</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <template v-if="loading">
                <ClientRowSkeleton v-for="i in 5" :key="i" />
              </template>
              <tr v-else-if="filteredClients.length === 0" class="text-center">
                <td colspan="7" class="px-6 py-4 text-gray-500">Nenhum cliente encontrado</td>
              </tr>
              <tr v-for="client in filteredClients" :key="client.id || client.uid" class="hover:bg-gray-50">
                <td class="px-6 py-4 whitespace-nowrap">{{ client.name }}</td>
                <td class="px-6 py-4 whitespace-nowrap">{{ client.email }}</td>
                <td class="px-6 py-4 whitespace-nowrap">{{ formatCPF(client.cpf) }}</td>
                <td class="px-6 py-4 whitespace-nowrap">{{ formatPhone(client.phone) }}</td>
                <td class="px-6 py-4 whitespace-nowrap">{{ formatDate(client.createdAt) }}</td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                    {{ getOrderCount(client) }}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <button
                    @click="viewClientDetails(client.id || client.uid)"
                    class="text-blue-600 hover:text-blue-900 mr-3"
                  >
                    <i class="fas fa-eye"></i>
                  </button>
                  <button
                    @click="viewClientOrders(client.id || client.uid)"
                    class="text-green-600 hover:text-green-900"
                  >
                    <i class="fas fa-shopping-cart"></i>
                  </button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- Informação de total -->
        <div class="mt-6 text-sm text-gray-500">
          Mostrando {{ filteredClients.length }} de {{ totalClients }} clientes
        </div>
      </div>
    </main>

    <!-- Modal de detalhes do cliente -->
    <ClienteDetailModal
      :show="showClientModal"
      :cliente-id="selectedClientId"
      @close="showClientModal = false"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useCustomerStore } from '~/stores/customerStore'
import { useProductsStore } from '~/stores/productsStore'
import { Customer } from '~/types/customer'
import ClienteDetailModal from '~/components/admin/ClienteDetailModal.vue'
import ClientRowSkeleton from '~/components/admin/ClientRowSkeleton.vue'

// Middleware para proteger a rota
definePageMeta({
  middleware: ['admin']
})

// Estado
const customerStore = useCustomerStore()
const productsStore = useProductsStore()
const filteredClients = ref<Customer[]>([])
const searchQuery = ref('')
const sortOption = ref('name_asc')
const selectedClientId = ref<string | null>(null)
const showClientModal = ref(false)

// Computed
const loading = computed(() => customerStore.loading)
const totalClients = computed(() => customerStore.customers.length)

// Métodos
async function fetchClients() {
  try {
    // Usar a store para buscar os clientes
    await customerStore.fetchAllCustomers()

    // Inicializar a lista filtrada
    filteredClients.value = [...customerStore.customers]

    // Aplicar ordenação inicial
    sortClients()

    // Carregar produtos para exibição de detalhes
    if (!productsStore.initialized) {
      await productsStore.fetchAllProducts()
    }
  } catch (error) {
    console.error('Erro ao buscar clientes:', error)
  }
}

function filterClients() {
  const query = searchQuery.value.toLowerCase()
  if (!query) {
    filteredClients.value = [...customerStore.customers]
  } else {
    filteredClients.value = customerStore.customers.filter(client =>
      client.name?.toLowerCase().includes(query) ||
      client.email?.toLowerCase().includes(query) ||
      client.cpf?.includes(query)
    )
  }
  sortClients()
}

function sortClients() {
  const option = sortOption.value

  filteredClients.value = [...filteredClients.value].sort((a, b) => {
    if (option === 'name_asc') {
      return (a.name || '').localeCompare(b.name || '')
    } else if (option === 'name_desc') {
      return (b.name || '').localeCompare(a.name || '')
    } else if (option === 'date_asc') {
      return (new Date(a.createdAt).getTime()) - (new Date(b.createdAt).getTime())
    } else if (option === 'date_desc') {
      return (new Date(b.createdAt).getTime()) - (new Date(a.createdAt).getTime())
    }
    return 0
  })
}

function getOrderCount(client: Customer): number {
  // Contar pedidos do cliente (aprovados)
  return client.orders?.length || 0
}

function viewClientDetails(clientId: string) {
  selectedClientId.value = clientId
  showClientModal.value = true
}

function viewClientOrders(clientId: string) {
  navigateTo(`/admin/pedidos?clientId=${clientId}`)
}

// Formatadores
function formatCPF(cpf: string | undefined): string {
  if (!cpf) return ''
  return cpf.replace(/^(\d{3})(\d{3})(\d{3})(\d{2})$/, '$1.$2.$3-$4')
}

function formatPhone(phone: string | undefined): string {
  if (!phone) return ''
  return phone.replace(/^(\d{2})(\d{5})(\d{4})$/, '($1) $2-$3')
}

function formatDate(timestamp: any): string {
  if (!timestamp) return ''

  // Verificar se é um timestamp do Firestore
  if (timestamp.toDate && typeof timestamp.toDate === 'function') {
    return new Intl.DateTimeFormat('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(timestamp.toDate())
  }

  // Se for uma data normal ou string
  const date = timestamp instanceof Date ? timestamp : new Date(timestamp)
  return new Intl.DateTimeFormat('pt-BR', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  }).format(date)
}

// Lifecycle
onMounted(() => {
  fetchClients()
})
</script>
