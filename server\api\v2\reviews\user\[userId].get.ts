import { collection, query, where, orderBy, getDocs } from "firebase/firestore";
import { db } from "~/server/utils/firebase";
import type { IReview, IReviewsApiResponse } from "~/types/reviews";

const REVIEWS_COLLECTION = "reviews";

/**
 * Endpoint otimizado para buscar avaliações de um usuário
 * GET /api/v2/reviews/user/:userId
 */
export default defineEventHandler(async (event) => {
  try {
    const userId = getRouterParam(event, "userId");
    
    if (!userId) {
      throw createError({
        statusCode: 400,
        statusMessage: "ID do usuário é obrigatório",
      });
    }

    console.log(`🔍 [API v2] Buscando avaliações do usuário ${userId}`);

    // Query otimizada para buscar avaliações do usuário
    const reviewsRef = collection(db, REVIEWS_COLLECTION);
    const userReviewsQuery = query(
      reviewsRef,
      where("userId", "==", userId),
      orderBy("createdAt", "desc")
    );

    const querySnapshot = await getDocs(userReviewsQuery);
    const reviews: IReview[] = [];

    querySnapshot.forEach((doc) => {
      const data = doc.data();
      reviews.push({
        ...data,
        id: doc.id,
        createdAt: data.createdAt?.toDate() || new Date(data.createdAt),
        updatedAt: data.updatedAt?.toDate() || new Date(data.updatedAt),
      } as IReview);
    });

    console.log(`✅ [API v2] Encontradas ${reviews.length} avaliações do usuário ${userId}`);

    const response: IReviewsApiResponse = {
      success: true,
      data: {
        reviews: reviews.map(review => ({
          ...review,
          createdAt: review.createdAt.toISOString(),
          updatedAt: review.updatedAt.toISOString(),
        } as any)),
        statistics: {
          totalReviews: reviews.length,
          averageRating: reviews.length > 0 
            ? Math.round((reviews.reduce((sum, r) => sum + r.rating, 0) / reviews.length) * 10) / 10
            : 0,
          ratingDistribution: {
            1: reviews.filter(r => r.rating === 1).length,
            2: reviews.filter(r => r.rating === 2).length,
            3: reviews.filter(r => r.rating === 3).length,
            4: reviews.filter(r => r.rating === 4).length,
            5: reviews.filter(r => r.rating === 5).length,
          },
          verifiedPurchaseCount: reviews.filter(r => r.isVerifiedPurchase).length,
          withMediaCount: reviews.filter(r => 
            (r.images && r.images.length > 0) || (r.videos && r.videos.length > 0)
          ).length,
        },
      },
    };

    return response;

  } catch (error: any) {
    console.error("❌ [API v2] Erro ao buscar avaliações do usuário:", error);
    
    if (error.statusCode) {
      throw error;
    }
    
    throw createError({
      statusCode: 500,
      statusMessage: "Erro interno do servidor ao buscar avaliações do usuário",
    });
  }
});
