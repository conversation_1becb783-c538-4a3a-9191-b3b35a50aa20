import { defineStore } from 'pinia'
import type { FormattedFigure } from '~/types/figures'

// Tempo de expiração do cache em milissegundos (15 minutos)
const CACHE_EXPIRATION_TIME = 15 * 60 * 1000

interface PreviouslyProducedState {
  products: FormattedFigure[];
  loading: boolean;
  error: string | null;
  initialized: boolean;
  lastFetchTime: number | null;
}

export const usePreviouslyProducedStore = defineStore('previouslyProduced', {
  state: (): PreviouslyProducedState => ({
    products: [],
    loading: false,
    error: null,
    initialized: false,
    lastFetchTime: null
  }),

  persist: true,

  getters: {
    getProducts: (state) => state.products,
    isLoading: (state) => state.loading,
    getError: (state) => state.error,
    isInitialized: (state) => state.initialized,
    isCacheExpired: (state) => {
      if (!state.initialized || state.products.length === 0 || state.lastFetchTime === null) {
        return true
      }
      return (Date.now() - state.lastFetchTime) >= CACHE_EXPIRATION_TIME
    }
  },

  actions: {
    async fetchPreviouslyProducedProducts() {
      // Verificar se o cache está válido
      const isCacheValid = this.initialized &&
                          this.products.length > 0 &&
                          this.lastFetchTime !== null &&
                          (Date.now() - this.lastFetchTime) < CACHE_EXPIRATION_TIME;

      if (isCacheValid) {
        console.log(' [API] [previouslyProducedStore] Produtos já produzidos carregados, usando cache')
        return this.products
      }

      // Se o cache expirou, mas temos produtos, informar que estamos atualizando
      if (this.initialized && this.products.length > 0) {
        console.log(` [API] [previouslyProducedStore] Cache expirado (${Math.round((Date.now() - (this.lastFetchTime || 0)) / 60000)} minutos), atualizando produtos...`)
      }

      this.loading = true
      this.error = null

      try {
        const response = await fetch('/api/products/previously-produced')

        if (!response.ok) {
          throw new Error(`Erro ao buscar produtos já produzidos: ${response.status}`)
        }

        const data = await response.json()
        this.products = data || []
        this.initialized = true
        this.lastFetchTime = Date.now()

        console.log(` [API] [previouslyProducedStore] Carregados ${this.products.length} produtos já produzidos`)
        return this.products
      } catch (error: any) {
        console.error(' [API] [previouslyProducedStore] Erro:', error)
        this.error = error.message || 'Erro ao buscar produtos já produzidos'
        return []
      } finally {
        this.loading = false
      }
    }
  }
})
