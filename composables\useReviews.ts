import type { 
  IReview, 
  IReviewsApiResponse, 
  ICreateReviewRequest, 
  IUpdateReviewRequest,
  IReviewFilters 
} from "~/types/reviews";

/**
 * Composable para gerenciar avaliações com a nova API otimizada
 */
export const useReviews = () => {
  
  /**
   * Busca avaliações de um produto
   */
  const getProductReviews = async (
    productId: string, 
    filters?: Partial<IReviewFilters>
  ): Promise<IReviewsApiResponse> => {
    try {
      const queryParams = new URLSearchParams();
      
      if (filters?.limit) queryParams.append('limit', filters.limit.toString());
      if (filters?.offset) queryParams.append('offset', filters.offset.toString());
      if (filters?.sortBy) queryParams.append('sortBy', filters.sortBy);
      if (filters?.minRating) queryParams.append('minRating', filters.minRating.toString());
      if (filters?.hasMedia) queryParams.append('hasMedia', 'true');
      if (filters?.isVerifiedPurchase) queryParams.append('verified', 'true');

      const url = `/api/v2/reviews/product/${productId}${queryParams.toString() ? '?' + queryParams.toString() : ''}`;
      
      const response = await $fetch<IReviewsApiResponse>(url);
      return response;
    } catch (error) {
      console.error('Erro ao buscar avaliações do produto:', error);
      throw error;
    }
  };

  /**
   * Busca avaliações de um usuário
   */
  const getUserReviews = async (userId: string): Promise<IReviewsApiResponse> => {
    try {
      const response = await $fetch<IReviewsApiResponse>(`/api/v2/reviews/user/${userId}`);
      return response;
    } catch (error) {
      console.error('Erro ao buscar avaliações do usuário:', error);
      throw error;
    }
  };

  /**
   * Cria uma nova avaliação
   */
  const createReview = async (reviewData: ICreateReviewRequest): Promise<IReview> => {
    try {
      const response = await $fetch<{ success: boolean; data: { review: IReview } }>('/api/v2/reviews', {
        method: 'POST',
        body: reviewData,
      });
      
      if (response.success) {
        return response.data.review;
      }
      
      throw new Error('Falha ao criar avaliação');
    } catch (error) {
      console.error('Erro ao criar avaliação:', error);
      throw error;
    }
  };

  /**
   * Atualiza uma avaliação existente
   */
  const updateReview = async (
    reviewId: string, 
    updateData: IUpdateReviewRequest & { userId: string }
  ): Promise<IReview> => {
    try {
      const response = await $fetch<{ success: boolean; data: { review: IReview } }>(`/api/v2/reviews/${reviewId}`, {
        method: 'PUT',
        body: updateData,
      });
      
      if (response.success) {
        return response.data.review;
      }
      
      throw new Error('Falha ao atualizar avaliação');
    } catch (error) {
      console.error('Erro ao atualizar avaliação:', error);
      throw error;
    }
  };

  /**
   * Deleta uma avaliação
   */
  const deleteReview = async (reviewId: string, userId: string): Promise<void> => {
    try {
      const response = await $fetch<{ success: boolean }>(`/api/v2/reviews/${reviewId}`, {
        method: 'DELETE',
        body: { userId },
      });
      
      if (!response.success) {
        throw new Error('Falha ao deletar avaliação');
      }
    } catch (error) {
      console.error('Erro ao deletar avaliação:', error);
      throw error;
    }
  };

  /**
   * Verifica se um usuário já avaliou um produto
   */
  const hasUserReviewedProduct = async (userId: string, productId: string): Promise<boolean> => {
    try {
      const userReviews = await getUserReviews(userId);
      return userReviews.data.reviews.some(review => review.productId === productId);
    } catch (error) {
      console.error('Erro ao verificar se usuário já avaliou produto:', error);
      return false;
    }
  };

  /**
   * Busca uma avaliação específica de um usuário para um produto
   */
  const getUserProductReview = async (userId: string, productId: string): Promise<IReview | null> => {
    try {
      const userReviews = await getUserReviews(userId);
      const review = userReviews.data.reviews.find(review => review.productId === productId);
      return review || null;
    } catch (error) {
      console.error('Erro ao buscar avaliação específica:', error);
      return null;
    }
  };

  /**
   * Calcula estatísticas resumidas de avaliações
   */
  const calculateReviewSummary = (reviews: IReview[]) => {
    if (reviews.length === 0) {
      return {
        totalReviews: 0,
        averageRating: 0,
        ratingDistribution: { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 },
        hasReviews: false,
      };
    }

    const totalReviews = reviews.length;
    const averageRating = reviews.reduce((sum, review) => sum + review.rating, 0) / totalReviews;
    
    const ratingDistribution = {
      1: reviews.filter(r => r.rating === 1).length,
      2: reviews.filter(r => r.rating === 2).length,
      3: reviews.filter(r => r.rating === 3).length,
      4: reviews.filter(r => r.rating === 4).length,
      5: reviews.filter(r => r.rating === 5).length,
    };

    return {
      totalReviews,
      averageRating: Math.round(averageRating * 10) / 10,
      ratingDistribution,
      hasReviews: true,
    };
  };

  /**
   * Formata a data de uma avaliação
   */
  const formatReviewDate = (date: string | Date): string => {
    const d = new Date(date);
    return d.toLocaleDateString('pt-BR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  /**
   * Calcula tempo relativo desde a avaliação
   */
  const getTimeAgo = (date: string | Date): string => {
    const now = new Date();
    const reviewDate = new Date(date);
    const diffInMs = now.getTime() - reviewDate.getTime();
    const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));

    if (diffInDays === 0) {
      return 'Hoje';
    } else if (diffInDays === 1) {
      return 'Ontem';
    } else if (diffInDays < 7) {
      return `${diffInDays} dias atrás`;
    } else if (diffInDays < 30) {
      const weeks = Math.floor(diffInDays / 7);
      return `${weeks} semana${weeks > 1 ? 's' : ''} atrás`;
    } else if (diffInDays < 365) {
      const months = Math.floor(diffInDays / 30);
      return `${months} mês${months > 1 ? 'es' : ''} atrás`;
    } else {
      const years = Math.floor(diffInDays / 365);
      return `${years} ano${years > 1 ? 's' : ''} atrás`;
    }
  };

  return {
    // API methods
    getProductReviews,
    getUserReviews,
    createReview,
    updateReview,
    deleteReview,
    hasUserReviewedProduct,
    getUserProductReview,
    
    // Utility methods
    calculateReviewSummary,
    formatReviewDate,
    getTimeAgo,
  };
};
