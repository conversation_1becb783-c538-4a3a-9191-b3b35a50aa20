<template>
  <div class="relative bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden animate-pulse">
    <div class="aspect-w-1 aspect-h-1 w-full bg-gray-200">
      <div class="flex items-center justify-center h-[100px]">
        <svg class="w-12 h-12 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
            d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
        </svg>
      </div>
    </div>
    <div class="p-4">
      <div class="h-4 bg-gray-200 rounded w-3/4"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
// Componente de skeleton para produtos nas categorias
</script>
