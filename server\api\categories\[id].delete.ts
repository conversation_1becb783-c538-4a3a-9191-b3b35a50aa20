import { createError } from "h3";
import { doc, getDoc, deleteDoc, collection, query, where, getDocs, limit } from "firebase/firestore";
import { db } from "~/server/utils/firebase";
import { invalidateCategoriesCache } from "./index.get";

const CATEGORIES_COLLECTION = "categories";

/**
 * Rota para excluir uma categoria
 * DELETE /api/categories/:id
 */
export default defineEventHandler(async (event) => {
  try {
    // Obter o ID da categoria da URL
    const id = getRouterParam(event, "id");

    if (!id) {
      throw createError({
        statusCode: 400,
        statusMessage: "ID da categoria não fornecido",
      });
    }

    console.log(`🔍 [API] Excluindo categoria com ID: ${id}`);

    // Verificar se a categoria existe
    const categoryRef = doc(db, CATEGORIES_COLLECTION, id);
    const categoryDoc = await getDoc(categoryRef);

    if (!categoryDoc.exists()) {
      console.log(`🔍 [API] Categoria com ID ${id} não encontrada`);
      throw createError({
        statusCode: 404,
        statusMessage: "Categoria não encontrada",
      });
    }

    // Verificar se existem subcategorias
    const subcategoriesRef = collection(db, CATEGORIES_COLLECTION);
    const subcategoriesQuery = query(subcategoriesRef, where("fatherId", "==", id));
    const subcategoriesSnapshot = await getDocs(subcategoriesQuery);

    if (!subcategoriesSnapshot.empty) {
      console.log(`🔍 [API] Categoria ${id} possui subcategorias e não pode ser excluída`);
      throw createError({
        statusCode: 400,
        statusMessage: "Não é possível excluir uma categoria que possui subcategorias",
      });
    }

    // Verificar se existem produtos associados a esta categoria
    const productsRef = collection(db, "products");
    const productsQuery = query(
      productsRef,
      where("categories", "array-contains", id),
      limit(1)
    );
    const productsSnapshot = await getDocs(productsQuery);

    if (!productsSnapshot.empty) {
      console.log(`🔍 [API] Categoria ${id} possui produtos associados e não pode ser excluída`);
      throw createError({
        statusCode: 400,
        statusMessage: "Não é possível excluir uma categoria que possui produtos associados",
      });
    }

    // Excluir a categoria
    await deleteDoc(categoryRef);

    console.log(`u2705 [API] Categoria ${id} excluída com sucesso`);
    // Invalidar o cache de categorias para forçar recarregamento
    invalidateCategoriesCache();

    return { success: true, message: "Categoria excluída com sucesso" };
  } catch (error: any) {
    console.error("u274c [API] Erro ao excluir categoria:", error);
    throw createError({
      statusCode: error.statusCode || 500,
      statusMessage: error.statusMessage || "Erro ao excluir categoria",
    });
  }
});
