import type { Figure } from '~/types/figures'

export function useStructuredData() {
  const productStructuredData = (figure: Figure | null) => {
    if (!figure) return null;

    return {
      '@context': 'https://schema.org/',
      '@type': 'Product',
      name: figure.title,
      description: figure.description,
      image: figure.gallery_imgs[0],
      offers: {
        '@type': 'Offer',
        priceCurrency: 'BRL',
        price: figure.finalPrice.toFixed(2),
        availability: !figure.in_stock
          ? 'https://schema.org/PreOrder'
          : 'https://schema.org/InStock'
      }
    }
  }

  return {
    productStructuredData
  }
}
