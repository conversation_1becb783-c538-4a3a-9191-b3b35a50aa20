<template>
  <header class="fixed top-0 left-0 right-0 z-50 transition-all duration-300" :class="[
    isClientHeader
      ? 'bg-white shadow-sm'
      : shouldBeTransparent && !isHovered && !isScrolled
        ? 'bg-transparent'
        : 'bg-white',
    !isClientHeader && isScrolled ? 'shadow-md backdrop-blur-sm' : '',
  ]" @mouseenter="isHovered = true" @mouseleave="isHovered = false">
    <!-- Header principal - fixo -->
    <nav class="container mx-auto px-4 h-16 flex items-center justify-between">
      <!-- Logo -->
      <NuxtLink :to="'/'" class="flex items-center space-x-2">
        <img src="/images/fidel_figures.png" alt="Fidel Figures Logo"
          class="h-8 sm:h-10 lg:h-12 w-auto transition-transform duration-300 hover:scale-105" :class="[
            !isClientHeader && shouldBeTransparent && !isHovered && !isScrolled
              ? 'brightness-0 invert'
              : '',
          ]" />
      </NuxtLink>


      <div class="hidden md:flex items-center space-x-8">
        <a v-for="link in navLinks" :key="link.to" :href="link.to" :target="link.blank ? '_blank' : ''"
          class="transition-colors duration-300" :class="[
            shouldBeTransparent && !isHovered && !isScrolled
              ? 'text-white hover:text-gray-200'
              : 'text-gray-900 hover:text-red-600',
            isActiveRoute(link.to) ? 'font-bold border-b-2 border-red-600' : ''
          ]">
          {{ link.text }}
        </a>
        <a href="https://www.instagram.com/fidel_figures" rel="noopener noreferrer" target="_blank"
          class="transition-colors duration-300 text-xl"
          :class="[shouldBeTransparent && !isHovered && !isScrolled ? 'text-white hover:text-gray-200' : 'text-gray-900 hover:text-red-600']"
          aria-label="Siga-nos no Instagram">
          <i class="fab fa-instagram"></i>
        </a>
        <NuxtLink v-if="isAdmin" to="/admin/figures" class="transition-colors duration-300" :class="[
          shouldBeTransparent && !isHovered && !isScrolled
            ? 'text-white hover:text-gray-200'
            : 'text-gray-900 hover:text-red-600',
          isActiveRoute('/admin/figures') ? 'font-bold border-b-2 border-red-600' : ''
        ]">
          Gerenciar Figures
        </NuxtLink>
      </div>



      <!-- Navegação do cliente -->
      <nav class="hidden md:flex items-center space-x-8">
        <button v-for="item in menuItems" :key="item.id" @click="$emit('change-section', item.id)"
          class="flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors duration-300" :class="{
            'bg-red-50 text-red-600': currentSection === item.id,
            'text-gray-600 hover:bg-red-50 hover:text-red-600': currentSection !== item.id
          }">
          <i :class="item.icon" class="text-lg"></i>
          <span>{{ item.text }}</span>
        </button>
      </nav>


      <div class="flex items-center space-x-4">

        <!-- Busca -->
        <button class="p-2 rounded-full transition-colors duration-300" :class="[
          shouldBeTransparent && !isHovered && !isScrolled
            ? 'text-white hover:text-gray-200'
            : 'text-gray-900 hover:text-red-600',
        ]" @click="openSearch">
          <i class="fas fa-search text-xl"></i>
        </button>


        <!-- Menu do usuário se estiver logado -->
        <div v-if="isAuthenticated" class="relative">
          <UserMenu :transparent-mode="shouldBeTransparent" :is-hovered="isHovered" :is-scrolled="isScrolled"
            :show-avatar="false" @menu-toggled="userMenuOpen = $event" />
        </div>

        <!-- Login/Registro se não estiver logado -->
        <NuxtLink v-else to="/login" class="transition-colors duration-300" :class="[
          shouldBeTransparent && !isHovered && !isScrolled
            ? 'text-white hover:text-gray-200'
            : 'text-gray-900 hover:text-red-600',
        ]">
          <i class="fas fa-user-circle text-xl"></i>
        </NuxtLink>





        <!-- Menu mobile -->
        <button class="md:hidden p-2 rounded-full transition-colors duration-300" :class="[
          !isClientHeader && shouldBeTransparent && !isHovered && !isScrolled
            ? 'text-white hover:text-gray-200'
            : 'text-gray-900 hover:text-red-600',
        ]" @click="isMobileMenuOpen = !isMobileMenuOpen">
          <i class="fas fa-bars text-xl"></i>
        </button>
      </div>
    </nav>

    <!-- Menu mobile -->
    <div v-if="isMobileMenuOpen" class="md:hidden py-4 border-t bg-white">
      <nav class="space-y-2">
        <template v-if="!isClientHeader">
          <a v-for="link in navLinks" :key="link.to" :href="link.to" :target="link.blank ? '_blank' : ''"
            class="block px-4 py-2 text-gray-600 hover:bg-red-50 hover:text-red-600" @click="isMobileMenuOpen = false">
            {{ link.text }}
          </a>
          <!-- Link para gerenciar figures no menu mobile -->
          <NuxtLink v-if="isAdmin" to="/admin/figures"
            class="block px-4 py-2 text-gray-600 hover:bg-red-50 hover:text-red-600" @click="isMobileMenuOpen = false">
            Gerenciar Figures
          </NuxtLink>
        </template>
        <template v-else>
          <button v-for="item in menuItems" :key="item.id"
            @click="$emit('change-section', item.id); isMobileMenuOpen = false"
            class="flex items-center space-x-2 px-4 py-2 text-gray-600 hover:bg-red-50 hover:text-red-600 rounded-lg w-full"
            :class="{ 'bg-red-50 text-red-600': currentSection === item.id }">
            <i :class="item.icon" class="text-lg"></i>
            <span>{{ item.text }}</span>
          </button>
        </template>
      </nav>
    </div>

    <!-- Search Modal -->
    <div v-if="searchIsOpen" class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-start justify-center pt-20"
      @click="closeSearch">
      <div class="bg-white w-full max-w-2xl mx-4 rounded-lg shadow-xl" @click.stop>
        <div class="p-4">
          <div class="relative">
            <input v-model="searchTerm" type="text" placeholder="Buscar figures..."
              class="w-full px-4 py-3 pr-10 rounded-lg border focus:outline-none focus:ring-2 focus:ring-red-500"
              @input="debouncedSearch" @keydown.esc="closeSearch" ref="searchInput" id="search-input" />
            <i class="fas fa-search absolute right-3 top-1/2 -translate-y-1/2 text-gray-400"></i>
          </div>

          <!-- Loading state -->
          <div v-if="loading" class="py-4">
            <!-- Skeletons para resultados de busca -->
            <div v-for="i in 3" :key="i" class="flex items-center space-x-4 p-2 animate-pulse">
              <!-- Imagem skeleton -->
              <div class="w-16 h-16 bg-gray-200 rounded flex items-center justify-center">
                <svg class="w-8 h-8 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
              </div>
              <!-- Conteúdo skeleton -->
              <div class="flex-1">
                <div class="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div class="h-3 bg-gray-200 rounded w-1/2"></div>
              </div>
            </div>
          </div>

          <!-- Results -->
          <div v-else-if="searchResults.length > 0" class="mt-4 max-h-96 overflow-y-auto">
            <div v-for="figure in searchResults" :key="figure.id"
              class="flex items-center space-x-4 p-2 hover:bg-gray-50 rounded-lg cursor-pointer"
              @click="goToFigure(figure)">
              <img :src="figure.gallery_imgs[0]" :alt="figure.title"
                class="w-16 h-16 object-contain bg-gray-50 rounded" />
              <div>
                <h3 class="font-semibold text-gray-800">{{ figure.alias }}</h3>
                <p class="text-sm text-gray-600">{{ figure.description }}</p>
              </div>
            </div>
          </div>

          <!-- No results -->
          <div v-else-if="searchTerm" class="py-8 text-center text-gray-600">
            Nenhuma figure encontrada para "{{ searchTerm }}"
          </div>
        </div>
      </div>
    </div>
  </header>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue';
import { useRoute } from 'vue-router';
import { useAuth } from '~/composables/useAuth';
import { useCategoriesStore } from "~/stores/categoriesStore";
import { useProductsStore } from "~/stores/productsStore";
import debounce from "lodash/debounce";
import type { FormattedFigure } from "~/types/figures";
import useGoogleAuth from '~/composables/useGoogleAuth';
import UserMenu from '~/components/UserMenu.vue';

// Stores e composables
const { user, isAuthenticated, isAdmin } = useAuth();
const categoriesStore = useCategoriesStore();
const productsStore = useProductsStore();

// Propriedades
const props = defineProps<{
  isClientHeader?: boolean;
  shouldBeTransparent?: boolean;
  menuItems?: { id: string; text: string; icon: string }[];
  currentSection?: string;
}>();

// Emits
const emit = defineEmits(["change-section"]);

console.log('headerbar criado')

// Estado
const isHovered = ref(false);
const isScrolled = ref(false);
const isMobileMenuOpen = ref(false);
const searchIsOpen = ref(false);
const searchTerm = ref("");
const searchResults = ref<FormattedFigure[]>([]);
const loading = ref(false);
const userMenuOpen = ref(false);
const showUserMenu = ref(false);

// Rota atual
const route = useRoute();

// Computed properties

// Carregar categorias ao montar o componente
onMounted(async () => {
  window.addEventListener("scroll", handleScroll);
  handleScroll();
});

// Gerar links de navegação
const navLinks = computed(() => {
  const baseLinks = [
    { text: "Home", to: "/" },
    { text: "Categorias", to: "/categorias" },
    { text: "Novidades", to: "/produtos-recentes" },
    { text: "Já Produzidos", to: "/produtos-ja-produzidos" },
    { text: "Studio", to: "/studio" },
    { text: "Contato", to: "https://bento.me/fidelfigures", blank: true },
  ];

  return baseLinks;
});

// Funções
function handleScroll() {
  isScrolled.value = window.scrollY > 0;
}

function openSearch() {
  searchIsOpen.value = true;
  searchTerm.value = "";
  searchResults.value = [];
  setTimeout(() => {
    const searchInput = document.querySelector("#search-input");
    if (searchInput) {
      (searchInput as HTMLElement).focus();
    }
  }, 100);
}

function closeSearch() {
  searchIsOpen.value = false;
}



// Debounce para a busca
const debouncedSearch = debounce(async () => {
  if (!searchTerm.value.trim()) {
    searchResults.value = [];
    return;
  }

  loading.value = true;
  try {
    console.log(`[HeaderBar] Buscando produtos com termo: ${searchTerm.value}`);

    // Verificar se os produtos já foram carregados
    if (!productsStore.initialized || productsStore.products.length === 0) {
      console.log(`[HeaderBar] Produtos ainda não carregados, buscando todos os produtos...`);
      await productsStore.fetchAllProducts();
    }

    const allProducts = productsStore.products;
    const searchTermLower = searchTerm.value.toLowerCase().trim();

    const filteredResults = allProducts.filter((product: any) => {
      return (
        product.title.toLowerCase().includes(searchTermLower) ||
        (product.description && product.description.toLowerCase().includes(searchTermLower)) ||
        product.categories.some((cat: string) => cat.toLowerCase().includes(searchTermLower))
      );
    });

    console.log(`[HeaderBar] Encontrados ${filteredResults.length} resultados para "${searchTerm.value}"`);
    searchResults.value = filteredResults as FormattedFigure[];
  } catch (error) {
    console.error('[HeaderBar] Erro ao buscar produtos:', error);
    searchResults.value = [];
  } finally {
    loading.value = false;
  }
}, 300);

async function handleLogout() {
  try {
    const googleAuth = useGoogleAuth();
    await googleAuth.logout();
    console.log('u2705 [Auth] Logout realizado com sucesso');
    navigateTo("/");
  } catch (error) {
    console.error("u274c Erro ao fazer logout:", error);
  }
}

function goToFigure(figure: FormattedFigure) {
  closeSearch();
  navigateTo(`/produto/${figure.idML}`);
}

function toggleUserMenu() {
  showUserMenu.value = !showUserMenu.value;
}

function handleSignOut() {
  handleLogout();
}

function isActiveRoute(to: string) {
  return route.path === to;
}

onMounted(() => {
  window.addEventListener("scroll", handleScroll);
});

onUnmounted(() => {
  window.removeEventListener("scroll", handleScroll);
});
</script>
