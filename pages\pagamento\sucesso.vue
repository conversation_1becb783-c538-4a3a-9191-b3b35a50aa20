<template>
  <div class="min-h-screen bg-gray-50 py-12">
    <div class="max-w-3xl mx-auto px-4">
      <div class="bg-white rounded-lg shadow-lg p-8">
        <!-- Cabeçalho -->
        <div class="text-center mb-8">
          <div class="mb-4">
            <i class="fas fa-check-circle text-green-500 text-6xl"></i>
          </div>
          <h1 class="text-3xl font-bold text-gray-900 mb-2">
            Pagamento Aprovado!
          </h1>
          <p class="text-gray-600">Seu pedido foi realizado com sucesso.</p>
        </div>

        <!-- Loading State -->
        <div v-if="isLoading" class="flex justify-center items-center py-8">
          <div
            class="animate-spin rounded-full h-12 w-12 border-b-2 border-green-500"
          ></div>
        </div>

        <!-- Error State -->
        <div
          v-else-if="error"
          class="bg-green-50 border border-green-500 rounded-lg p-4 mb-6"
        >
          <p class="text-green-700">{{ error }}</p>
        </div>

        <!-- Success Message -->
        <div
          v-else-if="successMessage"
          class="bg-green-50 border border-green-500 rounded-lg p-4 mb-6"
        >
          <p class="text-green-700">{{ successMessage }}</p>
        </div>

        <!-- Success State -->
        <div v-else-if="!successMessage" class="space-y-6">
          <!-- Detalhes do Produto -->
          <div
            v-if="productName.length"
            class="border-t border-b border-gray-200 py-4"
          >
            <h2 class="text-lg font-semibold text-gray-800 mb-4">
              Produto Adquirido
            </h2>
            <div class="flex items-center">
              <div
                v-if="productImage.length"
                class="w-24 h-24 flex-shrink-0 overflow-hidden rounded-md border border-gray-200"
              >
                <img
                  :src="productImage"
                  :alt="productName"
                  class="h-full w-full object-cover object-center"
                />
              </div>
              <div class="ml-4 flex-1">
                <h3 class="text-lg font-medium text-gray-900">
                  {{ productName }}
                </h3>
              </div>
            </div>
          </div>

          <!-- Endereço de Entrega -->
          <div
            v-if="addressStreet.length"
            class="border-t border-b border-gray-200 py-4"
          >
            <h2 class="text-lg font-semibold text-gray-800 mb-4">
              Endereço de Entrega
            </h2>
            <div class="bg-gray-50 p-4 rounded-md">
              <p class="font-medium">{{ addressName }}</p>
              <p>{{ addressStreet }}, {{ addressNumber }}</p>
              <p v-if="addressComplement.length">{{ addressComplement }}</p>
              <p>
                {{ addressNeighborhood }}, {{ addressCity }} -
                {{ addressState }}
              </p>
              <p>CEP: {{ addressZipCode }}</p>
            </div>
          </div>

          <!-- Detalhes do Pedido -->
          <div class="border-t border-b border-gray-200 py-4">
            <h2 class="text-lg font-semibold text-gray-800 mb-4">
              Detalhes do Pagamento
            </h2>
            <dl class="divide-y divide-gray-200">
              <div class="grid grid-cols-3 gap-4 py-3">
                <dt class="text-sm font-medium text-gray-500">ID do Pedido</dt>
                <dd class="text-sm text-gray-900 col-span-2">
                  {{ paymentId }}
                </dd>
              </div>
              <div class="grid grid-cols-3 gap-4 py-3">
                <dt class="text-sm font-medium text-gray-500">Status</dt>
                <dd class="text-sm text-green-600 font-medium col-span-2">
                  {{ formatCollectionStatus(collectionStatus) }}
                </dd>
              </div>
              <div class="grid grid-cols-3 gap-4 py-3">
                <dt class="text-sm font-medium text-gray-500">
                  Forma de Pagamento
                </dt>
                <dd class="text-sm text-gray-900 col-span-2">
                  {{ formatPaymentType(paymentType) }}
                </dd>
              </div>
            </dl>
          </div>

          <!-- Botões de Ação -->
          <div class="flex justify-center gap-4">
            <NuxtLink
              to="/admin/area-do-cliente"
              class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700"
            >
              <i class="fas fa-list-ul mr-2"></i>
              Ver Meus Pedidos
            </NuxtLink>
            <NuxtLink
              to="/"
              class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
            >
              <i class="fas fa-home mr-2"></i>
              Voltar para Home
            </NuxtLink>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, type Ref } from "vue";
import { useRoute } from "vue-router";
import { useAuth } from "~/composables/useAuth";
import { useCustomer } from "~/composables/useCustomer";
import { useGoogleAdsConversion } from "~/composables/useGoogleAdsConversion";
import { storeToRefs } from "pinia";
import type { PaymentProvider } from "~/types/payment";
import { v4 as uuidv4 } from "uuid";
import type { IPendingOrder } from "~/types/customer";

// Adiciona o snippet de evento de conversão do Google Ads
// Será configurado dinamicamente após o carregamento da página

const route = useRoute();
const { user } = useAuth();
const customerStore = useCustomer();
const { customer } = storeToRefs(customerStore);

const isLoading = ref(true);
const error = ref("");
const successMessage = ref("");
const paymentProvider = ref<PaymentProvider>("mercadopago");

// Sempre usando MercadoPago como método de pagamento

// Parâmetros da URL para MercadoPago
const paymentId = (route.query.payment_id as string) || "";
const collectionId = (route.query.collection_id as string) || "";
const collectionStatus =
  (route.query.collection_status as string) || "approved";
const paymentType = (route.query.payment_type as string) || "";
const status = (route.query.status as string) || "approved";
const merchantOrderId = (route.query.merchant_order_id as string) || "";
const preferenceId = (route.query.preference_id as string) || "";
const siteId = (route.query.site_id as string) || "";
const processingMode = (route.query.processing_mode as string) || "";
const merchantAccountId = (route.query.merchant_account_id as string) || "";
const externalReference = (route.query.external_reference as string) || null;

// Informações do produto
const productId = (route.query.product_id as string) || "";
const productName = ref((route.query.product_name as string) || "");
const productPrice = ref((route.query.product_price as string) || "");
const productImage = ref((route.query.product_image as string) || "");

// ID do pedido pendente (para MercadoPago)
const pendingOrderId = (route.query.pending_order_id as string) || "";

// Informações do endereço
const addressId = (route.query.address_id as string) || "";
const addressName = ref((route.query.address_name as string) || "");
const addressStreet = ref((route.query.address_street as string) || "");
const addressNumber = ref((route.query.address_number as string) || "");
const addressComplement = ref((route.query.address_complement as string) || "");
const addressNeighborhood = ref(
  (route.query.address_neighborhood as string) || ""
);
const addressCity = ref((route.query.address_city as string) || "");
const addressState = ref((route.query.address_state as string) || "");
const addressZipCode = ref((route.query.address_zipcode as string) || "");

const formatCollectionStatus = (status: string) => {
  return status === "approved" ? "Aprovado" : status;
};

const formatPaymentType = (type: string) => {
  const types: { [key: string]: string } = {
    credit_card: "Cartão de Crédito",
    debit_card: "Cartão de Débito",
    pix: "PIX",
    bank_transfer: "Transferência Bancária",
    card: "Cartão",
  };
  return types[type] || type;
};

// Formatar preço
const formatPrice = (price: Ref<string> | string) => {
  const priceValue = typeof price === "string" ? price : price.value;
  if (!priceValue) return "R$ 0,00";

  const numPrice = parseFloat(priceValue);
  return new Intl.NumberFormat("pt-BR", {
    style: "currency",
    currency: "BRL",
  }).format(numPrice);
};

onMounted(async () => {
  if (!user.value?.uid) {
    error.value = "Usuário não autenticado";
    isLoading.value = false;
    return;
  }

  try {
    // Verificar se temos os parâmetros necessários do MercadoPago
    if (paymentId && preferenceId) {
      // MercadoPago payment
      paymentProvider.value = "mercadopago";

      console.log("Processando pagamento aprovado com paymentId:", paymentId);
      console.log("Parâmetros da URL:", {
        paymentId,
        collectionId,
        collectionStatus,
        paymentType,
        merchantOrderId,
        preferenceId,
        productId,
        pendingOrderId,
      });
      console.log(
        "Pedidos pendentes disponíveis:",
        customer.value?.pendingOrders
      );

      // Se não temos a imagem do produto, buscar o produto pelo ID
      if (productId && !productImage.value) {
        try {
          debugger;
          const { data } = await useFetch(`/api/figures/${productId}`);
          if (data.value) {
            console.log("item: ", data.value);
            productName.value = data.value.title || data.value.alias || "";
            productImage.value = data.value.gallery_imgs?.[0] || "";
            productPrice.value = data.value.finalPrice?.toString() || "";
          }
        } catch (err) {
          console.error("Erro ao buscar detalhes do produto:", err);
        }
      }

      debugger;

      // Verificar se temos um pedido pendente com este preferenceId ou ID
      let pendingOrder = customer.value?.pendingOrders?.find(
        (order) =>
          order.preferenceId === preferenceId ||
          (pendingOrderId && order.id === pendingOrderId) ||
          (productId && order.productId === productId)
      );

      console.log("Pedido pendente encontrado:", pendingOrder);

      if (!pendingOrder && status !== "approved") {
        console.log(
          "Nenhum pedido pendente encontrado com preferenceId:",
          preferenceId
        );
        console.log("Criando um novo pedido com os dados disponíveis");

        // Criar um novo pedido com os dados disponíveis
        pendingOrder = {
          id: pendingOrderId || uuidv4(),
          paymentId,
          collectionId,
          collectionStatus,
          paymentType,
          externalReference: externalReference || productId,
          merchantOrderId,
          preferenceId,
          siteId,
          processingMode,
          merchantAccountId: merchantAccountId || "",
          productId,
          total: productPrice.value ? parseFloat(productPrice.value) : null,
          quantity: 1,
          createdAt: new Date(),
          updatedAt: new Date(),
          paymentProvider: "mercadopago",
        } as IPendingOrder;

        // Adicionar o pedido pendente ao cliente
        if (Array.isArray(customer.value?.pendingOrders)) {
          customer.value.pendingOrders.push(pendingOrder);
        } else if (customer.value) {
          customer.value.pendingOrders = [pendingOrder];
        }
      }

      // Agora salvar o pedido final
      try {
        console.log("Salvando pedido final com os dados:", pendingOrder);
        const orderData = {
          paymentId,
          collectionId,
          collectionStatus,
          paymentType,
          externalReference:
            externalReference || pendingOrder.externalReference || productId,
          merchantOrderId,
          preferenceId,
          siteId,
          total: productPrice.value ? parseFloat(productPrice.value) : null,
          processingMode,
          merchantAccountId: merchantAccountId || "",
          paymentProvider: "mercadopago" as const,
          productId: productId || pendingOrder.productId,
        };

        const savedOrder = await customerStore.saveOrder(orderData);

        // Verificar se houve erro ao salvar o pedido (produto já comprado)
        if (
          customerStore.error &&
          customerStore.error.includes("já foi comprado")
        ) {
          console.log("Produto já foi comprado anteriormente");
          successMessage.value =
            "Este produto já foi comprado anteriormente. Você pode ver seus pedidos na área do cliente.";
        } else {
          // Enviar notificação por email para o administrador e para o cliente
          try {
            console.log("Enviando notificação de venda realizada...");

            // Preparar os dados para a notificação
            const notifyData = {
              productName: productName.value || "",
              productId: productId || "",
              productImage: productImage.value || "",
              productPrice: productPrice.value || 0,
              userEmail: user?.value?.email || "",
              userName: user?.value?.displayName || "",
            };

            await $fetch(
              `/api/notify?type=payment&productName=${encodeURIComponent(
                notifyData.productName
              )}&productId=${encodeURIComponent(notifyData.productId)}`,
              {
                method: "POST",
                body: {
                  payer: {
                    email: notifyData.userEmail,
                    name: notifyData.userName,
                  },
                  productName: notifyData.productName,
                  productImage: notifyData.productImage,
                  productPrice: notifyData.productPrice,
                },
              }
            );
          } catch (notifyError) {
            console.error("Erro ao enviar notificação de venda:", notifyError);
            // Não interromper o fluxo se a notificação falhar
          }
        }

        // Remover o pedido pendente após salvar o pedido final
        if (pendingOrder && pendingOrder.id) {
          console.log("Removendo pedido pendente com ID:", pendingOrder.id);

          // Usar o método do customerStore para remover o pedido pendente
          try {
            const removed = await customerStore.removePendingOrder(
              pendingOrder.id
            );
            if (removed) {
              console.log("Pedido pendente removido com sucesso do Firebase");
            } else {
              console.error("Erro ao remover pedido pendente do Firebase");
            }
          } catch (removeError) {
            console.error("Erro ao remover pedido pendente:", removeError);
          }
        }
      } catch (saveError) {
        console.error("Erro ao salvar pedido final:", saveError);
        // Tentar salvar diretamente sem usar o pedido pendente
        const fallbackOrderData = {
          paymentId,
          collectionId,
          collectionStatus,
          paymentType,
          externalReference: externalReference || productId,
          merchantOrderId,
          preferenceId,
          siteId,
          processingMode,
          merchantAccountId: merchantAccountId || "",
          paymentProvider: "mercadopago" as const,
          productId,
        };

        console.log(
          "Tentando salvar com dados alternativos:",
          fallbackOrderData
        );
        await customerStore.saveOrder(fallbackOrderData);
      }

      // Se não temos informações completas do endereço, buscar o endereço padrão do cliente
      if (!addressStreet.value && customer.value) {
        const defaultAddress = customer.value.addresses?.find(
          (addr) => addr.isDefault
        );
        if (defaultAddress) {
          addressName.value = defaultAddress.name || customer.value.name;
          addressStreet.value = defaultAddress.street;
          addressNumber.value = defaultAddress.number;
          addressComplement.value = defaultAddress.complement || "";
          addressNeighborhood.value = defaultAddress.neighborhood;
          addressCity.value = defaultAddress.city;
          addressState.value = defaultAddress.state;
          addressZipCode.value = defaultAddress.zipCode;
        }
      }
    } else {
      error.value = "Informações de pagamento não encontradas";
    }
  } catch (e) {
    error.value =
      "Seu pedido já foi processado! Entraremos em contato o mais breve possível!";
  } finally {
    isLoading.value = false;

    // Configurar o evento de conversão do Google Ads com o valor real da compra
    setTimeout(() => {
      const { trackPurchase } = useGoogleAdsConversion();
      const price = parseFloat(productPrice.value) || 1.0;
      const transactionId = paymentId || collectionId || uuidv4();

      // Usar o composable para rastrear a conversão de compra
      trackPurchase(
        price,
        transactionId,
        productId,
        productName.value
      );
    }, 1000);
  }
});
</script>
