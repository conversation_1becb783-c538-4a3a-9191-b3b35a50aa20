<template>
  <div>
    <!-- Header Admin -->
    <HeaderAdmin />

    <div class="min-h-screen bg-gray-100 py-4 sm:py-8">
      <div class="container mx-auto px-3 sm:px-4">
        <div class="bg-white rounded-lg shadow-md p-4 sm:p-6">
          <div class="flex justify-between items-center mb-6">
            <h1 class="text-2xl font-bold">Gestão de Categorias</h1>
            <div v-if="loading" class="flex items-center text-gray-500">
              <i class="fas fa-spinner fa-spin mr-2"></i>
              <span>Carregando...</span>
            </div>
          </div>

          <!-- Formulário de criação - responsivo -->
          <div class="flex flex-col sm:flex-row gap-4 mb-6">
            <input
              v-model="newCategoryName"
              type="text"
              placeholder="Nome da nova categoria"
              class="w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-red-500"
            />
            <select
              v-model="selectedParentCategory"
              class="w-full sm:w-auto px-4 py-2 border rounded-lg focus:ring-2 focus:ring-red-500"
            >
              <option :value="undefined">Sem categoria pai</option>
              <option
                v-for="category in categories"
                :key="category.id"
                :value="category.id"
              >
                {{ category.name }}
              </option>
            </select>
            <button
              @click="createCategory"
              class="w-full sm:w-auto bg-red-600 text-white px-6 py-2 rounded-lg hover:bg-red-700 mt-2 sm:mt-0"
            >
              Adicionar
            </button>
          </div>

          <!-- Lista de categorias - responsiva -->
          <div class="border rounded-lg p-3 sm:p-4 overflow-x-auto">
            <div v-if="categories.length === 0" class="text-gray-500 text-center py-4">
              Nenhuma categoria encontrada
            </div>
            <div v-else class="space-y-4">
              <div v-for="category in rootCategories" :key="category.id"
                class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                <!-- Cabeçalho da categoria principal - responsivo -->
                <div class="bg-gray-50 px-3 sm:px-4 py-3 border-b border-gray-200">
                  <div class="flex flex-wrap items-center justify-between gap-2">
                    <div class="flex items-center">
                      <span class="font-medium text-gray-900 break-words">{{ category.name }}</span>
                    </div>
                    <div class="flex items-center gap-2">
                      <button
                        @click="openEditCategory(category)"
                        class="text-gray-400 hover:text-blue-600 p-2 rounded hover:bg-gray-100"
                        aria-label="Editar categoria"
                      >
                        <i class="fas fa-pencil-alt"></i>
                      </button>
                      <button
                        @click="confirmDeleteCategory(category)"
                        class="text-gray-400 hover:text-red-600 p-2 rounded hover:bg-gray-100"
                        aria-label="Excluir categoria"
                      >
                        <i class="fas fa-trash-alt"></i>
                      </button>
                    </div>
                  </div>
                </div>

                <!-- Subcategorias - responsivo -->
                <div class="px-2 sm:px-4 py-3 overflow-x-auto">
                  <CategoryTree
                    v-if="hierarchy.get(category.id)?.length"
                    :category-id="category.id"
                    :categories="categories"
                    :hierarchy="hierarchy"
                    :selected-categories="[]"
                    :depth="1"
                  />
                  <div v-else class="text-gray-500 text-sm italic">
                    Sem subcategorias
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Modal de Edição - responsivo -->
    <div v-if="editingCategory" class="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50 overflow-y-auto">
      <div class="bg-white rounded-lg shadow-xl w-full max-w-md mx-auto my-8">
        <div class="p-4 sm:p-6">
          <div class="flex justify-between items-center mb-4">
            <h3 class="text-xl font-semibold">Editar Categoria</h3>
            <button
              @click="editingCategory = null"
              class="text-gray-400 hover:text-gray-600"
              aria-label="Fechar"
            >
              <i class="fas fa-times"></i>
            </button>
          </div>

          <div class="space-y-4">
            <div>
              <label for="category-name" class="block text-sm font-medium text-gray-700 mb-1">Nome da categoria</label>
              <input
                id="category-name"
                v-model="editingCategory.name"
                type="text"
                class="w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-red-500"
                placeholder="Nome da categoria"
              />
            </div>

            <div>
              <label for="parent-category" class="block text-sm font-medium text-gray-700 mb-1">Categoria pai</label>
              <select
                id="parent-category"
                v-model="editingCategory.fatherId"
                class="w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-red-500"
              >
                <option :value="undefined">Sem categoria pai</option>
                <option
                  v-for="category in categories.filter(c => c.id !== editingCategory?.id)"
                  :key="category.id"
                  :value="category.id"
                >
                  {{ category.name }}
                </option>
              </select>
            </div>
          </div>

          <div class="flex flex-col sm:flex-row justify-end gap-3 mt-6">
            <button
              @click="editingCategory = null"
              class="w-full sm:w-auto order-2 sm:order-1 px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50"
            >
              Cancelar
            </button>
            <button
              @click="updateCategory"
              class="w-full sm:w-auto order-1 sm:order-2 bg-red-600 text-white px-6 py-2 rounded-lg hover:bg-red-700"
            >
              Salvar
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Modal de Confirmação de Exclusão - responsivo -->
    <div v-if="showDeleteModal" class="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50 overflow-y-auto">
      <div class="bg-white rounded-lg shadow-xl w-full max-w-md mx-auto my-8">
        <div class="p-4 sm:p-6">
          <div class="flex justify-between items-center mb-4">
            <h3 class="text-xl font-semibold">Confirmar Exclusão</h3>
            <button
              @click="showDeleteModal = false"
              class="text-gray-400 hover:text-gray-600"
              aria-label="Fechar"
            >
              <i class="fas fa-times"></i>
            </button>
          </div>

          <div class="mb-6">
            <p class="text-gray-700">
              Tem certeza que deseja excluir a categoria "{{ categoryToDelete?.name }}"?
            </p>
            <div v-if="hasSubcategories(categoryToDelete)" class="mt-3 p-3 bg-red-50 text-red-700 rounded-lg border border-red-200">
              <i class="fas fa-exclamation-triangle mr-2"></i>
              <span>Esta categoria possui subcategorias que também serão removidas!</span>
            </div>
          </div>

          <div class="flex flex-col sm:flex-row justify-end gap-3">
            <button
              @click="showDeleteModal = false"
              class="w-full sm:w-auto order-2 sm:order-1 px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50"
            >
              Cancelar
            </button>
            <button
              @click="deleteCategory"
              class="w-full sm:w-auto order-1 sm:order-2 bg-red-600 text-white px-6 py-2 rounded-lg hover:bg-red-700"
            >
              Confirmar Exclusão
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import CategoryTree from '~/components/CategoryTree.vue';

interface Category {
  id: string;
  name: string;
  fatherId?: string;
}

// Estados
const categories = ref<Category[]>([]);
const hierarchy = ref<Map<string | undefined, string[]>>(new Map());
const newCategoryName = ref('');
const selectedParentCategory = ref<string | undefined>(undefined);
const editingCategory = ref<Category | null>(null);
const showDeleteModal = ref(false);
const categoryToDelete = ref<Category | null>(null);
const loading = ref(false);

// Computed
const rootCategories = computed(() => {
  return categories.value.filter(category => !category.fatherId);
});

// Funções auxiliares
const buildHierarchy = (categories: Category[]) => {
  const hierarchyMap = new Map<string | undefined, string[]>();

  // Inicializa o mapa com uma entrada para categorias raiz
  hierarchyMap.set(undefined, []);

  // Primeiro, inicializa todas as entradas no mapa
  categories.forEach(category => {
    hierarchyMap.set(category.id, []);
  });

  // Depois, preenche as relações pai-filho
  categories.forEach(category => {
    const fatherId = category.fatherId || undefined;
    const children = hierarchyMap.get(fatherId) || [];
    children.push(category.id);
    hierarchyMap.set(fatherId, children);
  });

  return hierarchyMap;
};

const hasSubcategories = (category: Category | null) => {
  return category ? (hierarchy.value.get(category.id)?.length || 0) > 0 : false;
};

const toKebabCase = (str: string): string => {
  return str
    .normalize('NFD')
    .replace(/[\u0300-\u036f]/g, '') // Remove acentos
    .toLowerCase()
    .replace(/[^\w\s-]/g, '') // Remove caracteres especiais
    .replace(/[\s_]+/g, '-') // Substitui espaços e underscores por hífen
    .replace(/^-+|-+$/g, ''); // Remove hífens do início e fim
};

const toCamelCase = (str: string): string => {
  return str
    .normalize('NFD')
    .replace(/[\u0300-\u036f]/g, '') // Remove acentos
    .replace(/[^\w\s]/g, '') // Remove caracteres especiais
    .replace(/\b\w/g, c => c.toUpperCase()); // Capitaliza primeira letra de cada palavra
};

// Funções CRUD
const fetchCategories = async (forceRefresh = false) => {
  try {
    loading.value = true;
    const url = forceRefresh ? '/api/categories?refresh=true' : '/api/categories';
    const response = await fetch(url);
    if (!response.ok) {
      throw new Error(`Erro HTTP: ${response.status}`);
    }
    const data = await response.json();
    categories.value = data as Category[];
    hierarchy.value = buildHierarchy(categories.value);
  } catch (error) {
    console.error('Erro ao carregar categorias:', error);
  } finally {
    loading.value = false;
  }
};

const createCategory = async () => {
  if (!newCategoryName.value.trim()) return;

  try {
    loading.value = true;
    const categoryId = toKebabCase(newCategoryName.value.trim());
    const _name = toCamelCase(newCategoryName.value.trim());

    // Criar a categoria usando a API
    const response = await fetch('/api/categories', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        id: categoryId,
        name: _name,
        fatherId: selectedParentCategory.value || undefined,
        description: '',
      })
    });

    if (!response.ok) {
      throw new Error(`Erro HTTP: ${response.status}`);
    }

    // Recarregar as categorias para obter a lista atualizada (forçar refresh)
    await fetchCategories(true);

    newCategoryName.value = '';
    selectedParentCategory.value = undefined;

  } catch (error) {
    console.error('Erro ao criar categoria:', error);
    alert('Erro ao criar categoria. Por favor, tente novamente.');
  } finally {
    loading.value = false;
  }
};

const openEditCategory = (category: Category) => {
  editingCategory.value = { ...category };
};

const updateCategory = async () => {
  if (!editingCategory.value) return;

  try {
    loading.value = true;

    // Atualizar a categoria usando a API
    const response = await fetch(`/api/categories/${editingCategory.value.id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        name: editingCategory.value.name,
        fatherId: editingCategory.value.fatherId
      })
    });

    if (!response.ok) {
      throw new Error(`Erro HTTP: ${response.status}`);
    }

    // Recarregar as categorias para obter a lista atualizada (forçar refresh)
    await fetchCategories(true);

    editingCategory.value = null;

  } catch (error) {
    console.error('Erro ao atualizar categoria:', error);
    alert('Erro ao atualizar categoria. Por favor, tente novamente.');
  } finally {
    loading.value = false;
  }
};

const confirmDeleteCategory = (category: Category) => {
  categoryToDelete.value = category;
  showDeleteModal.value = true;
};

const deleteCategory = async () => {
  if (!categoryToDelete.value) return;

  try {
    loading.value = true;

    // Excluir a categoria usando a API
    const response = await fetch(`/api/categories/${categoryToDelete.value.id}`, {
      method: 'DELETE'
    });

    if (!response.ok) {
      throw new Error(`Erro HTTP: ${response.status}`);
    }

    // Recarregar as categorias para obter a lista atualizada (forçar refresh)
    await fetchCategories(true);

    showDeleteModal.value = false;
    categoryToDelete.value = null;
  } catch (error) {
    console.error('Erro ao excluir categoria:', error);
    alert('Erro ao excluir categoria. Por favor, tente novamente.');
  } finally {
    loading.value = false;
  }
};

// Inicialização
onMounted(() => {
  fetchCategories();
});

definePageMeta({
  middleware: ["auth"],
});
</script>
