import { doc, getDoc, updateDoc, Timestamp } from "firebase/firestore";
import { db } from "~/server/utils/firebase";
import type { IReview, IUpdateReviewRequest } from "~/types/reviews";
import { REVIEW_VALIDATION } from "~/types/reviews";

const REVIEWS_COLLECTION = "reviews";

/**
 * Endpoint otimizado para atualizar avaliações
 * PUT /api/v2/reviews/:reviewId
 */
export default defineEventHandler(async (event) => {
  try {
    const reviewId = getRouterParam(event, "reviewId");
    
    if (!reviewId) {
      throw createError({
        statusCode: 400,
        statusMessage: "ID da avaliação é obrigatório",
      });
    }

    const body = await readBody(event) as IUpdateReviewRequest & { userId: string };
    
    if (!body.userId) {
      throw createError({
        statusCode: 400,
        statusMessage: "ID do usuário é obrigatório",
      });
    }

    console.log(`🔍 [API v2] Atualizando avaliação ${reviewId} pelo usuário ${body.userId}`);

    // Buscar a avaliação existente
    const reviewRef = doc(db, REVIEWS_COLLECTION, reviewId);
    const reviewDoc = await getDoc(reviewRef);
    
    if (!reviewDoc.exists()) {
      throw createError({
        statusCode: 404,
        statusMessage: "Avaliação não encontrada",
      });
    }

    const existingReview = reviewDoc.data() as IReview;

    // Verificar se o usuário é o dono da avaliação
    if (existingReview.userId !== body.userId) {
      throw createError({
        statusCode: 403,
        statusMessage: "Você não tem permissão para editar esta avaliação",
      });
    }

    // Validar os dados de atualização
    const validation = validateUpdateData(body);
    if (!validation.isValid) {
      throw createError({
        statusCode: 400,
        statusMessage: "Dados inválidos",
        data: validation.errors,
      });
    }

    // Preparar dados para atualização
    const updateData: Partial<IReview> = {
      updatedAt: new Date(),
    };

    if (body.rating !== undefined) {
      updateData.rating = Number(body.rating);
    }

    if (body.comment !== undefined) {
      updateData.comment = body.comment.trim();
    }

    if (body.images !== undefined) {
      updateData.images = Array.isArray(body.images) ? body.images : [];
    }

    if (body.videos !== undefined) {
      updateData.videos = Array.isArray(body.videos) ? body.videos : [];
    }

    // Atualizar no Firestore
    await updateDoc(reviewRef, {
      ...updateData,
      updatedAt: Timestamp.fromDate(updateData.updatedAt!),
    });

    // Buscar a avaliação atualizada
    const updatedReviewDoc = await getDoc(reviewRef);
    const updatedReview = updatedReviewDoc.data() as IReview;

    console.log(`✅ [API v2] Avaliação ${reviewId} atualizada com sucesso`);

    return {
      success: true,
      message: "Avaliação atualizada com sucesso",
      data: {
        review: {
          ...updatedReview,
          id: reviewId,
          createdAt: updatedReview.createdAt?.toDate?.() 
            ? updatedReview.createdAt.toDate().toISOString() 
            : new Date(updatedReview.createdAt).toISOString(),
          updatedAt: updatedReview.updatedAt?.toDate?.() 
            ? updatedReview.updatedAt.toDate().toISOString() 
            : new Date(updatedReview.updatedAt).toISOString(),
        },
      },
    };

  } catch (error: any) {
    console.error("❌ [API v2] Erro ao atualizar avaliação:", error);
    
    if (error.statusCode) {
      throw error;
    }
    
    throw createError({
      statusCode: 500,
      statusMessage: "Erro interno do servidor ao atualizar avaliação",
    });
  }
});

/**
 * Valida os dados de atualização da avaliação
 */
function validateUpdateData(data: IUpdateReviewRequest) {
  const errors: Record<string, string> = {};

  if (data.rating !== undefined) {
    if (data.rating < REVIEW_VALIDATION.MIN_RATING || data.rating > REVIEW_VALIDATION.MAX_RATING) {
      errors.rating = `Avaliação deve ser entre ${REVIEW_VALIDATION.MIN_RATING} e ${REVIEW_VALIDATION.MAX_RATING} estrelas`;
    }
  }

  if (data.comment !== undefined) {
    if (data.comment.trim().length < REVIEW_VALIDATION.MIN_COMMENT_LENGTH) {
      errors.comment = `Comentário deve ter pelo menos ${REVIEW_VALIDATION.MIN_COMMENT_LENGTH} caracteres`;
    }
    
    if (data.comment.length > REVIEW_VALIDATION.MAX_COMMENT_LENGTH) {
      errors.comment = `Comentário deve ter no máximo ${REVIEW_VALIDATION.MAX_COMMENT_LENGTH} caracteres`;
    }
  }

  if (data.images && data.images.length > REVIEW_VALIDATION.MAX_IMAGES) {
    errors.images = `Máximo de ${REVIEW_VALIDATION.MAX_IMAGES} imagens permitidas`;
  }

  if (data.videos && data.videos.length > REVIEW_VALIDATION.MAX_VIDEOS) {
    errors.videos = `Máximo de ${REVIEW_VALIDATION.MAX_VIDEOS} vídeos permitidos`;
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors,
  };
}
