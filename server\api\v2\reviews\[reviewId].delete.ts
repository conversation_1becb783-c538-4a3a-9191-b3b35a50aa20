import { doc, getDoc, deleteDoc } from "firebase/firestore";
import { db } from "~/server/utils/firebase";
import type { IReview } from "~/types/reviews";

const REVIEWS_COLLECTION = "reviews";

/**
 * Endpoint otimizado para deletar avaliações
 * DELETE /api/v2/reviews/:reviewId
 */
export default defineEventHandler(async (event) => {
  try {
    const reviewId = getRouterParam(event, "reviewId");
    
    if (!reviewId) {
      throw createError({
        statusCode: 400,
        statusMessage: "ID da avaliação é obrigatório",
      });
    }

    const body = await readBody(event);
    const { userId } = body;
    
    if (!userId) {
      throw createError({
        statusCode: 400,
        statusMessage: "ID do usuário é obrigatório",
      });
    }

    console.log(`🔍 [API v2] Deletando avaliação ${reviewId} pelo usuário ${userId}`);

    // Buscar a avaliação existente
    const reviewRef = doc(db, REVIEWS_COLLECTION, reviewId);
    const reviewDoc = await getDoc(reviewRef);
    
    if (!reviewDoc.exists()) {
      throw createError({
        statusCode: 404,
        statusMessage: "Avaliação não encontrada",
      });
    }

    const existingReview = reviewDoc.data() as IReview;

    // Verificar se o usuário é o dono da avaliação
    if (existingReview.userId !== userId) {
      throw createError({
        statusCode: 403,
        statusMessage: "Você não tem permissão para deletar esta avaliação",
      });
    }

    // Deletar a avaliação
    await deleteDoc(reviewRef);

    console.log(`✅ [API v2] Avaliação ${reviewId} deletada com sucesso`);

    return {
      success: true,
      message: "Avaliação deletada com sucesso",
      data: {
        deletedReviewId: reviewId,
      },
    };

  } catch (error: any) {
    console.error("❌ [API v2] Erro ao deletar avaliação:", error);
    
    if (error.statusCode) {
      throw error;
    }
    
    throw createError({
      statusCode: 500,
      statusMessage: "Erro interno do servidor ao deletar avaliação",
    });
  }
});
