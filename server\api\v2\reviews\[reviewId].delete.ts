/**
 * Endpoint DESABILITADO - Usuários não podem deletar avaliações
 * DELETE /api/v2/reviews/[reviewId]
 *
 * POLÍTICA DE AVALIAÇÕES:
 * - Usuários podem apenas EDITAR suas avaliações
 * - Apenas ADMINISTRADORES podem deletar avaliações
 * - Use o endpoint: DELETE /api/admin/reviews/[reviewId]
 */
export default defineEventHandler(async (event) => {
  const reviewId = getRouterParam(event, "reviewId");

  console.log(`❌ [API v2] Tentativa de deletar avaliação ${reviewId} por usuário comum - NEGADO`);

  throw createError({
    statusCode: 403,
    statusMessage: "Usuários não podem deletar avaliações. Apenas administradores têm essa permissão. Use a função de editar para modificar sua avaliação.",
  });
});
