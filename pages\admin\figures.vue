<template>
    <!-- Header Admin -->
    <HeaderAdmin />

    <div class="min-h-screen bg-gray-100 py-8">
        <div class="container mx-auto px-4">
            <div class="flex justify-between items-center mb-8">

                <!-- Search bar -->
                <div class="flex items-center w-full">
                    <div class="relative w-full">
                        <input v-model="searchTerm" type="text" placeholder="Buscar figures..."
                            class="w-full px-4 py-2 pr-10 rounded-lg border focus:outline-none focus:ring-2 focus:ring-red-500"
                            @input="debouncedSearch" />
                        <i class="fas fa-search absolute right-3 top-1/2 -translate-y-1/2 text-gray-400"></i>
                    </div>
                    <div class="ml-4 flex space-x-4">
                        <label class="flex items-center space-x-2">
                            <input type="checkbox" v-model="showUpdatedOnly"
                                class="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500" />
                            <span>Atualizados</span>
                        </label>
                        <label class="flex items-center space-x-2">
                            <input type="checkbox" v-model="showPreviouslyProducedOnly"
                                class="w-4 h-4 text-purple-600 border-gray-300 rounded focus:ring-purple-500" />
                            <span>Já Produzidos</span>
                        </label>
                    </div>
                </div>
            </div>

            <!-- Loading state inicial -->
            <div v-if="adminStore.loading?.fetchFigures && !adminStore.figures?.length" class="space-y-4">
                <FigureCardSkeleton v-for="i in 5" :key="i" />
            </div>

            <!-- Error state -->
            <div v-if="adminStore.error" class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4"
                :class="{ 'animate-fade-out': alertFadeOut }">
                <p class="font-bold">Erro:</p>
                <p>{{ adminStore.error }}</p>
                <button @click="loadFigures"
                    class="mt-4 bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700 transition-colors">
                    Tentar Novamente
                </button>
            </div>

            <!-- Empty state -->
            <div v-else-if="!adminStore.figures?.length" class="text-center py-8">
                <p class="text-gray-600" v-if="searchTerm">
                    Nenhuma figure encontrada para "{{ searchTerm }}"
                </p>
                <p class="text-gray-600" v-else>Nenhuma figure encontrada.</p>
            </div>

            <!-- Success message -->
            <div v-if="successMessage"
                class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4 animate-fade-out">
                {{ successMessage }}
            </div>

            <!-- Figures list -->
            <div v-if="adminStore.figures?.length">
                <div class="grid gap-6 mb-8">
                    <div v-for="figure in filteredFigures" :key="figure.id" class="bg-white rounded-lg shadow-md p-6"
                        :class="{ '!bg-green-200': figure.verified, 'border-l-4 border-purple-500': figure.previously_produced }">
                        <!-- Layout responsivo para os cards de figures -->
                        <div class="flex flex-col sm:flex-row items-start gap-4 sm:gap-6">
                            <!-- Figure image -->
                            <div class="w-full sm:w-32 h-32 flex-shrink-0 mb-4 sm:mb-0">
                                <img :src="figure.gallery_imgs[0]" :alt="figure.title"
                                    class="w-full h-full object-contain bg-gray-50 rounded" />
                            </div>

                            <!-- Figure details -->
                            <div class="flex-grow w-full">
                                <div class="flex flex-col sm:flex-row justify-between items-start mb-4 gap-4">
                                    <div class="w-full">
                                        <h2 class="text-xl font-semibold mb-2">
                                            {{ figure.title }}
                                        </h2>
                                        <div class="flex flex-wrap gap-2 mb-2">
                                            <span v-for="category in figure.categories" :key="category"
                                                class="px-2 py-1 text-sm bg-gray-100 text-gray-700 rounded-full">
                                                {{ getCategoryName(category) }}
                                            </span>
                                        </div>
                                        <div v-if="figure.search_tokens?.length" class="flex flex-wrap gap-1">
                                            <span v-for="token in figure.search_tokens" :key="token"
                                                class="px-2 py-1 text-xs bg-red-50 text-red-600 rounded-full border border-red-100">
                                                {{ token }}
                                            </span>
                                        </div>
                                    </div>
                                    <div class="flex space-x-2 mt-2 sm:mt-0">
                                        <button @click="openEditModal(figure.id)"
                                            class="bg-blue-600 text-white px-3 py-1 sm:px-4 sm:py-2 rounded hover:bg-blue-700 transition-colors text-sm sm:text-base">
                                            Editar
                                        </button>
                                        <button @click="confirmDeleteFigure(figure)"
                                            class="bg-red-600 text-white px-3 py-1 sm:px-4 sm:py-2 rounded hover:bg-red-700 transition-colors text-sm sm:text-base">
                                            Excluir
                                        </button>
                                    </div>
                                </div>

                                <div class="grid grid-cols-1 sm:grid-cols-2 gap-2 sm:gap-4">
                                    <div>
                                        <p class="text-gray-600 text-sm">Preço no site:</p>
                                        <p class="font-semibold">{{ figure.formattedPrice }}</p>
                                    </div>
                                    <div>
                                        <p class="text-gray-600 text-sm">Preço no ML:</p>
                                        <p class="font-semibold">{{ figure.formattedWithoutDiscount }}</p>
                                    </div>
                                </div>

                                <!-- Status tags -->
                                <div class="flex flex-wrap gap-2 mt-2">
                                    <span v-if="figure.verified"
                                        class="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">
                                        Verificado
                                    </span>
                                    <span v-if="figure.updated"
                                        class="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                                        Atualizado
                                    </span>
                                    <span v-if="figure.hidden"
                                        class="px-2 py-1 bg-red-100 text-red-800 text-xs rounded-full">
                                        Oculto
                                    </span>
                                    <span v-if="figure.previously_produced"
                                        class="px-2 py-1 bg-purple-100 text-purple-800 text-xs rounded-full">
                                        Já Produzido
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Paginação responsiva com suporte a swipe -->
                <div class="flex flex-col items-center justify-center gap-2 mt-8">
                    <!-- Indicador de swipe para mobile -->
                    <div class="text-sm text-gray-500 mb-2 sm:hidden">
                        <span>Deslize para navegar entre as páginas</span>
                        <i class="fas fa-arrows-left-right ml-1"></i>
                    </div>

                    <!-- Container com suporte a swipe -->
                    <div
                        v-touch:swipe.left="swipeToNextPage"
                        v-touch:swipe.right="swipeToPrevPage"
                        v-touch:swipe-start="() => isSwipeActive = true"
                        v-touch:swipe-end="() => isSwipeActive = false"
                        :class="{'swipe-active': isSwipeActive}"
                        class="w-full max-w-md overflow-hidden touch-pan-x pagination-container"
                    >
                        <div class="flex flex-wrap items-center justify-center gap-2 relative">
                            <button @click="changePage(adminStore.currentPage - 1)" :disabled="adminStore.currentPage === 1"
                                class="px-3 py-1 rounded border"
                                :class="adminStore.currentPage === 1 ? 'text-gray-400 border-gray-200' : 'text-gray-600 border-gray-300 hover:bg-gray-50'">
                                <span class="sr-only">Anterior</span>
                                <i class="fas fa-chevron-left"></i>
                            </button>

                            <!-- Páginas - versão simplificada para mobile -->
                            <div class="flex items-center flex-wrap gap-1">
                                <!-- Em telas pequenas, mostrar menos botões -->
                                <template v-for="page in totalPages" :key="page">
                                    <!-- Página atual sempre visível -->
                                    <button v-if="page === adminStore.currentPage"
                                        class="px-3 py-1 rounded bg-red-600 text-white">
                                        {{ page }}
                                    </button>
                                    <!-- Em telas pequenas, mostrar apenas primeira, última e adjacentes à atual -->
                                    <button v-else-if="shouldShowPageButton(page)" @click="changePage(page)"
                                        class="px-3 py-1 rounded border border-gray-300 hover:bg-gray-50 hidden sm:block">
                                        {{ page }}
                                    </button>
                                    <!-- Em telas pequenas, mostrar apenas primeira e última página além da atual -->
                                    <button v-else-if="page === 1 || page === totalPages" @click="changePage(page)"
                                        class="px-3 py-1 rounded border border-gray-300 hover:bg-gray-50 sm:hidden">
                                        {{ page }}
                                    </button>
                                    <!-- Indicador de páginas omitidas -->
                                    <span v-else-if="(page === adminStore.currentPage - 2 || page === adminStore.currentPage + 2) && page > 1 && page < totalPages"
                                        class="px-1 hidden sm:block">...</span>
                                    <!-- Indicador simplificado para mobile -->
                                    <span v-else-if="page === 2 && adminStore.currentPage > 3" class="px-1 sm:hidden">...</span>
                                    <span v-else-if="page === totalPages - 1 && adminStore.currentPage < totalPages - 2" class="px-1 sm:hidden">...</span>
                                </template>
                            </div>

                            <button @click="changePage(adminStore.currentPage + 1)"
                                :disabled="adminStore.currentPage === totalPages" class="px-3 py-1 rounded border"
                                :class="adminStore.currentPage === totalPages ? 'text-gray-400 border-gray-200' : 'text-gray-600 border-gray-300 hover:bg-gray-50'">
                                <span class="sr-only">Próximo</span>
                                <i class="fas fa-chevron-right"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Ir para página específica - escondido em telas muito pequenas -->
                    <div class="hidden sm:flex items-center space-x-2 mt-2">
                        <input v-model.number="goToPage" type="number" min="1" :max="totalPages"
                            class="w-16 px-2 py-1 border rounded text-center" @keyup.enter="handleGoToPage" />
                        <button @click="handleGoToPage"
                            class="px-3 py-1 rounded bg-gray-200 text-gray-700 hover:bg-gray-300">
                            Ir
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal de Edição -->
    <Teleport to="body">
        <div v-if="showEditModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div class="bg-white rounded-lg shadow-xl w-full max-w-3xl max-h-screen overflow-auto">
                <!-- Header do Modal -->
                <div class="px-6 py-4 border-b flex justify-between items-center">
                    <h3 class="text-xl font-semibold">Editar Figure</h3>
                    <button @click="showEditModal = false" class="text-gray-500 hover:text-gray-800">
                        <i class="fas fa-times"></i>
                    </button>
                </div>

                <!-- Corpo do Modal -->
                <div class="p-6" v-if="editingFigure">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Campo de Título -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Título</label>
                            <input v-model="editingFigure.title" type="text"
                                class="w-full px-3 py-2 border rounded-md focus:ring-red-500 focus:border-red-500"
                                placeholder="Título da Figure" />
                        </div>
                        <!-- Campo de Alias -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Alias</label>
                            <input v-model="editingFigure.alias" type="text"
                                class="w-full px-3 py-2 border rounded-md focus:ring-blue-500 focus:border-blue-500"
                                placeholder="Alias da Figure" />
                        </div>

                        <!-- Campo de Preço -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Preço (R$)</label>
                            <input v-model.number="editingFigure.base_price" type="number" min="0" step="0.01"
                                class="w-full px-3 py-2 border rounded-md focus:ring-red-500 focus:border-red-500">
                        </div>

                        <!-- Campo de Verificação -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Verificado</label>
                            <div class="flex items-center">
                                <input v-model="editingFigure.verified" type="checkbox"
                                    class="w-4 h-4 text-red-600 border-gray-300 rounded focus:ring-red-500">
                                <span class="ml-2 text-gray-700">Figura verificada</span>
                            </div>
                        </div>

                        <!-- Campo Colored -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Colorido</label>
                            <div class="flex items-center">
                                <input v-model="editingFigure.colored" type="checkbox"
                                    class="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500">
                                <span class="ml-2 text-gray-700">Figura colorida (mesmo que a imagem seja cinza)</span>
                            </div>
                        </div>

                        <!-- Campo Já Produzido Anteriormente -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Já Produzido</label>
                            <div class="flex items-center">
                                <input v-model="editingFigure.previously_produced" type="checkbox"
                                    class="w-4 h-4 text-green-600 border-gray-300 rounded focus:ring-green-500">
                                <span class="ml-2 text-gray-700">Figura já foi produzida anteriormente</span>
                            </div>
                        </div>

                        <!-- Campo Em Estoque -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Em Estoque</label>
                            <div class="flex items-center">
                                <input v-model="editingFigure.in_stock" type="checkbox"
                                    class="w-4 h-4 text-orange-600 border-gray-300 rounded focus:ring-orange-500">
                                <span class="ml-2 text-gray-700">Figura disponível em estoque para envio imediato</span>
                            </div>
                        </div>

                        <!-- Campo Sem Desconto -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Sem Desconto</label>
                            <div class="flex items-center">
                                <input v-model="editingFigure.no_discount" type="checkbox"
                                    class="w-4 h-4 text-red-600 border-gray-300 rounded focus:ring-red-500">
                                <span class="ml-2 text-gray-700">Não aplicar desconto base nesta figura</span>
                            </div>
                        </div>

                        <!-- Campo de Preço sem Desconto -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Preço ML (R$)</label>
                            <input v-model.number="editingFigure.base_price" type="number" min="0" step="0.01"
                                class="w-full px-3 py-2 border rounded-md focus:ring-red-500 focus:border-red-500">
                        </div>

                        <!-- Campo de Descrição -->
                        <div class="md:col-span-2">
                            <label class="block text-sm font-medium text-gray-700 mb-1">Descrição</label>
                            <textarea v-model="editingFigure.description" rows="4"
                                class="w-full px-3 py-2 border rounded-md focus:ring-red-500 focus:border-red-500"></textarea>
                        </div>

                        <!-- Seletor de Categorias -->
                        <div class="md:col-span-2">
                            <label class="block text-sm font-medium text-gray-700 mb-1">Categorias</label>
                            <div class="flex flex-wrap gap-2 mb-2">
                                <span v-for="cat in selectedCategories" :key="cat"
                                    class="px-3 py-1 bg-red-100 text-red-800 rounded-full flex items-center">
                                    {{ getCategoryName(cat) }}
                                    <button @click="removeCategory(cat)" class="ml-2 text-red-600 hover:text-red-800">
                                        <i class="fas fa-times-circle"></i>
                                    </button>
                                </span>
                            </div>
                            <div class="flex flex-col w-full">
                                <!-- Mensagem de sucesso -->
                                <div v-if="categoryCreatedMessage"
                                    class="mb-2 px-3 py-2 bg-green-100 text-green-800 rounded-md flex items-center justify-between">
                                    <span>{{ categoryCreatedMessage }}</span>
                                    <button @click="categoryCreatedMessage = ''" class="text-green-600 hover:text-green-800">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>

                                <div class="flex items-center gap-2">
                                    <input v-model="categorySearch" type="text" placeholder="Buscar ou adicionar categoria..."
                                        class="flex-grow px-3 py-2 border rounded-md focus:ring-red-500 focus:border-red-500">
                                    <button @click="addCategory"
                                        class="bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700 flex-shrink-0">
                                        Adicionar
                                    </button>
                                    <button @click="openNewCategoryModal"
                                        class="bg-green-600 text-white px-3 py-2 rounded-md hover:bg-green-700 flex-shrink-0"
                                        title="Criar nova categoria">
                                        <i class="fas fa-plus"></i>
                                    </button>
                                </div>

                                <!-- Dica de uso -->
                                <div v-if="!categorySearch" class="mt-2 text-sm text-gray-500 bg-blue-50 p-2 rounded">
                                    💡 <strong>Dica:</strong> Digite para buscar categorias existentes ou clique no botão <i class="fas fa-plus text-green-600"></i> para criar uma nova categoria
                                </div>

                                <!-- Lista de categorias existentes -->
                                <div v-if="categorySearch"
                                    class="mt-2 border rounded-md shadow-sm max-h-40 overflow-y-auto">
                                    <div v-if="filteredCategories.length > 0">
                                        <div v-for="category in filteredCategories" :key="category.id"
                                            @click="selectExistingCategory(category)"
                                            class="px-3 py-2 hover:bg-gray-100 cursor-pointer flex items-center justify-between">
                                            <span>{{ category.name }}</span>
                                            <span class="text-xs text-gray-500">{{ category.id }}</span>
                                        </div>
                                    </div>
                                    <!-- Opção para criar nova categoria -->
                                    <div v-if="!categoryMatchesExisting"
                                        @click="openNewCategoryModal"
                                        class="px-3 py-2 hover:bg-green-100 cursor-pointer flex items-center justify-between text-green-700 border-t">
                                        <span>Criar nova categoria: "{{ categorySearch }}"</span>
                                        <i class="fas fa-plus-circle"></i>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Tokens de Busca -->
                        <div class="md:col-span-2">
                            <label class="block text-sm font-medium text-gray-700 mb-1">Tokens de Busca</label>
                            <div class="flex flex-wrap gap-2 mb-2">
                                <span v-for="token in editingFigure.search_tokens" :key="token"
                                    class="px-3 py-1 bg-blue-100 text-blue-800 rounded-full flex items-center">
                                    {{ token }}
                                    <button @click="removeToken(token)" class="ml-2 text-blue-600 hover:text-blue-800">
                                        <i class="fas fa-times-circle"></i>
                                    </button>
                                </span>
                            </div>
                            <div class="flex items-center">
                                <input v-model="newToken" type="text" placeholder="Novo token..."
                                    class="flex-grow px-3 py-2 border rounded-md focus:ring-red-500 focus:border-red-500">
                                <button @click="addToken"
                                    class="ml-2 bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
                                    Adicionar
                                </button>
                            </div>
                        </div>

                        <!-- Upload de Imagens -->
                        <div class="md:col-span-2">
                            <label class="block text-sm font-medium text-gray-700 mb-1">Imagens</label>
                            <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                                <div v-for="(img, index) in editingFigure.gallery_imgs" :key="index"
                                    class="relative aspect-square bg-gray-100 rounded overflow-hidden">
                                    <img :src="img" :alt="`Imagem ${index + 1}`" class="w-full h-full object-contain">
                                    <div class="absolute top-1 right-1 flex space-x-1">
                                        <button @click="setMainImage(index)"
                                            class="bg-blue-600 text-white rounded-full w-6 h-6 flex items-center justify-center"
                                            v-if="index !== 0" title="Definir como imagem principal">
                                            <i class="fas fa-star"></i>
                                        </button>
                                        <button @click="removeImage(index)"
                                            class="bg-red-600 text-white rounded-full w-6 h-6 flex items-center justify-center"
                                            title="Remover imagem">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                    <div v-if="index === 0"
                                        class="absolute top-1 left-1 bg-green-500 text-white text-xs px-2 py-1 rounded-full">
                                        Principal
                                    </div>
                                </div>
                            </div>
                            <div
                                class="flex items-center justify-center p-4 border-2 border-dashed border-gray-300 rounded-md">
                                <label class="cursor-pointer flex flex-col items-center">
                                    <span class="text-sm text-gray-500">Clique para adicionar imagens</span>
                                    <input type="file" multiple @change="handleImageUpload" class="hidden"
                                        accept="image/*">
                                </label>
                            </div>
                        </div>

                        <!-- Upload de Vídeos -->
                        <div class="md:col-span-2 mt-6">
                            <label class="block text-sm font-medium text-gray-700 mb-1">Vídeos</label>
                            <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                                <div v-for="(video, index) in editingFigure.gallery_videos" :key="`video-${index}`"
                                    class="relative aspect-square bg-gray-100 rounded overflow-hidden">
                                    <video class="w-full h-full object-cover" controls>
                                        <source :src="video" type="video/mp4">
                                        Seu navegador não suporta vídeos.
                                    </video>
                                    <div class="absolute top-1 right-1 flex space-x-1">
                                        <button @click="removeVideo(index)"
                                            class="bg-red-600 text-white rounded-full w-6 h-6 flex items-center justify-center"
                                            title="Remover vídeo">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                    <div class="absolute top-1 left-1 bg-blue-500 text-white text-xs px-2 py-1 rounded-full">
                                        Vídeo
                                    </div>
                                </div>
                            </div>
                            <div
                                class="flex items-center justify-center p-4 border-2 border-dashed border-gray-300 rounded-md">
                                <label class="cursor-pointer flex flex-col items-center">
                                    <span class="text-sm text-gray-500">Clique para adicionar vídeos</span>
                                    <span class="text-xs text-gray-400 mt-1">Recomendado: até 100MB por vídeo</span>
                                    <input type="file" multiple @change="handleVideoUpload" class="hidden"
                                        accept="video/*">
                                </label>
                            </div>
                            <div v-if="localLoading.videoUpload" class="mt-2 text-center">
                                <div class="inline-block animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-blue-500 mr-2"></div>
                                <span class="text-sm text-blue-500">Processando vídeos...</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Rodapé do Modal com Botões -->
                <div class="px-6 py-4 border-t bg-gray-50 flex justify-end space-x-4">
                    <button @click="showEditModal = false"
                        class="px-4 py-2 border rounded-md text-gray-700 hover:bg-gray-100">
                        Cancelar
                    </button>
                    <button @click="saveFigure" class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
                        :disabled="localLoading.updateFigure">
                        <span v-if="localLoading.updateFigure" class="flex items-center">
                            <i class="fas fa-spinner fa-spin mr-2"></i> Salvando...
                        </span>
                        <span v-else>Salvar Alterações</span>
                    </button>
                </div>
            </div>
        </div>
    </Teleport>

    <!-- Modal de confirmação de exclusão -->
    <div v-if="showDeleteModal" class="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
        <div class="bg-white rounded-lg shadow-xl w-full max-w-md">
            <div class="p-6">
                <h3 class="text-lg font-semibold mb-4">Confirmar Exclusão</h3>
                <p class="mb-4">Tem certeza que deseja excluir "{{ figureToDelete?.title }}"?</p>

                <div class="mb-4">
                    <label class="flex items-center space-x-2">
                        <input type="checkbox" v-model="deleteImages"
                            class="w-4 h-4 text-red-600 border-gray-300 rounded focus:ring-red-500">
                        <span>Excluir também as imagens do storage</span>
                    </label>
                </div>

                <div class="flex justify-end space-x-4">
                    <button @click="showDeleteModal = false"
                        class="px-4 py-2 border rounded-md text-gray-700 hover:bg-gray-100">
                        Cancelar
                    </button>
                    <button @click="deleteFigure"
                        class="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors">
                        Excluir
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal de criação de categoria -->
    <Teleport to="body">
        <div v-if="showNewCategoryModal" class="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-[100]">
            <div class="bg-white rounded-lg shadow-xl w-full max-w-md">
                <div class="p-6">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-semibold">Criar Nova Categoria</h3>
                        <button @click="showNewCategoryModal = false" class="text-gray-500 hover:text-gray-800">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>

                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-1">Nome da Categoria</label>
                        <input v-model="newCategoryName" type="text"
                            class="w-full px-3 py-2 border rounded-md focus:ring-red-500 focus:border-red-500"
                            placeholder="Digite o nome da categoria">
                    </div>

                    <div class="mb-6">
                        <label class="block text-sm font-medium text-gray-700 mb-1">Categoria Pai (opcional)</label>
                        <select v-model="selectedParentCategory"
                            class="w-full px-3 py-2 border rounded-md focus:ring-red-500 focus:border-red-500">
                            <option :value="null">Nenhuma (categoria raiz)</option>
                            <option v-for="category in categories" :key="category.id" :value="category.id">
                                {{ category.name }}
                            </option>
                        </select>
                    </div>

                    <div class="flex justify-end space-x-4">
                        <button @click="showNewCategoryModal = false"
                            class="px-4 py-2 border rounded-md text-gray-700 hover:bg-gray-100">
                            Cancelar
                        </button>
                        <button @click="createNewCategory"
                            class="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 transition-colors"
                            :disabled="localLoading.createCategory || !newCategoryName.trim()">
                            <span v-if="localLoading.createCategory">
                                <i class="fas fa-spinner fa-spin mr-2"></i>
                                Criando...
                            </span>
                            <span v-else>Criar Categoria</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </Teleport>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watchEffect, watch } from "vue";
import { useRoute, useRouter } from "vue-router";
import { useAdminStore } from "~/stores/adminStore";
import { useStorage } from "~/composables/useStorage";
import type { FormattedFigure } from "~/types/figures";
import FigureCardSkeleton from "~/components/admin/FigureCardSkeleton.vue";
import _ from "lodash";

// Interface de categoria
interface Category {
    id: string;
    name: string;
    fatherId?: string;
}

// Composables e referências
const route = useRoute();
const router = useRouter();
const adminStore = useAdminStore();

// Estados reativos
const categories = ref<Category[]>([]);
const hierarchy = ref<Map<string | undefined, string[]>>(new Map());
const showEditModal = ref(false);
const editingFigure = ref<FormattedFigure | null>(null);
const successMessage = ref("");
const newImages = ref<File[]>([]);
const newVideos = ref<File[]>([]);
const newToken = ref("");
const categorySearch = ref("");
const selectedCategories = ref<string[]>([]);
const categoryCreatedMessage = ref("");
const searchTerm = ref("");
const goToPage = ref<number>(1);
const alertFadeOut = ref(false);
const showUpdatedOnly = ref(false);
const showPreviouslyProducedOnly = ref(false);
const showNewCategoryModal = ref(false);
const newCategoryName = ref("");
const selectedParentCategory = ref<string | null>(null);
const isSwipeActive = ref(false);

// Local loading state para UI
const localLoading = reactive({
    figures: false,
    updateFigure: false,
    imageUpload: false,
    videoUpload: false,
    createCategory: false
});

// Referências para exclusão
const showDeleteModal = ref(false);
const figureToDelete = ref<FormattedFigure | null>(null);
const deleteImages = ref(false);

// Computed properties para paginação
const totalPages = computed(() => {
    if (!adminStore.totalItems) return 1;
    return Math.ceil(adminStore.totalItems / adminStore.PAGE_SIZE);
});

// Mostrar botão de página
const shouldShowPageButton = (page: number) => {
    const current = adminStore.currentPage;
    const total = Math.ceil(adminStore.totalItems / adminStore.PAGE_SIZE);

    // Sempre mostrar primeira e última página
    if (page === 1 || page === total) return true;

    // Mostrar páginas ao redor da página atual
    return Math.abs(page - current) <= 2;
};

// Filtrar figuras com base no termo de busca e no filtro de atualização
const filteredFigures = computed(() => {
    let filtered = adminStore.figures

    if (searchTerm.value) {
        const searchLower = searchTerm.value.toLowerCase()
        filtered = filtered.filter(figure => {
            return (
                figure.title.toLowerCase().includes(searchLower) ||
                figure.idML.includes(searchLower) ||
                (figure.description && figure.description.toLowerCase().includes(searchLower))
            )
        })
    }

    if (showUpdatedOnly.value) {
        filtered = filtered.filter(figure => figure.updated)
    }

    if (showPreviouslyProducedOnly.value) {
        filtered = filtered.filter(figure => figure.previously_produced)
    }

    return filtered
})

// Carregar figures com filtro
const loadFigures = async () => {
    console.log(" Carregando figures...");
    try {
        await adminStore.fetchFigures(
            adminStore.currentPage,
            searchTerm.value
        );
        console.log(" Figures carregadas:", adminStore.figures?.length);
    } catch (e) {
        console.error(" Erro ao carregar figures:", e);
    }
};

// Mudar de página
const changePage = async (page: number) => {
    console.log(" Mudando para página:", page);
    if (page < 1 || page > totalPages.value) return;

    // Adicionar classe de transição para feedback visual
    const paginationContainer = document.querySelector('.pagination-container');
    if (paginationContainer) {
        // Determinar a direção da transição
        const direction = page > adminStore.currentPage ? 'next' : 'prev';
        paginationContainer.classList.add(`page-transition-${direction}`);

        // Remover a classe após a animação
        setTimeout(() => {
            paginationContainer.classList.remove(`page-transition-next`, `page-transition-prev`);
        }, 300);
    }

    try {
        await adminStore.fetchFigures(page, searchTerm.value);
    } catch (e) {
        console.error(" Erro ao mudar de página:", e);
    }
};

// Watch para mudanças na busca
watch(searchTerm, () => {
    debouncedSearch();
});

// Debounce para busca
const debouncedSearch = _.debounce(async () => {
    console.log(" Buscando:", searchTerm.value);
    await adminStore.fetchFigures(1, searchTerm.value);
}, 500);



// Função para abrir modal de edição
const openEditModal = async (figureId: string) => {
    try {
        console.log('Abrindo modal de edição para figure:', figureId);
        // Adicionar verificação de null/undefined
        const figuresArray = adminStore.figures || [];
        const figureToEdit = figuresArray.find((fig) => fig.id === figureId);

        if (figureToEdit) {
            editingFigure.value = { ...figureToEdit } as FormattedFigure;

            // Carregar categorias relacionadas
            if (figureToEdit.categories) {
                selectedCategories.value = [...figureToEdit.categories];
            } else {
                selectedCategories.value = [];
            }

            // Abrir o modal
            showEditModal.value = true;
        } else {
            if (adminStore.error) adminStore.error = "Figure não encontrado!";
        }
    } catch (error) {
        console.error('Erro ao abrir modal de edição:', error);
        if (adminStore.error) adminStore.error = "Erro ao abrir modal de edição. Por favor, tente novamente.";
    }
};

// Definir imagem principal
const setMainImage = (index: number) => {
    if (!editingFigure.value) return;

    // Pegar o item selecionado
    const selected = editingFigure.value.gallery_imgs[index];

    // Remover do array
    editingFigure.value.gallery_imgs.splice(index, 1);

    // Adicionar no início
    editingFigure.value.gallery_imgs.unshift(selected);
};

// Remover imagem
const removeImage = (index: number) => {
    if (!editingFigure.value) return;

    // Criar uma cópia do array de imagens e remover a imagem no índice especificado
    const updatedImages = [...editingFigure.value.gallery_imgs];
    updatedImages.splice(index, 1);

    // Atualizar o array de imagens
    editingFigure.value.gallery_imgs = updatedImages;
};

// Remover vídeo
const removeVideo = (index: number) => {
    if (!editingFigure.value || !editingFigure.value.gallery_videos) return;

    // Criar uma cópia do array de vídeos e remover o vídeo no índice especificado
    const updatedVideos = [...editingFigure.value.gallery_videos];
    updatedVideos.splice(index, 1);

    // Atualizar o array de vídeos
    editingFigure.value.gallery_videos = updatedVideos;
};

// Adicionar novo token
const addToken = () => {
    if (!editingFigure.value) return;

    if (newToken.value && (!editingFigure.value.search_tokens ||
        !editingFigure.value.search_tokens.includes(newToken.value))) {

        if (!editingFigure.value.search_tokens) {
            editingFigure.value.search_tokens = [];
        }

        editingFigure.value.search_tokens.push(newToken.value);
        newToken.value = '';
    }
};

// Remover token
const removeToken = (token: string) => {
    if (!editingFigure.value || !editingFigure.value.search_tokens) return;

    editingFigure.value.search_tokens = editingFigure.value.search_tokens.filter(t => t !== token);
};



// Usar o composable useStorage para upload de arquivos
const { uploadProductFiles } = useStorage();

// Salvar alterações
const saveFigure = async () => {
    try {
        if (!editingFigure.value) return;

        localLoading.updateFigure = true;
        // Limpar erro
        if (adminStore.error) adminStore.error = null;

        // Fazer upload de novas imagens se houver
        if (newImages.value.length > 0) {
            console.log('📸 Iniciando upload de', newImages.value.length, 'imagens');
            try {
                const uploadedImageUrls = await uploadProductFiles(
                    newImages.value,
                    editingFigure.value.idML || editingFigure.value.id,
                    'images'
                );

                // Filtrar URLs temporárias e adicionar as URLs reais
                const tempUrls = newImages.value.map(file => {
                    const reader = new FileReader();
                    return new Promise<string>((resolve) => {
                        reader.onload = (e) => resolve(e.target?.result as string);
                        reader.readAsDataURL(file);
                    });
                });

                const tempUrlsResolved = await Promise.all(tempUrls);

                // Remover URLs temporárias
                const filteredGalleryImgs = editingFigure.value.gallery_imgs.filter(
                    url => !tempUrlsResolved.includes(url)
                );

                // Adicionar URLs reais
                editingFigure.value.gallery_imgs = [
                    ...filteredGalleryImgs,
                    ...uploadedImageUrls
                ];

                console.log('✅ Upload de imagens concluído. URLs:', uploadedImageUrls);
            } catch (error) {
                console.error('❌ Erro ao fazer upload das imagens:', error);
                throw new Error('Falha ao fazer upload das imagens');
            }
        }

        // Fazer upload de novos vídeos se houver
        if (newVideos.value.length > 0) {
            console.log('🎬 Iniciando upload de', newVideos.value.length, 'vídeos');
            try {
                // Mostrar mensagem de progresso
                adminStore.error = `Iniciando upload de ${newVideos.value.length} vídeos. Isso pode levar alguns minutos...`;

                // Processar vídeos um por um para evitar timeouts
                const uploadedVideoUrls: string[] = [];

                for (let i = 0; i < newVideos.value.length; i++) {
                    const file = newVideos.value[i];
                    adminStore.error = `Enviando vídeo ${i+1} de ${newVideos.value.length}: ${file.name} (${(file.size / (1024 * 1024)).toFixed(2)} MB)`;

                    try {
                        // Fazer upload de um vídeo por vez
                        const urls = await uploadProductFiles(
                            [file],
                            editingFigure.value.idML || editingFigure.value.id,
                            'videos'
                        );

                        if (urls && urls.length > 0) {
                            uploadedVideoUrls.push(urls[0]);
                            console.log(`✅ Vídeo ${i+1} enviado com sucesso: ${urls[0]}`);
                        }
                    } catch (videoError) {
                        console.error(`❌ Erro ao enviar vídeo ${i+1}:`, videoError);
                        adminStore.error = `Erro ao enviar vídeo ${i+1}: ${file.name}. Continuando com os demais...`;
                    }
                }

                // Filtrar URLs temporárias e adicionar as URLs reais
                const tempUrls = newVideos.value.map(file => {
                    const reader = new FileReader();
                    return new Promise<string>((resolve) => {
                        reader.onload = (e) => resolve(e.target?.result as string);
                        reader.readAsDataURL(file);
                    });
                });

                const tempUrlsResolved = await Promise.all(tempUrls);

                // Inicializar o array de vídeos se não existir
                if (!editingFigure.value.gallery_videos) {
                    editingFigure.value.gallery_videos = [];
                }

                // Remover URLs temporárias
                const filteredGalleryVideos = editingFigure.value.gallery_videos.filter(
                    url => !tempUrlsResolved.includes(url)
                );

                // Adicionar URLs reais
                editingFigure.value.gallery_videos = [
                    ...filteredGalleryVideos,
                    ...uploadedVideoUrls
                ];

                console.log('✅ Upload de vídeos concluído. URLs:', uploadedVideoUrls);

                if (uploadedVideoUrls.length === newVideos.value.length) {
                    adminStore.error = `✅ Todos os ${uploadedVideoUrls.length} vídeos foram enviados com sucesso!`;
                } else {
                    adminStore.error = `⚠️ ${uploadedVideoUrls.length} de ${newVideos.value.length} vídeos foram enviados com sucesso.`;
                }

                // Limpar a mensagem após alguns segundos
                setTimeout(() => {
                    if (adminStore.error && adminStore.error.includes('vídeos foram enviados com sucesso')) {
                        adminStore.error = '';
                    }
                }, 5000);

            } catch (error) {
                console.error('❌ Erro ao fazer upload dos vídeos:', error);
                adminStore.error = 'Falha ao fazer upload dos vídeos. Por favor, tente novamente com arquivos menores.';
                throw new Error('Falha ao fazer upload dos vídeos');
            }
        }

        // Criar um novo objeto apenas com as propriedades que queremos enviar
        const figureToUpdate = {
            title: editingFigure.value.title,
            base_price: editingFigure.value.base_price,
            description: editingFigure.value.description,
            categories: selectedCategories.value,
            idML: editingFigure.value.idML,
            gallery_imgs: editingFigure.value.gallery_imgs,
            gallery_videos: editingFigure.value.gallery_videos,
            hidden: editingFigure.value.hidden,
            colored: editingFigure.value.colored,
            characteristics: editingFigure.value.characteristics,
            lockedPrice: editingFigure.value.lockedPrice,
            lockedAttrs: editingFigure.value.lockedAttrs,
            lockedImages: editingFigure.value.lockedImages,
            ready_to_ship: editingFigure.value.ready_to_ship,
            search_tokens: editingFigure.value.search_tokens,
            is_exclusive: editingFigure.value.is_exclusive,
            in_stock: editingFigure.value.in_stock,
            release: editingFigure.value.release,
            site_exclusive: editingFigure.value.site_exclusive,
            new_content: editingFigure.value.new_content,
            alias: editingFigure.value.alias,
            verified: editingFigure.value.verified ?? true,
            previously_produced: editingFigure.value.previously_produced ?? false,
            no_discount: editingFigure.value.no_discount ?? false
        };

        debugger

        console.log('Dados que serão enviados para API:', JSON.stringify(figureToUpdate, null, 2));

        const success = await adminStore.updateFigure(
            editingFigure.value.id,
            figureToUpdate
        );

        if (success) {
            console.log('Figure atualizado com sucesso!');
            // Limpar os arrays de arquivos
            newImages.value = [];
            newVideos.value = [];
            showEditModal.value = false;
        } else if (adminStore.error) {
            adminStore.error = "Erro ao salvar figure. Por favor, tente novamente.";
        }
    } catch (error) {
        console.error('Erro ao salvar figure:', error);
        if (adminStore.error) adminStore.error = "Erro ao salvar figure. Por favor, tente novamente.";
    } finally {
        localLoading.updateFigure = false;
    }
};

// Função para lidar com upload de imagens
const handleImageUpload = async (event: Event) => {
    const input = event.target as HTMLInputElement;
    if (!input.files?.length) return;

    localLoading.imageUpload = true;
    console.log(" Upload de imagens iniciado");

    try {
        // Armazena os arquivos para upload posterior
        newImages.value = [...input.files];

        // Cria URLs temporárias para visualização
        const tempUrls = [];
        for (const file of newImages.value) {
            const reader = new FileReader();
            reader.onload = (e) => {
                if (e.target?.result && editingFigure.value) {
                    tempUrls.push(e.target.result as string);
                    editingFigure.value.gallery_imgs = [
                        ...editingFigure.value.gallery_imgs,
                        e.target.result as string,
                    ];
                }
            };
            reader.readAsDataURL(file);
        }

        console.log(" URLs temporárias criadas", tempUrls.length);
    } catch (e) {
        console.error(" Erro ao processar imagens:", e);
    } finally {
        localLoading.imageUpload = false;
    }
};

// Função para lidar com upload de vídeos
const handleVideoUpload = async (event: Event) => {
    const input = event.target as HTMLInputElement;
    if (!input.files?.length) return;

    localLoading.videoUpload = true;
    console.log("🎬 Upload de vídeos iniciado");

    try {
        // Verificar tamanho dos vídeos
        const maxSizeMB = 100; // Tamanho máximo em MB
        const filesToProcess = [];

        for (const file of input.files) {
            const fileSizeMB = file.size / (1024 * 1024);
            console.log(`Verificando vídeo: ${file.name} (${fileSizeMB.toFixed(2)} MB)`);

            if (fileSizeMB > maxSizeMB) {
                console.warn(`⚠️ Vídeo muito grande: ${file.name} (${fileSizeMB.toFixed(2)} MB). Máximo recomendado: ${maxSizeMB} MB`);
                // Mostrar alerta ao usuário
                if (adminStore.error) adminStore.error = `O vídeo ${file.name} é muito grande (${fileSizeMB.toFixed(2)} MB). Recomendamos vídeos de até ${maxSizeMB} MB.`;
                // Continuar mesmo assim
            }

            filesToProcess.push(file);
        }

        // Armazena os arquivos para upload posterior
        newVideos.value = [...filesToProcess];

        // Cria URLs temporárias para visualização
        const tempUrls = [];
        for (const file of newVideos.value) {
            const reader = new FileReader();
            reader.onload = (e) => {
                if (e.target?.result && editingFigure.value) {
                    tempUrls.push(e.target.result as string);

                    // Inicializa o array de vídeos se não existir
                    if (!editingFigure.value.gallery_videos) {
                        editingFigure.value.gallery_videos = [];
                    }

                    editingFigure.value.gallery_videos = [
                        ...editingFigure.value.gallery_videos,
                        e.target.result as string,
                    ];
                }
            };
            reader.readAsDataURL(file);
        }

        console.log("🎬 URLs temporárias de vídeos criadas", tempUrls.length);
    } catch (e) {
        console.error("❌ Erro ao processar vídeos:", e);
        if (adminStore.error) adminStore.error = "Erro ao processar vídeos. Por favor, tente novamente com arquivos menores.";
    } finally {
        localLoading.videoUpload = false;
    }
};

// Função para buscar categorias
const fetchCategories = async (forceRefresh = false) => {
    try {
        console.log('Buscando categorias via API...');
        const url = forceRefresh ? '/api/categories?refresh=true' : '/api/categories';
        const response = await fetch(url);

        if (!response.ok) {
            throw new Error(`Erro ao buscar categorias: ${response.status}`);
        }

        const data = await response.json();
        categories.value = data.map((cat: any) => ({
            id: cat.id,
            name: cat.name || '',
            fatherId: cat.fatherId || undefined
        }));

        console.log('Categorias carregadas:', categories.value);
        hierarchy.value = buildHierarchy(categories.value);
        console.log('Hierarquia construída:', Array.from(hierarchy.value.entries()));
    } catch (error) {
        console.error('Erro ao carregar categorias:', error);
    }
};

// Função para construir hierarquia de categorias
const buildHierarchy = (categories: Category[]) => {
    const hierarchyMap = new Map<string | undefined, string[]>();

    // Inicializa o mapa com uma entrada para categorias raiz
    hierarchyMap.set(undefined, []);

    // Primeiro, inicializa todas as entradas no mapa
    categories.forEach(category => {
        hierarchyMap.set(category.id, []);
    });

    // Depois, preenche as relações pai-filho
    categories.forEach(category => {
        const fatherId = category.fatherId || undefined;
        const children = hierarchyMap.get(fatherId) || [];
        children.push(category.id);
        hierarchyMap.set(fatherId, children);
    });

    return hierarchyMap;
};



// Função para obter o nome da categoria a partir do ID
const getCategoryName = (categoryId: string) => {
    const category = categories.value.find(cat => cat.id === categoryId);
    return category ? category.name : categoryId;
};



// Categorias filtradas
const filteredCategories = computed(() => {
    if (!categorySearch.value) {
        return categories.value;
    }

    // Função para verificar se a busca corresponde a uma categoria
    const matchInCategory = (category: Category) => {
        return category.name.toLowerCase().includes(categorySearch.value.toLowerCase());
    };

    // Filtra mantendo apenas as categorias que correspondem à busca
    return categories.value.filter(matchInCategory);
});

// Verifica se a categoria digitada corresponde exatamente a uma categoria existente
const categoryMatchesExisting = computed(() => {
    if (!categorySearch.value) return false;

    return categories.value.some(
        category => category.name.toLowerCase() === categorySearch.value.toLowerCase()
    );
});

// Função para abrir o modal de criação de categoria
const openNewCategoryModal = () => {
    // Preencher o nome da categoria com o texto da busca
    newCategoryName.value = categorySearch.value.trim();
    selectedParentCategory.value = null;
    showNewCategoryModal.value = true;
};

// Função para criar uma nova categoria
const createNewCategory = async () => {
    if (!newCategoryName.value.trim()) return;

    try {
        localLoading.createCategory = true;

        // Gerar ID a partir do nome (kebab-case)
        const categoryId = newCategoryName.value.trim()
            .toLowerCase()
            .replace(/\s+/g, '-')
            .replace(/[^a-z0-9-]/g, '');

        // Preparar dados da categoria
        const categoryData = {
            id: categoryId,
            name: newCategoryName.value.trim(),
            fatherId: selectedParentCategory.value,
            description: ''
        };

        console.log('Criando nova categoria:', categoryData);

        // Enviar para a API
        const response = await fetch('/api/categories', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(categoryData)
        });

        if (!response.ok) {
            throw new Error(`Erro ao criar categoria: ${response.status}`);
        }

        const newCategory = await response.json();
        console.log('Categoria criada com sucesso:', newCategory);

        // Recarregar todas as categorias para garantir consistência
        await fetchCategories(true);

        // Adicionar à lista de categorias selecionadas
        if (!selectedCategories.value.includes(newCategory.id)) {
            // Adicionar a nova categoria
            selectedCategories.value.push(newCategory.id);

            // Se a categoria tiver um pai, adicionar o pai também
            if (newCategory.fatherId && !selectedCategories.value.includes(newCategory.fatherId)) {
                // Encontrar todas as categorias pai
                const parentCategories = findAllParentCategories(newCategory.id);

                // Adicionar todas as categorias pai que ainda não estão selecionadas
                parentCategories.forEach(catId => {
                    if (!selectedCategories.value.includes(catId)) {
                        selectedCategories.value.push(catId);
                    }
                });
            }
        }

        // Limpar campos
        categorySearch.value = '';
        newCategoryName.value = '';
        selectedParentCategory.value = null;

        // Fechar o modal
        showNewCategoryModal.value = false;

        // Exibir mensagem de sucesso
        categoryCreatedMessage.value = `Categoria "${newCategory.name}" criada com sucesso!`;

        // Limpar a mensagem após 3 segundos
        setTimeout(() => {
            categoryCreatedMessage.value = '';
        }, 3000);

    } catch (error) {
        console.error('Erro ao criar categoria:', error);
        if (adminStore.error) adminStore.error = "Erro ao criar categoria. Por favor, tente novamente.";
    } finally {
        localLoading.createCategory = false;
    }
};

// Função para ir para uma página específica
const handleGoToPage = () => {
    if (goToPage.value && goToPage.value >= 1 && goToPage.value <= totalPages.value) {
        changePage(goToPage.value);
    } else {
        // Resetar para a página atual se o valor for inválido
        goToPage.value = adminStore.currentPage;
    }
};

// Funções para navegação por swipe
const swipeToNextPage = () => {
    if (adminStore.currentPage < totalPages.value) {
        changePage(adminStore.currentPage + 1);
    }
};

const swipeToPrevPage = () => {
    if (adminStore.currentPage > 1) {
        changePage(adminStore.currentPage - 1);
    }
};

// Inicializar dados
onMounted(async () => {
    const pageParam = route.query.page;
    if (pageParam && !isNaN(Number(pageParam))) {
        adminStore.currentPage = Number(pageParam);
    }

    await Promise.all([
        loadFigures(),
        fetchCategories(),
    ]);
});

// Monitorar mudanças na página atual
watch(() => adminStore.currentPage, (newPage) => {
    router.push({
        query: {
            ...route.query,
            page: newPage.toString()
        }
    });
});

// Watch para fade out do erro
watchEffect(() => {
    if (adminStore.error) {
        alertFadeOut.value = false;
        setTimeout(() => {
            alertFadeOut.value = true;
        }, 3000);
    }
});

// Funções para gerenciar categorias
const addCategory = () => {
    if (categorySearch.value && !selectedCategories.value.includes(categorySearch.value)) {
        // Verificar se existe uma categoria com esse nome
        const existingCategory = categories.value.find(
            cat => cat.name.toLowerCase() === categorySearch.value.toLowerCase()
        );

        if (existingCategory) {
            // Se existir, adiciona o ID da categoria e suas categorias pai
            if (!selectedCategories.value.includes(existingCategory.id)) {
                // Encontrar todas as categorias pai
                const parentCategories = findAllParentCategories(existingCategory.id);

                // Adicionar a categoria selecionada e todas as suas categorias pai
                const categoriesToAdd = [existingCategory.id, ...parentCategories];

                // Adicionar apenas as categorias que ainda não estão selecionadas
                categoriesToAdd.forEach(catId => {
                    if (!selectedCategories.value.includes(catId)) {
                        selectedCategories.value.push(catId);
                    }
                });
            }
        } else {
            // Se não existir, adiciona o texto como está
            selectedCategories.value.push(categorySearch.value);
        }

        categorySearch.value = '';
    }
};

// Função para encontrar todas as categorias pai de uma categoria
const findAllParentCategories = (categoryId: string): string[] => {
    const parentIds: string[] = [];
    let currentCategory = categories.value.find(c => c.id === categoryId);

    while (currentCategory?.fatherId) {
        parentIds.push(currentCategory.fatherId);
        const fatherId = currentCategory.fatherId;
        currentCategory = categories.value.find(c => c.id === fatherId);
    }

    return parentIds;
};

// Função para selecionar uma categoria existente da lista
const selectExistingCategory = (category: Category) => {
    if (!selectedCategories.value.includes(category.id)) {
        // Encontrar todas as categorias pai
        const parentCategories = findAllParentCategories(category.id);

        // Adicionar a categoria selecionada e todas as suas categorias pai
        const categoriesToAdd = [category.id, ...parentCategories];

        // Adicionar apenas as categorias que ainda não estão selecionadas
        categoriesToAdd.forEach(catId => {
            if (!selectedCategories.value.includes(catId)) {
                selectedCategories.value.push(catId);
            }
        });

        categorySearch.value = '';
    }
};

const removeCategory = (category: string) => {
    selectedCategories.value = selectedCategories.value.filter(c => c !== category);
};

// Função para abrir modal de confirmação de exclusão
const confirmDeleteFigure = (figure: FormattedFigure) => {
    figureToDelete.value = figure;
    showDeleteModal.value = true;
};

// Função para excluir figure
const deleteFigure = async () => {
    if (!figureToDelete.value) return;

    try {
        // Enviar para a API
        await $fetch(`/api/products/${figureToDelete.value.id}`, {
            method: 'DELETE',
            body: {
                deleteImages: deleteImages.value
            }
        });

        showDeleteModal.value = false;
        figureToDelete.value = null;
        deleteImages.value = false;
        successMessage.value = 'Figure excluída com sucesso!';

        // Recarregar figures
        await loadFigures();
    } catch (error) {
        console.error('Erro ao excluir figure:', error);
        adminStore.error = 'Erro ao excluir figure. Tente novamente.';
    }
};
</script>

<style scoped>
.animate-fade-out {
    animation: fadeOut 3s forwards;
}

@keyframes fadeOut {
    0% {
        opacity: 1;
    }

    70% {
        opacity: 1;
    }

    100% {
        opacity: 0;
    }
}

/* Estilos para o swipe na paginação */
.swipe-active {
    cursor: grabbing;
    background-color: rgba(239, 68, 68, 0.05);
    border-radius: 0.5rem;
    transition: background-color 0.2s ease;
}

/* Animações de transição de página */
.pagination-container {
    position: relative;
    transition: transform 0.3s ease, opacity 0.3s ease;
}

.page-transition-next {
    animation: slideLeft 0.3s ease;
}

.page-transition-prev {
    animation: slideRight 0.3s ease;
}

@keyframes slideLeft {
    0% {
        transform: translateX(0);
        opacity: 1;
    }
    50% {
        transform: translateX(-10px);
        opacity: 0.5;
    }
    100% {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideRight {
    0% {
        transform: translateX(0);
        opacity: 1;
    }
    50% {
        transform: translateX(10px);
        opacity: 0.5;
    }
    100% {
        transform: translateX(0);
        opacity: 1;
    }
}

@media (max-width: 640px) {
    .touch-pan-x {
        position: relative;
    }

    .touch-pan-x::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(90deg, rgba(239, 68, 68, 0.1) 0%, transparent 10%, transparent 90%, rgba(239, 68, 68, 0.1) 100%);
        opacity: 0;
        transition: opacity 0.3s ease;
        pointer-events: none;
        z-index: 1;
    }

    .touch-pan-x:active::before {
        opacity: 1;
    }
}
</style>
