import { doc, getDoc, updateDoc, arrayUnion, Timestamp } from "firebase/firestore";
import { db } from "~/server/utils/firebase";
import { v4 as uuidv4 } from "uuid";

const USERS_COLLECTION = "users";

/**
 * Endpoint para criar um pedido de teste para avaliação
 * POST /api/debug/create-test-order
 * APENAS PARA DESENVOLVIMENTO/TESTE
 */
export default defineEventHandler(async (event) => {
  try {
    const body = await readBody(event);
    const { userId, productId } = body;
    
    if (!userId) {
      throw createError({
        statusCode: 400,
        statusMessage: "ID do usuário é obrigatório",
      });
    }
    
    if (!productId) {
      throw createError({
        statusCode: 400,
        statusMessage: "ID do produto é obrigatório",
      });
    }
    
    console.log(`🧪 [DEBUG] Criando pedido de teste para usuário ${userId} e produto ${productId}`);
    
    // Verificar se o usuário existe
    const userRef = doc(db, USERS_COLLECTION, userId);
    const userDoc = await getDoc(userRef);
    
    if (!userDoc.exists()) {
      throw createError({
        statusCode: 404,
        statusMessage: "Usuário não encontrado",
      });
    }
    
    // Criar pedido de teste
    const testOrder = {
      id: uuidv4(),
      createdAt: new Date(), // Data atual para permitir avaliação imediata
      updatedAt: new Date(),
      paymentId: `TEST_${Date.now()}`,
      collectionId: `TEST_COLLECTION_${Date.now()}`,
      collectionStatus: "approved", // Status que permite avaliação
      status: "approved", // Status que permite avaliação
      paymentType: "test",
      externalReference: "TEST_REF",
      merchantOrderId: `TEST_MERCHANT_${Date.now()}`,
      preferenceId: `TEST_PREF_${Date.now()}`,
      siteId: "TEST_SITE",
      processingMode: "test",
      merchantAccountId: "TEST_ACCOUNT",
      customerId: userId,
      productId: productId,
      paymentMethod: "test",
      total: 99.99,
      quantity: 1,
      paymentProvider: "test",
      isTest: true, // Marcar como pedido de teste
    };
    
    // Adicionar o pedido ao usuário
    await updateDoc(userRef, {
      orders: arrayUnion(testOrder),
      updatedAt: Timestamp.now(),
    });
    
    console.log(`✅ [DEBUG] Pedido de teste criado com sucesso: ${testOrder.id}`);
    
    return {
      success: true,
      message: "Pedido de teste criado com sucesso",
      order: {
        ...testOrder,
        createdAt: testOrder.createdAt.toISOString(),
        updatedAt: testOrder.updatedAt.toISOString(),
      },
    };
    
  } catch (error: any) {
    console.error("❌ [DEBUG] Erro ao criar pedido de teste:", error);
    
    if (error.statusCode) {
      throw error;
    }
    
    throw createError({
      statusCode: 500,
      statusMessage: "Erro interno do servidor ao criar pedido de teste",
    });
  }
});
