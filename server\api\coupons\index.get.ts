import { createError } from "h3";
import { collection, getDocs } from "firebase/firestore";
import { db } from "~/server/utils/firebase";
import { formatCouponData } from "~/types/coupons";

const COUPONS_COLLECTION = "coupons";

// Cache em memória para cupons
const CACHE_EXPIRATION_TIME = 60 * 60 * 1000; // 1 hora em milissegundos
let couponsCache = {
  data: null,
  timestamp: 0
};

/**
 * Rota para listar todos os cupons
 * GET /api/coupons
 */
export default defineEventHandler(async (event) => {
  try {
    // Obter parâmetros de consulta
    const query = getQuery(event);
    const forceRefresh = query.refresh === 'true';

    // Verificar se podemos usar o cache
    const isCacheValid =
      !forceRefresh &&
      couponsCache.data !== null &&
      (Date.now() - couponsCache.timestamp) < CACHE_EXPIRATION_TIME;

    let coupons = [];

    // Se o cache for válido, use o cache
    if (isCacheValid) {
      console.log('🔍 [API] Usando cache de cupons');
      coupons = couponsCache.data;
    } else {
      console.log("🔍 [API] Buscando todos os cupons do Firebase");

      // Buscar todos os cupons
      const couponsRef = collection(db, COUPONS_COLLECTION);
      const couponsSnapshot = await getDocs(couponsRef);

      // Formatar os cupons
      coupons = couponsSnapshot.docs.map((doc) => {
        const data = doc.data();
        return formatCouponData({
          ...data,
          id: doc.id,
          formattedProducts: []
        } as any, doc.id);
      });

      // Atualizar o cache
      couponsCache.data = coupons;
      couponsCache.timestamp = Date.now();
      console.log('🔍 [API] Cache de cupons atualizado');
    }

    console.log(`✅ [API] ${coupons.length} cupons encontrados`);

    return { coupons };
  } catch (error: any) {
    console.error("u274c [API] Erro ao buscar cupons:", error);
    throw createError({
      statusCode: 500,
      statusMessage: "Erro ao buscar cupons",
    });
  }
});
