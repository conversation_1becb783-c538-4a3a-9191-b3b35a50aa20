<template>
  <section class="relative py-16 bg-gradient-to-b from-neutral-100 to-white">
    <div class="container mx-auto px-4">
      <h2 class="text-3xl font-bold text-center mb-8">ÚLTIMOS LANÇAMENTOS</h2>

      <div v-if="loading" class="relative">
        <!-- Slider Container com skeletons -->
        <div class="flex gap-4 overflow-x-auto pb-6 snap-x snap-mandatory">
          <ReleaseCardSkeleton v-for="i in 4" :key="i" />
        </div>
      </div>

      <div v-else-if="error" class="text-center text-red-600 min-h-[300px] flex items-center justify-center">
        <p>{{ error }}</p>
      </div>

      <div v-else-if="items.length === 0" class="text-center text-gray-600 min-h-[300px] flex items-center justify-center">
        <p>Nenhum lançamento disponível no momento.</p>
      </div>

      <div v-else class="relative">
        <!-- Slider Container -->
        <div ref="sliderRef" class="flex gap-4 overflow-x-auto pb-6 snap-x snap-mandatory">
          <div v-for="(item, index) in items" :key="index" class="min-w-[250px] max-w-[250px] flex-shrink-0 snap-start">
            <div class="bg-white rounded-lg shadow-lg overflow-hidden">
              <div class="relative aspect-[4/5]">
                <img :src="item.image"
                     :alt="item.title"
                     class="w-full h-full object-cover">
                <div class="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent"></div>
                <div class="absolute bottom-0 left-0 right-0 p-3">
                  <h3 class="text-white text-base font-bold mb-1 line-clamp-2">{{ item.title }}</h3>
                  <p class="text-white/80 text-xs mb-3 line-clamp-2">{{ item.description }}</p>
                  <button class="bg-red-600 text-white px-3 py-1.5 rounded-md hover:bg-red-700 transition-colors w-full text-sm">
                    {{ item.buttonText }}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Navigation Buttons -->
        <button
          v-if="items.length > 0"
          @click="scrollSlider('left')"
          class="absolute left-0 top-1/2 -translate-y-1/2 bg-white/80 p-2 rounded-full shadow-lg hover:bg-white transition-colors -ml-4"
        >
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6">
            <path stroke-linecap="round" stroke-linejoin="round" d="M15.75 19.5L8.25 12l7.5-7.5" />
          </svg>
        </button>
        <button
          v-if="items.length > 0"
          @click="scrollSlider('right')"
          class="absolute right-0 top-1/2 -translate-y-1/2 bg-white/80 p-2 rounded-full shadow-lg hover:bg-white transition-colors -mr-4"
        >
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6">
            <path stroke-linecap="round" stroke-linejoin="round" d="M8.25 4.5l7.5 7.5-7.5 7.5" />
          </svg>
        </button>
      </div>

      <!-- See All Button -->
      <div v-if="items.length > 0" class="text-center mt-8">
        <button @click="$emit('seeAll')" class="bg-neutral-800 text-white px-8 py-3 rounded-md hover:bg-neutral-700 transition-colors inline-flex items-center gap-2">
          Ver Todos
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4">
            <path stroke-linecap="round" stroke-linejoin="round" d="M8.25 4.5l7.5 7.5-7.5 7.5M21 12H3" />
          </svg>
        </button>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import ReleaseCardSkeleton from './ReleaseCardSkeleton.vue';

interface ReleaseItem {
  image: string;
  title: string;
  description: string;
  buttonText: string;
}

const props = withDefaults(defineProps<{
  items: ReleaseItem[];
  loading?: boolean;
  error?: string | null;
}>(), {
  loading: false,
  error: null
});

defineEmits<{
  (e: 'seeAll'): void;
}>();

const sliderRef = ref(null);

const scrollSlider = (direction: 'left' | 'right') => {
  if (!sliderRef.value) return;

  const scrollAmount = 300;
  const container = sliderRef.value;

  if (direction === 'left') {
    container.scrollBy({ left: -scrollAmount, behavior: 'smooth' });
  } else {
    container.scrollBy({ left: scrollAmount, behavior: 'smooth' });
  }
};
</script>
