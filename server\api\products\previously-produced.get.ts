import { createError } from "h3";
import { collection, getDocs, query as firebaseQuery, where } from "firebase/firestore";
import { db } from "~/server/utils/firebase";
import { FigureData, formatFigureData } from "~/types/figures";
import { Coupon } from "~/types/coupons";
import { CACHE_EXPIRATION_TIME, getCachedCoupons, isCacheValid } from "~/server/utils/cache";

// Cache em memória para produtos previamente produzidos
let previouslyProducedCache = {
  data: null,
  timestamp: 0
};

/**
 * Rota para buscar produtos que já foram produzidos anteriormente
 * GET /api/products/previously-produced
 */
export default defineEventHandler(async (event) => {
  try {
    // Obter parâmetros de consulta
    const queryParams = getQuery(event);
    const forceRefresh = queryParams.refresh === 'true';

    // Verificar se podemos usar o cache
    const cacheValid = isCacheValid(previouslyProducedCache, forceRefresh);

    // Se o cache for válido, use o cache
    if (cacheValid) {
      console.log('🔍 [API] Usando cache de produtos previamente produzidos');
      return previouslyProducedCache.data;
    }

    console.log(`🔍 [API] Buscando todos os produtos já produzidos anteriormente`);

    // Buscar produtos
    const productsRef = collection(db, "products");

    // Criar uma consulta para produtos previamente produzidos
    const productsQuery = firebaseQuery(
      productsRef,
      where("previously_produced", "==", true)
    );

    const querySnapshot = await getDocs(productsQuery);

    // Buscar cupons ativos para aplicar aos produtos usando o cache compartilhado
    const coupons = await getCachedCoupons();

    // Processar os resultados
    console.log(`🔍 [API] Processando ${querySnapshot.docs.length} documentos`);

    const products = querySnapshot.docs
      .filter(doc => {
        // Filtrar produtos com hidden = true
        const data = doc.data();
        if (data.hidden === true) {
          console.log(`⚠️ [API] Produto ${doc.id} está oculto`);
          return false;
        }
        return true;
      })
      .map(doc => {
        const figureData = { ...(doc.data() as FigureData) };
        console.log(`✅ [API] Produto ${doc.id} já foi produzido anteriormente`);

        // Formatar o produto
        return formatFigureData(figureData, doc.id, {
          coupons,
          ignoreCoupons: coupons.length === 0,
        });
      });

    console.log(`✅ [API] Retornando ${products.length} produtos já produzidos`);

    // Atualizar o cache
    previouslyProducedCache.data = products;
    previouslyProducedCache.timestamp = Date.now();
    console.log('🔍 [API] Cache de produtos previamente produzidos atualizado');

    return products;
  } catch (error: any) {
    console.error("❌ [API] Erro ao buscar produtos já produzidos:", error);
    throw createError({
      statusCode: error.statusCode || 500,
      message: "Erro ao buscar produtos já produzidos"
    });
  }
});
