import { collection, getDocs, getCountFromServer, query, where } from "firebase/firestore";
import { db } from "~/server/utils/firebase";

const USERS_COLLECTION = "users";
const REVIEWS_COLLECTION = "reviews";

/**
 * Endpoint para verificar o status da migração de avaliações
 * GET /api/admin/migration-status
 */
export default defineEventHandler(async (event) => {
  try {
    console.log(`🔍 [Migration Status] Verificando status da migração`);

    // Contar avaliações na nova coleção
    const reviewsRef = collection(db, REVIEWS_COLLECTION);
    const newReviewsCount = await getCountFromServer(reviewsRef);

    // Contar usuários com avaliações no formato antigo
    const usersRef = collection(db, USERS_COLLECTION);
    const usersSnapshot = await getDocs(usersRef);

    let usersWithOldReviews = 0;
    let totalOldReviews = 0;
    const usersWithReviewsDetails: any[] = [];

    usersSnapshot.forEach((userDoc) => {
      const userData = userDoc.data();
      if (userData.reviews && Array.isArray(userData.reviews) && userData.reviews.length > 0) {
        usersWithOldReviews++;
        totalOldReviews += userData.reviews.length;
        
        usersWithReviewsDetails.push({
          userId: userDoc.id,
          userName: userData.name || 'Usuário Anônimo',
          reviewCount: userData.reviews.length,
          reviews: userData.reviews.map((review: any) => ({
            id: review.id,
            productId: review.productId,
            rating: review.rating,
            createdAt: review.createdAt,
          })),
        });
      }
    });

    // Verificar se há avaliações duplicadas (mesmo userId + productId)
    const duplicateCheck: any[] = [];
    if (newReviewsCount.data().count > 0) {
      const allNewReviews = await getDocs(reviewsRef);
      const reviewMap = new Map();
      
      allNewReviews.forEach((doc) => {
        const data = doc.data();
        const key = `${data.userId}-${data.productId}`;
        
        if (reviewMap.has(key)) {
          duplicateCheck.push({
            key,
            reviews: [reviewMap.get(key), { id: doc.id, ...data }],
          });
        } else {
          reviewMap.set(key, { id: doc.id, ...data });
        }
      });
    }

    const migrationStatus = {
      newCollectionReviews: newReviewsCount.data().count,
      oldFormatReviews: totalOldReviews,
      usersWithOldReviews,
      totalUsers: usersSnapshot.size,
      migrationComplete: totalOldReviews === 0 && newReviewsCount.data().count > 0,
      hasDuplicates: duplicateCheck.length > 0,
      duplicateCount: duplicateCheck.length,
      
      // Detalhes para debug
      usersWithReviewsDetails: usersWithReviewsDetails.slice(0, 5), // Primeiros 5 para não sobrecarregar
      duplicates: duplicateCheck.slice(0, 5), // Primeiros 5 duplicados
      
      // Recomendações
      recommendations: [],
    };

    // Gerar recomendações baseadas no status
    if (totalOldReviews > 0 && newReviewsCount.data().count === 0) {
      migrationStatus.recommendations.push("Migração ainda não foi executada. Execute primeiro em modo dry-run.");
    } else if (totalOldReviews > 0 && newReviewsCount.data().count > 0) {
      migrationStatus.recommendations.push("Migração parcial detectada. Verifique se houve erros na migração anterior.");
    } else if (totalOldReviews === 0 && newReviewsCount.data().count > 0) {
      migrationStatus.recommendations.push("Migração concluída com sucesso!");
    } else if (totalOldReviews === 0 && newReviewsCount.data().count === 0) {
      migrationStatus.recommendations.push("Nenhuma avaliação encontrada em ambos os formatos.");
    }

    if (duplicateCheck.length > 0) {
      migrationStatus.recommendations.push(`${duplicateCheck.length} avaliações duplicadas detectadas. Considere executar limpeza.`);
    }

    console.log(`✅ [Migration Status] Status verificado:`, {
      newReviews: newReviewsCount.data().count,
      oldReviews: totalOldReviews,
      complete: migrationStatus.migrationComplete,
    });

    return {
      success: true,
      data: migrationStatus,
    };

  } catch (error: any) {
    console.error("❌ [Migration Status] Erro ao verificar status:", error);
    
    throw createError({
      statusCode: 500,
      statusMessage: "Erro interno ao verificar status da migração",
    });
  }
});
