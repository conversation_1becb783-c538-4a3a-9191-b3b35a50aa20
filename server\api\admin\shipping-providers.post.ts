import { collection, doc, setDoc, Timestamp } from "firebase/firestore";
import { db } from "~/server/utils/firebase";
import type { IShippingProvider } from "~/types/customer";

const SHIPPING_PROVIDERS_COLLECTION = "shippingProviders";

/**
 * Rota para criar ou atualizar uma transportadora
 * POST /api/admin/shipping-providers
 */
export default defineEventHandler(async (event) => {
  try {
    const body = await readBody(event);
    
    console.log('📝 [API] Criando/atualizando transportadora:', body);

    // Validar dados obrigatórios
    if (!body.name || !body.code || !body.trackingUrl) {
      throw createError({
        statusCode: 400,
        statusMessage: "Nome, código e URL de rastreamento são obrigatórios",
      });
    }

    // Validar URL de rastreamento
    try {
      new URL(body.trackingUrl);
    } catch {
      throw createError({
        statusCode: 400,
        statusMessage: "URL de rastreamento inválida",
      });
    }

    // Preparar dados da transportadora
    const providerData = {
      name: body.name.trim(),
      code: body.code.trim().toUpperCase(),
      trackingUrl: body.trackingUrl.trim(),
      isActive: body.isActive !== undefined ? body.isActive : true,
      updatedAt: Timestamp.now()
    };

    // Se tem ID, é uma atualização; senão, é criação
    let providerId: string;
    
    if (body.id && !body.id.startsWith('custom_temp_')) {
      // Atualização de transportadora existente
      providerId = body.id;
      console.log(`🔄 [API] Atualizando transportadora existente: ${providerId}`);
    } else {
      // Criação de nova transportadora
      providerId = `custom_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      providerData.createdAt = Timestamp.now();
      console.log(`➕ [API] Criando nova transportadora: ${providerId}`);
    }

    // Salvar no Firebase
    const providerRef = doc(db, SHIPPING_PROVIDERS_COLLECTION, providerId);
    await setDoc(providerRef, providerData, { merge: true });

    // Retornar dados da transportadora criada/atualizada
    const savedProvider: IShippingProvider = {
      id: providerId,
      name: providerData.name,
      code: providerData.code,
      trackingUrl: providerData.trackingUrl,
      isActive: providerData.isActive,
      createdAt: providerData.createdAt?.toDate() || new Date(),
      updatedAt: providerData.updatedAt.toDate()
    };

    console.log(`✅ [API] Transportadora ${body.id ? 'atualizada' : 'criada'} com sucesso: ${providerId}`);

    return {
      success: true,
      data: savedProvider,
      message: `Transportadora ${body.id ? 'atualizada' : 'criada'} com sucesso`
    };

  } catch (error) {
    console.error('❌ [API] Erro ao salvar transportadora:', error);
    
    // Se é um erro conhecido, repassar
    if (error.statusCode) {
      throw error;
    }
    
    throw createError({
      statusCode: 500,
      statusMessage: "Erro interno do servidor ao salvar transportadora",
    });
  }
});
