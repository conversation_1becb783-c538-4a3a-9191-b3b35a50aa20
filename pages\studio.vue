<template>
  <div class="min-h-screen bg-gray-100">
    <!-- Hero Section -->
    <div class="relative h-[400px] lg:h-[500px]">
      <img
        src="@/assets/images/ka<PERSON>hi_vs_obito.webp"
        alt="<PERSON><PERSON><PERSON> vs Obito"
        class="w-full h-full object-cover"
      />
      <div
        class="absolute inset-0 bg-gradient-to-t from-black/80 via-black/50 to-transparent"
      >
        <div class="container mx-auto px-4 h-full flex items-end pb-12">
          <div class="text-white">
            <h1 class="text-4xl lg:text-5xl font-bold mb-4">
              Exclusivas do Estúdio Fidel Figures
            </h1>
            <p class="text-lg lg:text-xl max-w-2xl">
              Peças únicas e exclusivas que você não encontrará em nenhum outro
              lugar.
            </p>
          </div>
        </div>
      </div>
    </div>

    <!-- <PERSON><PERSON><PERSON><PERSON> Principal -->
    <div class="container mx-auto px-4 py-12">
      <!-- <PERSON><PERSON> de Figuras Exclusivas -->
      <section class="mb-16">
        <h2 class="text-3xl font-bold mb-8">Figuras Exclusivas</h2>

        <!-- Error state -->
        <div v-if="error" class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6">
          <strong class="font-bold">Erro!</strong>
          <span class="block sm:inline"> {{ error }}</span>
        </div>

        <!-- Content -->
        <div v-else>
          <FiguresList
            :figures="studioFigures"
            :loading="loading"
            :error="error"
            :hide-overlay="false"
          />
        </div>
      </section>


      <!-- Seção de Contato -->
      <section class="bg-gray-900 text-white rounded-lg p-8">
        <div class="max-w-3xl mx-auto text-center">
          <h2 class="text-3xl font-bold mb-4">
            Interessado em uma Peça Exclusiva?
          </h2>
          <p class="text-lg mb-6">
            Entre em contato conosco para discutir seu projeto ou saber mais
            sobre nossas peças disponíveis.
          </p>
          <button
            class="bg-red-600 hover:bg-red-700 text-white px-8 py-3 rounded-md transition-colors"
          >
            Falar com um Especialista
          </button>
        </div>
      </section>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from "vue";
import HeaderBar from "~/components/HeaderBar.vue";
import FiguresList from "~/components/FiguresList.vue";
import { useProductsStore } from "~/stores/productsStore";
import type { FormattedFigure } from "~/types/figures";

const studioFigures = ref<FormattedFigure[]>([]);
const loading = ref(true);
const error = ref<string | null>(null);
const productsStore = useProductsStore();

onMounted(async () => {
  try {
    loading.value = true;
    error.value = null;
    console.log("[studio] Carregando produtos exclusivos");

    const result = await productsStore.fetchExclusiveProducts();
    studioFigures.value = result;

    console.log(
      `[studio] ${studioFigures.value.length} produtos exclusivos carregados`
    );
  } catch (err: any) {
    console.error("[studio] Erro ao carregar produtos exclusivos:", err);
    error.value = "Erro ao carregar figuras exclusivas. Por favor, tente novamente mais tarde.";
  } finally {
    loading.value = false;
  }
});
</script>
