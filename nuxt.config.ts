// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
  ssr: true,
  compatibilityDate: '2024-11-01',
  runtimeConfig: {
    public: {
      PUBLIC_MERCADOPAGO_PUBLIC_KEY: process.env.PUBLIC_MERCADOPAGO_PUBLIC_KEY,
      PRODUCTION_TIME: process.env.PRODUCTION_TIME,
      PRODUCTION_START_DATE: process.env.PRODUCTION_START_DATE,
      GOOGLE_CLIENT_ID: process.env.GOOGLE_CLIENT_ID,
      // Make sure Stripe public key is available
      STRIPE_PUBLIC_KEY: process.env.STRIPE_PUBLIC_KEY,
      BASE_DISCOUNT_PERCENTAGE: process.env.BASE_DISCOUNT_PERCENTAGE || '0',
    },
    // Server-side environment variables
    PT: process.env.PT,
    MERCADO_PAGO_ACCESS_TOKEN: process.env.MERCADO_PAGO_ACCESS_TOKEN,
    STRIPE_SECRET_KEY: process.env.STRIPE_SECRET_KEY,
    STRIPE_WEBHOOK_SECRET: process.env.STRIPE_WEBHOOK_SECRET,
    GOOGLE_APP_PASSWORD: process.env.GOOGLE_APP_PASSWORD,
    WHATSAPP_API_TOKEN: process.env.WHATSAPP_API_TOKEN
  },
  devServer: {
    port: 4001
  },
  nitro: {
    experimental: {
      wasm: true
    },
    // Configurações para APIs de longa duração
    routeRules: {
      '/api/admin/update-tokens': {
        headers: { 'cache-control': 'no-cache' },
        prerender: false
      }
    }
  },
  devtools: { enabled: true },
  css: ['~/assets/css/main.css'],
  modules: [
    '@pinia/nuxt',
    [
      '@pinia-plugin-persistedstate/nuxt',
      {
        cookieOptions: {
          sameSite: 'strict'
        },
        storage: 'localStorage'
      }
    ],
    '@nuxtjs/tailwindcss',
    '@nuxtjs/robots',
    '@nuxt/image'
  ],
  imports: {
    dirs: ['stores']
  },
  app: {
    head: {
      meta: [
        { name: 'google-site-verification', content: 'ThD_kEbeg85IV1ePexkATbhh6FhUkibf4ogJ6aswuzI' },
        { name: 'viewport', content: 'width=device-width, initial-scale=1' },
        { name: 'format-detection', content: 'telephone=no' },
        { name: 'p:domain_verify', content: 'ec759ace3b180b49ecc2736cc09bbcd0' }
      ],
      link: [
        { rel: 'stylesheet', href: 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css' },
        { rel: 'stylesheet', href: 'https://fonts.googleapis.com/icon?family=Material+Icons' },
        { rel: 'icon', type: 'image/x-icon', href: '/favicon.ico' }
      ],
      script: [
        { src: 'https://sdk.mercadopago.com/js/v2' },
        // Script do Stripe removido pois não está mais em uso
        {
          innerHTML: `(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
})(window,document,'script','dataLayer','GTM-5WPM97MX');`,
          type: 'text/javascript',
          hid: 'gtm-script'
        }
      ]
    }
  },
  router: {
    options: {
      hashMode: false
    }
  },
  robots: {
    // @ts-ignore
    UserAgent: '*',
    Disallow: '/admin',
    Allow: '/produto/*',
    Sitemap: 'https://fidelfigures.com.br/api/sitemap.xml'
  }
})