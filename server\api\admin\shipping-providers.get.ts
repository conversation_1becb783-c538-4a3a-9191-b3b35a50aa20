import { collection, getDocs } from "firebase/firestore";
import { db } from "~/server/utils/firebase";
import { DEFAULT_SHIPPING_PROVIDERS } from "~/constants/shippingProviders";
import type { IShippingProvider } from "~/types/customer";

const SHIPPING_PROVIDERS_COLLECTION = "shippingProviders";

/**
 * Rota para buscar todas as transportadoras
 * GET /api/admin/shipping-providers
 */
export default defineEventHandler(async (event) => {
  try {
    console.log('🔍 [API] Buscando transportadoras');

    // Buscar transportadoras customizadas do Firebase
    const providersRef = collection(db, SHIPPING_PROVIDERS_COLLECTION);
    const providersSnapshot = await getDocs(providersRef);

    const customProviders: IShippingProvider[] = providersSnapshot.docs.map((doc) => {
      const data = doc.data();
      return {
        id: doc.id,
        name: data.name,
        code: data.code,
        trackingUrl: data.trackingUrl,
        isActive: data.isActive,
        createdAt: data.createdAt?.toDate() || new Date(),
        updatedAt: data.updatedAt?.toDate() || new Date()
      };
    });

    // Combinar transportadoras padrão com customizadas
    const defaultProviders: IShippingProvider[] = DEFAULT_SHIPPING_PROVIDERS.map(provider => ({
      ...provider,
      createdAt: new Date('2024-01-01'), // Data fictícia para transportadoras padrão
      updatedAt: new Date('2024-01-01')
    }));

    // Filtrar transportadoras padrão que não foram sobrescritas por customizadas
    const filteredDefaultProviders = defaultProviders.filter(
      defaultProvider => !customProviders.some(
        customProvider => customProvider.code === defaultProvider.code
      )
    );

    const allProviders = [...filteredDefaultProviders, ...customProviders];

    // Ordenar por nome
    allProviders.sort((a, b) => a.name.localeCompare(b.name));

    console.log(`✅ [API] Encontradas ${allProviders.length} transportadoras (${filteredDefaultProviders.length} padrão + ${customProviders.length} customizadas)`);

    return {
      success: true,
      data: allProviders
    };

  } catch (error) {
    console.error('❌ [API] Erro ao buscar transportadoras:', error);
    
    throw createError({
      statusCode: 500,
      statusMessage: "Erro interno do servidor ao buscar transportadoras",
    });
  }
});
