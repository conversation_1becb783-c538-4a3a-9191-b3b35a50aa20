import { ref } from "vue";

interface UploadResponse {
  url: string;
}

export function useStorage() {
  const imageUrls = ref<string[]>([]);
  const uploadProgress = ref<Record<string, number>>({});

  const uploadFile = async (file: File, path: string): Promise<string> => {
    try {
      console.log(`📤 Preparando upload de ${file.name} (${(file.size / 1024 / 1024).toFixed(2)} MB) para ${path}`);

      // Criar um ID único para este upload para rastrear o progresso
      const uploadId = `${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
      uploadProgress.value[uploadId] = 0;

      const formData = new FormData();
      formData.append("file", file);
      formData.append("path", path);

      // Verificar se é um arquivo grande (mais de 5MB)
      const isLargeFile = file.size > 5 * 1024 * 1024;

      if (isLargeFile && file.type.startsWith('video/')) {
        console.log(`🎬 Arquivo de vídeo grande detectado (${(file.size / 1024 / 1024).toFixed(2)} MB). Usando método otimizado.`);

        // Para arquivos grandes, especialmente vídeos, usar fetch com XMLHttpRequest para monitorar o progresso
        return new Promise((resolve, reject) => {
          const xhr = new XMLHttpRequest();

          // Monitorar o progresso do upload
          xhr.upload.addEventListener('progress', (event) => {
            if (event.lengthComputable) {
              const percentComplete = (event.loaded / event.total) * 100;
              uploadProgress.value[uploadId] = percentComplete;
              console.log(`Upload em progresso: ${percentComplete.toFixed(2)}%`);
            }
          });

          // Configurar handlers de eventos
          xhr.addEventListener('load', () => {
            if (xhr.status >= 200 && xhr.status < 300) {
              try {
                const response = JSON.parse(xhr.responseText);
                uploadProgress.value[uploadId] = 100;
                console.log(`✅ Upload concluído: ${response.url}`);
                resolve(response.url);
              } catch (e) {
                reject(new Error(`Erro ao processar resposta: ${e}`));
              }
            } else {
              reject(new Error(`Erro no upload: ${xhr.status} ${xhr.statusText}`));
            }
          });

          xhr.addEventListener('error', () => {
            reject(new Error('Erro de rede durante o upload'));
          });

          xhr.addEventListener('abort', () => {
            reject(new Error('Upload abortado'));
          });

          // Iniciar o upload
          xhr.open('POST', '/api/upload');
          xhr.send(formData);
        });
      } else {
        // Para arquivos menores, usar o método padrão
        const response = await $fetch("/api/upload", {
          method: "POST",
          body: formData,
        });

        return (response as { url: string }).url;
      }
    } catch (error) {
      console.error("Erro ao fazer upload do arquivo:", error);
      throw error;
    }
  };

  const uploadProductFiles = async (
    files: File[],
    productId: string,
    type: "images" | "videos" = "images"
  ): Promise<string[]> => {
    try {
      // Para vídeos, fazer upload sequencial para evitar sobrecarga
      if (type === "videos") {
        console.log(`🎬 Iniciando upload sequencial de ${files.length} vídeos`);
        const urls: string[] = [];

        for (const file of files) {
          const uniqueName = `fidel_figures_${Date.now()}_${Math.random().toString(36).substring(2, 15)}_${Math.floor(Math.random() * 10000000000)}.${file.name.split('.').pop()}`;
          const path = `products/${productId}/${type}/${uniqueName}`;
          console.log(`📤 Enviando vídeo: ${file.name} → ${uniqueName}`);
          const url = await uploadFile(file, path);
          urls.push(url);
        }

        return urls;
      } else {
        // Para imagens, manter o upload paralelo
        const uploadPromises = files.map((file) => {
          const uniqueName = `${Date.now()}_${Math.random().toString(36).substring(2, 15)}_${file.name}`;
          const path = `products/${productId}/${type}/${uniqueName}`;
          return uploadFile(file, path);
        });

        const urls = await Promise.all(uploadPromises);
        return urls;
      }
    } catch (error) {
      console.error("Erro ao fazer upload dos arquivos:", error);
      throw error;
    }
  };

  const uploadImage = async (file: File, path: string): Promise<string> => {
    return uploadFile(file, path);
  };

  const uploadProductImages = async (
    files: File[],
    productId: string
  ): Promise<string[]> => {
    return uploadProductFiles(files, productId, "images");
  };

  return {
    uploadFile,
    uploadProductFiles,
    uploadImage,
    uploadProductImages,
    imageUrls,
    uploadProgress,
  };
}
