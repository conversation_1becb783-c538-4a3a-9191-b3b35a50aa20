<template>
  <div>
    <!-- Google Tag Manager (noscript) -->
    <noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-5WPM97MX"
    height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
    <!-- End Google Tag Manager (noscript) -->

    <NuxtLayout>
      <NuxtPage :class="isAdminOnManager ? '' : 'mt-[64px]'" />
      <WhatsAppButton v-if="!isAdminRoute" />
    </NuxtLayout>

  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import WhatsAppButton from '~/components/WhatsAppButton.vue'
import { useAuth } from '~/composables/useAuth'

const route = useRoute()
const isAdminRoute = computed(() => route.path.startsWith('/admin'))

const isAdminOnManager = computed(() => {
  const route = useRoute();
  const auth = useAuth();

  // Não esconder o header na área do cliente, mesmo para admins
  if (route.path === '/admin/area-do-cliente') {
    return false;
  }
  // Esconder o header em outras páginas de admin
  return auth.isAdmin.value && route.path.startsWith('/admin/');
});
</script>
