import type { FigureData } from "./figures";
import type { PaymentProvider } from "./payment";

export interface IAddress {
  street: string;
  number: string;
  complement?: string;
  neighborhood: string;
  city: string;
  state: string;
  zipCode: string;
  isDefault?: boolean;
  id?: string;
  name?: string;
  phone?: string; // Added phone field for contact
}

export interface IShippingProvider {
  id: string;
  name: string;
  code: string;
  trackingUrl: string; // URL base para rastreamento, ex: "https://rastreamento.correios.com.br/app/index.php?objeto="
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface IOrderShipping {
  provider: IShippingProvider;
  trackCode: string;
  trackingUrl?: string; // URL completa para rastreamento (provider.trackingUrl + trackCode)
}

export interface IOrder {
  id: string;
  createdAt: Date;
  updatedAt: Date;
  paymentId: string;
  collectionId: string;
  collectionStatus: string;
  paymentType: string;
  externalReference: string;
  merchantOrderId: string;
  preferenceId: string;
  siteId: string;
  processingMode: string;
  merchantAccountId: string;
  customerId: string;
  status?: string;
  productId: string;
  figure?: FigureData;
  paymentMethod?: string;
  total?: number;
  shipping?: any;
  quantity?: number;
  paymentProvider?: PaymentProvider;
  stripePaymentIntentId?: string;
  isTest?: boolean; // Flag para indicar se é uma compra de teste
  trackCode?: string; // Código de rastreamento dos Correios (mantido para compatibilidade)
  shippingTracking?: IOrderShipping; // Nova estrutura para rastreamento com transportadora
}

export interface IRejectedOrder {
  id: string;
  createdAt: Date;
  updatedAt: Date;
  paymentId: string;
  collectionId: string;
  collectionStatus: string;
  paymentType: string;
  externalReference: string;
  merchantOrderId: string;
  preferenceId: string;
  siteId: string;
  processingMode: string;
  merchantAccountId: string;
  customerId?: string;
  status?: string;
  productId?: string;
  total?: number;
  shipping?: any;
  quantity?: number;
  paymentProvider?: PaymentProvider;
  isTest?: boolean; // Flag para indicar se é uma compra de teste
  trackCode?: string; // Código de rastreamento dos Correios (mantido para compatibilidade)
  shippingTracking?: IOrderShipping; // Nova estrutura para rastreamento com transportadora
}

export interface IPendingOrder {
  id: string;
  createdAt: Date;
  updatedAt: Date;
  paymentId: string;
  collectionId: string;
  collectionStatus: string;
  paymentType: string;
  externalReference: string;
  merchantOrderId: string;
  preferenceId: string;
  siteId: string;
  processingMode: string;
  merchantAccountId: string;
  customerId?: string;
  status?: string;
  productId?: string;
  shipping?: any;
  quantity?: number;
  total?: number;
  paymentProvider?: PaymentProvider;
  stripePaymentIntentId?: string;
  stripeClientSecret?: string;
  isTest?: boolean; // Flag para indicar se é uma compra de teste
  trackCode?: string; // Código de rastreamento dos Correios (mantido para compatibilidade)
  shippingTracking?: IOrderShipping; // Nova estrutura para rastreamento com transportadora
}

export interface ICustomer {
  name: string;
  email: string;
  phone: string;
  cpf: string;
  addresses: IAddress[];
  defaultAddressIndex: number;
  wishlist: string[];
  orderHistory: string[];
  orders: IOrder[];
  rejectedOrders: IRejectedOrder[];
  pendingOrders: IPendingOrder[];
  createdAt: Date;
  updatedAt: Date;
  lastPurchaseDate: Date | null;
  uid: string;
  totalPurchases?: number;
  id?: string;
  photoURL?: string;
}

export class Customer implements ICustomer {
  name: string;
  email: string;
  phone: string;
  cpf: string;
  addresses: IAddress[];
  defaultAddressIndex: number;
  wishlist: string[];
  orderHistory: string[];
  orders: IOrder[];
  uid: string;
  rejectedOrders: IRejectedOrder[];
  pendingOrders: IPendingOrder[];
  createdAt: Date;
  updatedAt: Date;
  lastPurchaseDate: Date | null;
  totalPurchases?: number;
  id?: string;
  photoURL?: string;

  constructor(data: Partial<ICustomer>) {
    this.uid = data.uid || "";
    this.name = data.name || "";
    this.email = data.email || "";
    this.phone = data.phone || "";
    this.rejectedOrders = Array.isArray(data.rejectedOrders)
      ? data.rejectedOrders
      : [];
    this.cpf = data.cpf || "";
    this.orders = Array.isArray(data.orders) ? data.orders : [];
    this.addresses = Array.isArray(data.addresses) ? data.addresses : [];
    this.defaultAddressIndex =
      typeof data.defaultAddressIndex === "number"
        ? data.defaultAddressIndex
        : 0;
    this.createdAt =
      data.createdAt instanceof Date ? data.createdAt : new Date();
    this.updatedAt =
      data.updatedAt instanceof Date ? data.updatedAt : new Date();
    this.lastPurchaseDate =
      data.lastPurchaseDate instanceof Date ? data.lastPurchaseDate : null;
    this.totalPurchases =
      typeof data.totalPurchases === "number" ? data.totalPurchases : 0;
    this.wishlist = Array.isArray(data.wishlist) ? data.wishlist : [];
    this.orderHistory = Array.isArray(data.orderHistory)
      ? data.orderHistory
      : [];
    this.pendingOrders = Array.isArray(data.pendingOrders)
      ? data.pendingOrders
      : [];
    this.id = data.id;
    this.photoURL = data.photoURL || "";
  }

  addAddress(address: IAddress) {
    this.addresses.push(address);
    this.updatedAt = new Date();
  }

  removeAddress(index: number) {
    if (index >= 0 && index < this.addresses.length) {
      this.addresses.splice(index, 1);
      if (this.defaultAddressIndex >= this.addresses.length) {
        this.defaultAddressIndex = Math.max(0, this.addresses.length - 1);
      }
      this.updatedAt = new Date();
    }
  }

  setDefaultAddress(index: number) {
    if (index >= 0 && index < this.addresses.length) {
      this.defaultAddressIndex = index;
      this.updatedAt = new Date();
    }
  }

  addToWishlist(figureId: string) {
    if (!this.wishlist.includes(figureId)) {
      this.wishlist.push(figureId);
      this.updatedAt = new Date();
    }
  }

  removeFromWishlist(figureId: string) {
    const index = this.wishlist.indexOf(figureId);
    if (index > -1) {
      this.wishlist.splice(index, 1);
      this.updatedAt = new Date();
    }
  }

  addOrder(orderId: string) {
    this.orderHistory.push(orderId);
    this.totalPurchases = (this.totalPurchases || 0) + 1;
    this.lastPurchaseDate = new Date();
    this.updatedAt = new Date();
  }

  getDefaultAddress(): IAddress | null {
    if (
      Array.isArray(this.addresses) &&
      this.addresses.length > 0 &&
      this.defaultAddressIndex >= 0 &&
      this.defaultAddressIndex < this.addresses.length
    ) {
      return this.addresses[this.defaultAddressIndex];
    }
    return null;
  }
}
