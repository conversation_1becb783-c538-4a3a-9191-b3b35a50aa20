/**
 * Composable para facilitar o rastreamento de conversões do Google Ads
 * 
 * Este composable fornece funções para rastrear diferentes tipos de conversões
 * do Google Ads, como visitas ao site e compras.
 */

import { v4 as uuidv4 } from 'uuid';

export const useGoogleAdsConversion = () => {
  /**
   * Rastreia uma conversão de visita ao site
   * Usa o ID de conversão AW-16649667979/8jvRCM-N8OMZEIuLl4M-
   */
  const trackVisit = () => {
    if (typeof window === 'undefined' || !window.gtag) {
      console.error('Google Ads: gtag não está disponível para rastreamento de visita');
      return;
    }

    console.log('Registrando conversão de visita do Google Ads');
    window.gtag('event', 'conversion', {
      'send_to': 'AW-16649667979/8jvRCM-N8OMZEIuLl4M-',
      'value': 1.0,
      'currency': 'BRL'
    });
  };

  /**
   * Rastreia uma conversão de compra
   * Usa o ID de conversão AW-16649667979/8jvRCM-N8OMZEIuLl4M-
   * 
   * @param price - Valor da compra
   * @param transactionId - ID da transação (opcional)
   * @param productId - ID do produto (opcional)
   * @param productName - Nome do produto (opcional)
   */
  const trackPurchase = (
    price: number,
    transactionId?: string,
    productId?: string,
    productName?: string
  ) => {
    if (typeof window === 'undefined' || !window.gtag) {
      console.error('Google Ads: gtag não está disponível para rastreamento de compra');
      return;
    }

    // Gerar um ID de transação se não for fornecido
    const txId = transactionId || uuidv4();
    
    console.log('Registrando conversão de compra do Google Ads:', {
      price,
      transactionId: txId,
      productId,
      productName
    });

    // Evento de conversão do Google Ads
    window.gtag('event', 'conversion', {
      'send_to': 'AW-16649667979/8jvRCM-N8OMZEIuLl4M-',
      'value': price,
      'currency': 'BRL',
      'transaction_id': txId
    });

    // Evento de compra para o Google Analytics (opcional, mas recomendado para consistência)
    if (productId || productName) {
      window.gtag('event', 'purchase', {
        'transaction_id': txId,
        'value': price,
        'currency': 'BRL',
        'items': [{
          'id': productId || 'unknown',
          'name': productName || 'Produto',
          'price': price
        }]
      });
    }
  };

  return {
    trackVisit,
    trackPurchase
  };
};
