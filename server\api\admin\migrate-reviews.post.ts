import { collection, getDocs, doc, getDoc, addDoc, updateDoc, arrayRemove, Timestamp } from "firebase/firestore";
import { db } from "~/server/utils/firebase";
import type { IReview } from "~/types/reviews";
import type { IProductReview } from "~/types/customer";

const USERS_COLLECTION = "users";
const REVIEWS_COLLECTION = "reviews";
const PRODUCTS_COLLECTION = "products"; // Nome correto da coleção de produtos

/**
 * Endpoint para migrar avaliações dos objetos de usuário para a nova coleção
 * POST /api/admin/migrate-reviews
 * 
 * ATENÇÃO: Este endpoint deve ser executado apenas uma vez durante a migração!
 */
export default defineEventHandler(async (event) => {
  try {
    // Verificar se é um admin (adicionar verificação de autenticação aqui)
    const body = await readBody(event);
    const { adminKey, dryRun = true } = body;
    
    // Chave de segurança para evitar execução acidental
    if (adminKey !== "MIGRATE_REVIEWS_2024") {
      throw createError({
        statusCode: 403,
        statusMessage: "Chave de admin inválida",
      });
    }

    console.log(`🔄 [Migration] Iniciando migração de avaliações (dry-run: ${dryRun})`);

    // Buscar todos os usuários
    const usersRef = collection(db, USERS_COLLECTION);
    const usersSnapshot = await getDocs(usersRef);

    let totalUsers = 0;
    let totalReviews = 0;
    let migratedReviews = 0;
    let errors: string[] = [];
    const migrationLog: any[] = [];

    // Iterar por todos os usuários
    for (const userDoc of usersSnapshot.docs) {
      totalUsers++;
      const userData = userDoc.data();
      const userId = userDoc.id;

      if (!userData.reviews || !Array.isArray(userData.reviews) || userData.reviews.length === 0) {
        continue;
      }

      console.log(`📋 [Migration] Processando usuário ${userId} com ${userData.reviews.length} avaliações`);

      // Processar cada avaliação do usuário
      for (const oldReview of userData.reviews) {
        totalReviews++;
        
        try {
          // Buscar dados do produto para desnormalização
          let productData = null;
          if (oldReview.productId) {
            try {
              const productRef = doc(db, PRODUCTS_COLLECTION, oldReview.productId);
              const productDoc = await getDoc(productRef);
              if (productDoc.exists()) {
                productData = productDoc.data();
              }
            } catch (error) {
              console.warn(`⚠️ [Migration] Produto ${oldReview.productId} não encontrado`);
            }
          }

          // Criar nova estrutura de avaliação
          const newReview: Omit<IReview, 'id'> = {
            userId,
            productId: oldReview.productId || '',
            orderId: oldReview.orderId || '',
            rating: Number(oldReview.rating) || 5,
            comment: oldReview.comment || '',
            images: Array.isArray(oldReview.images) ? oldReview.images : [],
            videos: Array.isArray(oldReview.videos) ? oldReview.videos : [],
            createdAt: oldReview.createdAt instanceof Date 
              ? oldReview.createdAt 
              : new Date(oldReview.createdAt || Date.now()),
            updatedAt: oldReview.updatedAt instanceof Date 
              ? oldReview.updatedAt 
              : new Date(oldReview.updatedAt || Date.now()),
            isVerifiedPurchase: oldReview.isVerifiedPurchase !== false, // Default true para compatibilidade
            
            // Dados desnormalizados
            userName: userData.name || "Usuário Anônimo",
            userPhoto: userData.photoURL || null,
            productTitle: productData?.title || productData?.alias || "Produto",
            productImage: productData?.mainImage || productData?.gallery_imgs?.[0] || null,
            
            // Metadados iniciais
            helpfulCount: 0,
            reportCount: 0,
            isHidden: false,
          };

          // Log da migração
          const logEntry = {
            oldReviewId: oldReview.id,
            userId,
            productId: oldReview.productId,
            productTitle: newReview.productTitle,
            rating: newReview.rating,
            hasImages: newReview.images.length > 0,
            hasVideos: newReview.videos.length > 0,
          };

          if (!dryRun) {
            // Salvar na nova coleção
            const reviewsRef = collection(db, REVIEWS_COLLECTION);
            const docRef = await addDoc(reviewsRef, {
              ...newReview,
              createdAt: Timestamp.fromDate(newReview.createdAt),
              updatedAt: Timestamp.fromDate(newReview.updatedAt),
            });

            logEntry.newReviewId = docRef.id;
            console.log(`✅ [Migration] Avaliação migrada: ${oldReview.id} -> ${docRef.id}`);
          } else {
            logEntry.newReviewId = 'DRY_RUN';
            console.log(`🔍 [Migration] [DRY RUN] Avaliação seria migrada: ${oldReview.id}`);
          }

          migrationLog.push(logEntry);
          migratedReviews++;

        } catch (error: any) {
          const errorMsg = `Erro ao migrar avaliação ${oldReview.id} do usuário ${userId}: ${error.message}`;
          errors.push(errorMsg);
          console.error(`❌ [Migration] ${errorMsg}`);
        }
      }

      // Remover avaliações do objeto do usuário (apenas se não for dry-run)
      if (!dryRun && userData.reviews.length > 0) {
        try {
          const userRef = doc(db, USERS_COLLECTION, userId);
          await updateDoc(userRef, {
            reviews: [], // Limpar array de avaliações
            updatedAt: Timestamp.now(),
          });
          console.log(`🧹 [Migration] Avaliações removidas do usuário ${userId}`);
        } catch (error: any) {
          const errorMsg = `Erro ao limpar avaliações do usuário ${userId}: ${error.message}`;
          errors.push(errorMsg);
          console.error(`❌ [Migration] ${errorMsg}`);
        }
      }
    }

    const summary = {
      dryRun,
      totalUsers,
      totalReviews,
      migratedReviews,
      errors: errors.length,
      errorMessages: errors,
      migrationLog: dryRun ? migrationLog.slice(0, 10) : migrationLog, // Limitar log em dry-run
    };

    console.log(`🎉 [Migration] Migração concluída:`, summary);

    return {
      success: true,
      message: dryRun 
        ? "Simulação de migração concluída com sucesso" 
        : "Migração de avaliações concluída com sucesso",
      data: summary,
    };

  } catch (error: any) {
    console.error("❌ [Migration] Erro durante migração:", error);
    
    if (error.statusCode) {
      throw error;
    }
    
    throw createError({
      statusCode: 500,
      statusMessage: "Erro interno durante migração de avaliações",
    });
  }
});

/**
 * Exemplo de uso:
 * 
 * 1. Primeiro, executar em modo dry-run para verificar:
 * POST /api/admin/migrate-reviews
 * {
 *   "adminKey": "MIGRATE_REVIEWS_2024",
 *   "dryRun": true
 * }
 * 
 * 2. Depois, executar a migração real:
 * POST /api/admin/migrate-reviews
 * {
 *   "adminKey": "MIGRATE_REVIEWS_2024",
 *   "dryRun": false
 * }
 */
