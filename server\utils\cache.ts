import { collection, getDocs } from "firebase/firestore";
import { db } from "~/server/utils/firebase";
import { Coupon } from "~/types/coupons";

// Tempo de expiração do cache em milissegundos (1 hora)
export const CACHE_EXPIRATION_TIME = 60 * 60 * 1000;

// Cache compartilhado para cupons
const couponsCache = {
  data: null as Coupon[] | null,
  timestamp: 0
};

/**
 * Busca cupons ativos, usando cache quando possível
 * @param forceRefresh Forçar atualização do cache
 * @returns Array de cupons ativos
 */
export async function getCachedCoupons(forceRefresh = false): Promise<Coupon[]> {
  // Verificar se podemos usar o cache
  const isCacheValid = 
    !forceRefresh && 
    couponsCache.data !== null && 
    (Date.now() - couponsCache.timestamp) < CACHE_EXPIRATION_TIME;

  if (isCacheValid) {
    console.log('🔍 [Cache] Usando cache de cupons');
    return couponsCache.data as Coupon[];
  }

  console.log('🔍 [Cache] Buscando cupons do Firebase');
  
  try {
    const couponsRef = collection(db, "coupons");
    const querySnapshot = await getDocs(couponsRef);

    const coupons = querySnapshot.docs
      .map((doc) => {
        const data = doc.data();

        try {
          const endAt = data.endAt?.toDate();
          // Se o cupom já expirou, retorna null
          if (endAt && endAt < new Date()) {
            return null;
          }

          return {
            id: doc.id,
            ...data,
            createdAt: data.createdAt?.toDate(),
            endAt: endAt,
          };
        } catch (dateError) {
          console.error("Erro ao converter datas do cupom:", doc.id, dateError);
          return null;
        }
      })
      .filter(Boolean) as unknown as Coupon[];

    // Atualizar o cache
    couponsCache.data = coupons;
    couponsCache.timestamp = Date.now();
    console.log(`🔍 [Cache] Cache de cupons atualizado com ${coupons.length} cupons`);

    return coupons;
  } catch (error) {
    console.error("Erro ao buscar cupons:", error);
    
    // Se temos um cache antigo, é melhor usá-lo do que retornar vazio
    if (couponsCache.data) {
      console.log('🔍 [Cache] Usando cache antigo de cupons após erro');
      return couponsCache.data;
    }
    
    return [];
  }
}

/**
 * Verifica se um cache está válido
 * @param cache Objeto de cache com timestamp
 * @param forceRefresh Forçar atualização do cache
 * @returns true se o cache for válido
 */
export function isCacheValid(cache: { timestamp: number, data: any }, forceRefresh = false): boolean {
  return !forceRefresh && 
         cache.data !== null && 
         (Date.now() - cache.timestamp) < CACHE_EXPIRATION_TIME;
}
