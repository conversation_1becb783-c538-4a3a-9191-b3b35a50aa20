import { doc, getDoc, deleteDoc } from "firebase/firestore";
import { db } from "~/server/utils/firebase";

const REVIEWS_COLLECTION = "reviews";
const USERS_COLLECTION = "users";

/**
 * Endpoint para admins deletarem avaliações
 * DELETE /api/admin/reviews/[reviewId]
 * 
 * Apenas administradores podem deletar avaliações
 * Usuários comuns podem apenas editar suas avaliações
 */
export default defineEventHandler(async (event) => {
  try {
    const reviewId = getRouterParam(event, 'reviewId');
    const body = await readBody(event);
    const { adminUserId } = body;

    console.log(`🗑️ [Admin API] Tentativa de deletar avaliação ${reviewId} pelo admin ${adminUserId}`);

    // Validar parâmetros obrigatórios
    if (!reviewId) {
      throw createError({
        statusCode: 400,
        statusMessage: "ID da avaliação é obrigatório",
      });
    }

    if (!adminUserId) {
      throw createError({
        statusCode: 400,
        statusMessage: "ID do administrador é obrigatório",
      });
    }

    // Verificar se o usuário é admin
    const adminRef = doc(db, USERS_COLLECTION, adminUserId);
    const adminDoc = await getDoc(adminRef);
    
    if (!adminDoc.exists()) {
      throw createError({
        statusCode: 404,
        statusMessage: "Administrador não encontrado",
      });
    }

    const adminData = adminDoc.data();
    if (!adminData.isAdmin) {
      console.log(`❌ [Admin API] Usuário ${adminUserId} não é admin`);
      throw createError({
        statusCode: 403,
        statusMessage: "Acesso negado. Apenas administradores podem deletar avaliações",
      });
    }

    console.log(`✅ [Admin API] Admin ${adminUserId} verificado com sucesso`);

    // Verificar se a avaliação existe
    const reviewRef = doc(db, REVIEWS_COLLECTION, reviewId);
    const reviewDoc = await getDoc(reviewRef);
    
    if (!reviewDoc.exists()) {
      throw createError({
        statusCode: 404,
        statusMessage: "Avaliação não encontrada",
      });
    }

    const reviewData = reviewDoc.data();
    console.log(`🔍 [Admin API] Avaliação encontrada:`, {
      id: reviewId,
      userId: reviewData.userId,
      productId: reviewData.productId,
      rating: reviewData.rating
    });

    // Deletar a avaliação
    await deleteDoc(reviewRef);

    console.log(`✅ [Admin API] Avaliação ${reviewId} deletada com sucesso pelo admin ${adminUserId}`);

    return {
      success: true,
      message: "Avaliação deletada com sucesso",
      data: {
        deletedReviewId: reviewId,
        deletedBy: adminUserId,
        deletedAt: new Date().toISOString(),
        originalReview: {
          userId: reviewData.userId,
          productId: reviewData.productId,
          rating: reviewData.rating,
          comment: reviewData.comment
        }
      }
    };

  } catch (error: any) {
    console.error("❌ [Admin API] Erro ao deletar avaliação:", error);
    
    if (error.statusCode) {
      throw error;
    }
    
    throw createError({
      statusCode: 500,
      statusMessage: "Erro interno ao deletar avaliação",
    });
  }
});

/**
 * Exemplo de uso:
 * 
 * DELETE /api/admin/reviews/review-id-123
 * {
 *   "adminUserId": "admin-user-id"
 * }
 * 
 * Resposta de sucesso:
 * {
 *   "success": true,
 *   "message": "Avaliação deletada com sucesso",
 *   "data": {
 *     "deletedReviewId": "review-id-123",
 *     "deletedBy": "admin-user-id",
 *     "deletedAt": "2024-01-01T12:00:00.000Z",
 *     "originalReview": {
 *       "userId": "user-123",
 *       "productId": "product-456",
 *       "rating": 5,
 *       "comment": "Excelente produto!"
 *     }
 *   }
 * }
 */
