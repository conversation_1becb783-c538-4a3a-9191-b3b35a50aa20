<template>
  <div v-if="isAdmin" class="admin-order-button mt-4">
    <button
      @click="showModal = true"
      class="w-full bg-purple-600 hover:bg-purple-700 text-white font-bold py-3 px-4 rounded-lg flex items-center justify-center"
    >
      <i class="fas fa-user-shield mr-2"></i>
      Adicionar como Pedido
    </button>

    <!-- Modal de seleção de cliente -->
    <div v-if="showModal" class="fixed inset-0 z-50 overflow-y-auto">
      <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <!-- Overlay de fundo -->
        <div class="fixed inset-0 transition-opacity" @click="showModal = false">
          <div class="absolute inset-0 bg-gray-500 opacity-75"></div>
        </div>

        <!-- Modal -->
        <div
          class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full"
        >
          <!-- Cabeçalho -->
          <div class="bg-gray-50 px-4 py-3 border-b flex justify-between items-center">
            <h3 class="text-lg font-medium text-gray-900">Adicionar Pedido Administrativo</h3>
            <button @click="showModal = false" class="text-gray-400 hover:text-gray-500">
              <i class="fas fa-times"></i>
            </button>
          </div>

          <!-- Conteúdo -->
          <div class="bg-white p-6">
            <div v-if="loading" class="flex justify-center py-8">
              <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-500"></div>
            </div>

            <div v-else>
              <div class="mb-4">
                <h4 class="text-sm font-medium text-gray-700 mb-2">Produto selecionado:</h4>
                <div class="flex items-center p-3 bg-gray-50 rounded-lg">
                  <div class="w-16 h-16 flex-shrink-0 mr-4">
                    <img :src="product.gallery_imgs[0]" :alt="product.title" class="w-full h-full object-cover rounded" />
                  </div>
                  <div>
                    <p class="font-medium">{{ product.title }}</p>
                    <p class="text-sm text-gray-500">{{ formatCurrency(product.base_price) }}</p>
                  </div>
                </div>
              </div>

              <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">Selecione o cliente:</label>
                <div class="relative">
                  <input
                    v-model="searchQuery"
                    type="text"
                    placeholder="Buscar por nome ou email..."
                    class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
                    @input="searchClients"
                  />
                  <i class="fas fa-search absolute right-3 top-3 text-gray-400"></i>
                </div>
              </div>

              <div v-if="filteredClients.length > 0" class="max-h-60 overflow-y-auto border rounded-lg mb-4">
                <div
                  v-for="client in filteredClients"
                  :key="client.id || client.uid"
                  @click="selectClient(client)"
                  class="p-3 border-b hover:bg-gray-50 cursor-pointer flex items-center"
                  :class="{ 'bg-purple-50': selectedClient && (selectedClient.id === client.id || selectedClient.uid === client.uid) }"
                >
                  <div class="flex-1">
                    <p class="font-medium">{{ client.name }}</p>
                    <p class="text-sm text-gray-500">{{ client.email }}</p>
                  </div>
                  <div v-if="selectedClient && (selectedClient.id === client.id || selectedClient.uid === client.uid)">
                    <i class="fas fa-check text-purple-600"></i>
                  </div>
                </div>
              </div>
              <div v-else-if="searchQuery && !loading" class="text-center py-4 text-gray-500">
                Nenhum cliente encontrado
              </div>

              <div class="mt-6">
                <h4 class="text-sm font-medium text-gray-700 mb-2">Opções adicionais:</h4>
                <div class="space-y-3">
                  <div class="flex items-center">
                    <input
                      id="test-order"
                      v-model="isTestOrder"
                      type="checkbox"
                      class="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
                    />
                    <label for="test-order" class="ml-2 block text-sm text-gray-700">
                      Marcar como pedido de teste
                    </label>
                  </div>
                  <div class="flex items-center">
                    <input
                      id="send-notification"
                      v-model="sendNotification"
                      type="checkbox"
                      class="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
                    />
                    <label for="send-notification" class="ml-2 block text-sm text-gray-700">
                      Enviar notificação ao cliente
                    </label>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Rodapé -->
          <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
            <button
              @click="createOrder"
              :disabled="!selectedClient || processing"
              class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-purple-600 text-base font-medium text-white hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <i v-if="processing" class="fas fa-spinner fa-spin mr-2"></i>
              Adicionar Pedido
            </button>
            <button
              @click="showModal = false"
              class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
            >
              Cancelar
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useAuthStore } from '~/stores/auth'
import { useCustomerStore } from '~/stores/customerStore'
import type { Customer } from '~/types/customer'
import type { Figure } from '~/types/figures'
import { v4 as uuidv4 } from 'uuid'

const props = defineProps<{
  product: Figure
}>()

const authStore = useAuthStore()
const customerStore = useCustomerStore()

// Estado
const showModal = ref(false)
const loading = ref(false)
const processing = ref(false)
const searchQuery = ref('')
const filteredClients = ref<Customer[]>([])
const selectedClient = ref<Customer | null>(null)
const isTestOrder = ref(true) // Por padrão, marcar como pedido de teste
const sendNotification = ref(false)

// Computed
const isAdmin = computed(() => authStore.isAdmin)

// Métodos
async function searchClients() {
  if (!searchQuery.value.trim()) {
    filteredClients.value = [...customerStore.customers]
    return
  }

  const query = searchQuery.value.toLowerCase()
  filteredClients.value = customerStore.customers.filter(
    client => 
      client.name?.toLowerCase().includes(query) || 
      client.email?.toLowerCase().includes(query)
  )
}

function selectClient(client: Customer) {
  selectedClient.value = client
}

async function createOrder() {
  if (!selectedClient.value) return

  processing.value = true
  try {
    const clientId = selectedClient.value.id || selectedClient.value.uid

    // Criar um novo pedido
    const order = {
      id: uuidv4(),
      productId: props.product.id,
      paymentId: `20930${Date.now()}`,
      collectionId: `20930${Date.now()}`,
      collectionStatus: 'approved',
      paymentType: 'admin',
      externalReference: '',
      merchantOrderId: `20930${Date.now()}`,
      preferenceId: '',
      siteId: '',
      processingMode: 'admin',
      merchantAccountId: '',
      status: 'approved',
      shipping: null,
      quantity: 1,
      total: props.product.base_price,
      paymentProvider: 'admin',
      isTest: isTestOrder.value,
      createdAt: new Date(),
      updatedAt: new Date()
    }

    // Adicionar o pedido ao cliente
    const { data, error } = await useFetch(`/api/customers/${clientId}/orders`, {
      method: 'POST',
      body: order
    })

    if (error.value) {
      throw new Error(error.value.message || 'Erro ao adicionar pedido')
    }

    // Enviar notificação se solicitado
    if (sendNotification.value) {
      await useFetch('/api/notify', {
        method: 'POST',
        body: {
          type: 'order_created',
          email: selectedClient.value.email,
          name: selectedClient.value.name,
          orderId: order.id,
          productId: props.product.id,
          productName: props.product.title,
          productImage: props.product.gallery_imgs[0],
          price: props.product.base_price
        }
      })
    }

    // Atualizar a lista de clientes
    await customerStore.fetchCustomerById(clientId)

    // Fechar o modal e mostrar mensagem de sucesso
    showModal.value = false
    alert('Pedido adicionado com sucesso!')
  } catch (error) {
    console.error('Erro ao criar pedido:', error)
    alert(`Erro ao adicionar pedido: ${error}`)
  } finally {
    processing.value = false
  }
}

function formatCurrency(value: number): string {
  return new Intl.NumberFormat('pt-BR', {
    style: 'currency',
    currency: 'BRL'
  }).format(value)
}

// Lifecycle
onMounted(async () => {
  loading.value = true
  try {
    // Carregar clientes se ainda não estiverem carregados
    if (customerStore.customers.length === 0) {
      await customerStore.fetchAllCustomers()
    }
    
    // Inicializar a lista filtrada com todos os clientes
    filteredClients.value = [...customerStore.customers]
  } catch (error) {
    console.error('Erro ao carregar clientes:', error)
  } finally {
    loading.value = false
  }
})
</script>
