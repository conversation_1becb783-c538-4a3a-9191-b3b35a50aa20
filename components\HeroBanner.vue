<template>
  <section class="relative w-full h-[100vh] overflow-hidden">
    <!-- Background com Parallax -->
    <div
      class="absolute inset-0 w-full h-full transform transition-transform duration-1000"
      :style="{ transform: `translateY(${parallaxOffset}px)` }"
    >
      <div class="relative w-full h-full">
        <!-- Imagem de fundo -->
        <img
          :src="currentSlide.image"
          :alt="currentSlide.title"
          class="hidden md:block w-full h-full object-cover object-center transform scale-110"
          :style="{
            objectPosition: currentSlide.imagePosition || 'center center'
          }"
        >
        <img
          :src="currentSlide.imageMobile || currentSlide.image"
          :alt="currentSlide.title"
          class="md:hidden w-full h-full object-cover object-center transform scale-110"
          :style="{
            objectPosition: currentSlide.imageMobilePosition || 'center center'
          }"
        >

        <!-- Overlay gradiente -->
        <div class="absolute inset-0 bg-gradient-to-r from-black/90 via-black/70 to-transparent"></div>
      </div>
    </div>

    <!-- Conteúdo -->
    <div class="relative h-full">
      <div class="container mx-auto px-4 h-full">
        <div class="flex flex-col justify-center h-full max-w-3xl">
          <!-- Tag -->
          <div
            class="inline-flex items-center space-x-2 mb-6 opacity-0 animate-fade-in"
            style="animation-delay: 200ms"
          >
            <span class="w-12 h-[1px] bg-red-500"></span>
            <span class="text-red-500 font-medium tracking-wider uppercase text-sm">
              {{ currentSlide.category }}
            </span>
          </div>

          <!-- Título -->
          <h1
            class="text-5xl sm:text-6xl lg:text-7xl font-bold text-white mb-6 opacity-0 animate-fade-in"
            style="animation-delay: 400ms"
          >
            {{ currentSlide.title }}
          </h1>

          <!-- Descrição -->
          <p
            class="text-lg sm:text-xl text-gray-300 mb-8 max-w-2xl opacity-0 animate-fade-in"
            style="animation-delay: 600ms"
          >
            {{ currentSlide.description }}
          </p>

          <!-- Botões -->
          <div
            class="flex flex-wrap gap-4 opacity-0 animate-fade-in"
            style="animation-delay: 800ms"
          >
            <button
              @click="handleNavigation(currentSlide.primaryAction.link)"
              class="px-8 py-4 bg-red-600 text-white rounded-md font-medium transition-all duration-300 hover:bg-red-700 hover:scale-105"
            >
              {{ currentSlide.primaryAction.text }}
            </button>

            <button
              v-if="currentSlide.secondaryAction"
              @click="handleNavigation(currentSlide.secondaryAction.link)"
              class="px-8 py-4 border-2 border-white/30 text-white rounded-md font-medium transition-all duration-300 hover:border-white/60 hover:bg-white/10"
            >
              {{ currentSlide.secondaryAction.text }}
            </button>
          </div>
        </div>
      </div>

      <!-- Navegação -->
      <div class="absolute bottom-8 left-1/2 -translate-x-1/2 flex items-center space-x-4">
        <button
          v-for="(slide, index) in slides"
          :key="index"
          @click="currentSlideIndex = index"
          class="w-3 h-3 rounded-full transition-all duration-300"
          :class="currentSlideIndex === index ? 'bg-red-500 scale-125' : 'bg-white/30 hover:bg-white/50'"
        />
      </div>
    </div>
  </section>
</template>

<script setup>
import { ref, computed, onMounted, onBeforeUnmount } from 'vue'
import { useRouter } from 'vue-router'
import dbz from '~/assets/images/dbz.png'
import ww from '~/assets/images/fidel_figures_1737597647_3551548936310601939_3175919548 (1).jpg'
import naruto from '~/assets/images/naruto-with-glowing-orange-eyes-desktop-wallpaper-preview.jpg'
import wwMobile from '~/assets/images/fidel_figures_1737597647_3551548936310601939_3175919548 (2).jpg'

// Slides
const slides = ref([
  {
    title: 'Alto nível de detalhes',
    category: 'Seus heróis e vilões favoritos',
    description: '',
    image: ww,
    imageMobile: wwMobile,
    imageMobilePosition: 'center center',
    imagePosition: 'center 30%',
    primaryAction: {
      text: 'Ver Coleção',
      link: '/collection?category=comics'
    }
  },
  {
    title: 'Produtos Já Produzidos',
    category: 'Envio Imediato',
    description: 'Figuras já produzidas e prontas para envio em até 5 dias úteis após a confirmação do pagamento.',
    image: dbz,
    primaryAction: {
      text: 'Ver Produtos',
      link: '/produtos-ja-produzidos'
    },
    secondaryAction: {
      text: 'Saiba Mais',
      link: '/produtos-ja-produzidos'
    }
  },
  {
    title: 'Dragon Ball',
    category: 'Cha la head cha la',
    description: 'Figuras exclusivas do universo de Dragon Ball, com alta qualidade e acabamento premium.',
    image: dbz,
    primaryAction: {
      text: 'Ver Coleção',
      link: '/collection?category=dragon-ball'
    }
  },
  {
    title: 'Naruto Shippuden',
    category: 'Edição Limitada',
    description: 'Dioramas épicos capturando os momentos mais memoráveis da série.',
    image: naruto,
    primaryAction: {
      text: 'Explorar',
      link: '/collection?category=naruto'
    }
  }
])

// Controle de slides
const currentSlideIndex = ref(0)
const currentSlide = computed(() => slides.value[currentSlideIndex.value])

// Efeito de parallax
const parallaxOffset = ref(0)
const handleScroll = () => {
  const scrollPosition = window.scrollY
  parallaxOffset.value = scrollPosition * 0.4
}

// Navegação
const router = useRouter()
const handleNavigation = (path) => {
  router.push(path)
}

// Auto-play
let autoplayInterval
const startAutoplay = () => {
  autoplayInterval = setInterval(() => {
    currentSlideIndex.value = (currentSlideIndex.value + 1) % slides.value.length
  }, 12000)
}

// Lifecycle
onMounted(() => {
  window.addEventListener('scroll', handleScroll)
  startAutoplay()
})

onBeforeUnmount(() => {
  window.removeEventListener('scroll', handleScroll)
  if (autoplayInterval) clearInterval(autoplayInterval)
})
</script>

<style scoped>
@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fade-in 0.8s cubic-bezier(0.215, 0.61, 0.355, 1) forwards;
}
</style>
