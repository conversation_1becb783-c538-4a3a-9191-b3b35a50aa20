<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste API Reviews v2</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 5px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
    </style>
</head>
<body>
    <h1>Teste API Reviews v2</h1>
    
    <div class="test-section">
        <h2>1. Buscar Produtos</h2>
        <button onclick="testGetProducts()">Buscar Produtos</button>
        <div id="products-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>2. Testar Criação de Review</h2>
        <p>Primeiro busque os produtos para obter um ID válido</p>
        <input type="text" id="productId" placeholder="ID do Produto" style="width: 300px; padding: 5px;">
        <br><br>
        <button onclick="testCreateReview()">Criar Review de Teste</button>
        <div id="review-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>3. Buscar Reviews por Produto (API v2 Simplificada)</h2>
        <input type="text" id="searchProductId" placeholder="ID do Produto" style="width: 300px; padding: 5px;">
        <br><br>
        <button onclick="testGetProductReviews()">Buscar Reviews (Sem Índices Compostos)</button>
        <div id="search-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>4. Teste de Query Simples</h2>
        <p>Teste da nova query simplificada que evita índices compostos do Firebase</p>
        <button onclick="testSimpleQuery()">Testar Query Simples</button>
        <div id="simple-result" class="result"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:4001';

        async function testGetProducts() {
            const resultDiv = document.getElementById('products-result');
            resultDiv.textContent = 'Buscando produtos...';
            
            try {
                const response = await fetch(`${API_BASE}/api/products`);
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ Encontrados ${data.products.length} produtos\n\nPrimeiros 3 produtos:\n` + 
                        JSON.stringify(data.products.slice(0, 3).map(p => ({
                            id: p.id,
                            title: p.title,
                            alias: p.alias
                        })), null, 2);
                    
                    // Preencher automaticamente o primeiro produto
                    if (data.products.length > 0) {
                        document.getElementById('productId').value = data.products[0].id;
                        document.getElementById('searchProductId').value = data.products[0].id;
                    }
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ Erro: ${response.status} - ${JSON.stringify(data)}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ Erro de rede: ${error.message}`;
            }
        }

        async function testCreateReview() {
            const resultDiv = document.getElementById('review-result');
            const productId = document.getElementById('productId').value;
            
            if (!productId) {
                resultDiv.className = 'result error';
                resultDiv.textContent = '❌ Por favor, insira um ID de produto';
                return;
            }

            resultDiv.textContent = 'Criando review...';
            
            const reviewData = {
                userId: 'test-user-' + Date.now(),
                productId: productId,
                orderId: 'test-order-' + Date.now(),
                rating: 5,
                comment: 'Produto excelente! Teste da API v2 de reviews.',
                images: [],
                videos: []
            };

            try {
                const response = await fetch(`${API_BASE}/api/v2/reviews`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(reviewData)
                });

                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ Review criado com sucesso!\n\n` + JSON.stringify(data, null, 2);
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ Erro: ${response.status} - ${JSON.stringify(data, null, 2)}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ Erro de rede: ${error.message}`;
            }
        }

        async function testGetProductReviews() {
            const resultDiv = document.getElementById('search-result');
            const productId = document.getElementById('searchProductId').value;

            if (!productId) {
                resultDiv.className = 'result error';
                resultDiv.textContent = '❌ Por favor, insira um ID de produto';
                return;
            }

            resultDiv.textContent = 'Buscando reviews com query simplificada...';

            try {
                const response = await fetch(`${API_BASE}/api/v2/reviews/product/${productId}`);
                const data = await response.json();

                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ Reviews encontrados com query simplificada!\n\n` + JSON.stringify(data, null, 2);
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ Erro: ${response.status} - ${JSON.stringify(data, null, 2)}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ Erro de rede: ${error.message}`;
            }
        }

        async function testSimpleQuery() {
            const resultDiv = document.getElementById('simple-result');
            const productId = document.getElementById('searchProductId').value || 'test-product';

            resultDiv.textContent = 'Testando query simples sem índices compostos...';

            try {
                // Teste com parâmetros que antes causavam problemas de índice
                const url = `${API_BASE}/api/v2/reviews/product/${productId}?limit=5&sortBy=newest`;
                const response = await fetch(url);
                const data = await response.json();

                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ Query simples funcionou!\n\nURL: ${url}\n\nResposta:\n` + JSON.stringify(data, null, 2);
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ Erro na query simples: ${response.status}\n\n${JSON.stringify(data, null, 2)}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ Erro de rede: ${error.message}`;
            }
        }

        // Auto-carregar produtos ao abrir a página
        window.onload = function() {
            testGetProducts();
        };
    </script>
</body>
</html>
