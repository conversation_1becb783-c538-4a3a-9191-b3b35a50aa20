<template>
  <div>
    <main>
      <HeroBanner />
      <div class="container mx-auto px-4 py-8">
        <section class="mb-12">
          <h2 class="text-2xl font-bold text-center mb-8">
            COLEÇÕES EM DESTAQUE
          </h2>
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <!-- <PERSON> Marvel -->
            <div
              class="relative group cursor-pointer overflow-hidden rounded-lg shadow-lg"
            >
              <img
                src="https://images.unsplash.com/photo-1635805737707-575885ab0820"
                alt="Marvel"
                class="w-full h-[300px] object-cover transition-transform duration-300 group-hover:scale-110"
              />
              <div
                class="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent flex items-end"
              >
                <div class="p-6 w-full">
                  <h3 class="text-white text-2xl font-bold mb-2">MARVEL</h3>
                  <button
                    class="bg-red-600 text-white px-6 py-2 rounded-md hover:bg-red-700 transition-colors"
                    @click="navigateToCollection('marvel')"
                  >
                    Ver Coleção
                  </button>
                </div>
              </div>
            </div>

            <!-- Card DC -->
            <div
              class="relative group cursor-pointer overflow-hidden rounded-lg shadow-lg"
            >
              <img
                src="https://images.unsplash.com/photo-1531259683007-016a7b628fc3"
                alt="DC Comics"
                class="w-full h-[300px] object-cover transition-transform duration-300 group-hover:scale-110"
              />
              <div
                class="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent flex items-end"
              >
                <div class="p-6 w-full">
                  <h3 class="text-white text-2xl font-bold mb-2">DC COMICS</h3>
                  <button
                    class="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 transition-colors"
                    @click="navigateToCollection('dc')"
                  >
                    Ver Coleção
                  </button>
                </div>
              </div>
            </div>

            <!-- Card Star Wars -->
            <div
              class="relative group cursor-pointer overflow-hidden rounded-lg shadow-lg"
            >
              <img
                src="https://images.unsplash.com/photo-1608346128025-1896b97a6fa7"
                alt="Star Wars"
                class="w-full h-[300px] object-cover transition-transform duration-300 group-hover:scale-110"
              />
              <div
                class="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent flex items-end"
              >
                <div class="p-6 w-full">
                  <h3 class="text-white text-2xl font-bold mb-2">STAR WARS</h3>
                  <button
                    class="bg-yellow-600 text-white px-6 py-2 rounded-md hover:bg-yellow-700 transition-colors"
                    @click="navigateToCollection('star-wars')"
                  >
                    Ver Coleção
                  </button>
                </div>
              </div>
            </div>

            <!-- Card Anime -->
            <div
              class="relative group cursor-pointer overflow-hidden rounded-lg shadow-lg"
            >
              <img
                src="https://images.unsplash.com/photo-1607604276583-eef5d076aa5f"
                alt="Anime"
                class="w-full h-[300px] object-cover transition-transform duration-300 group-hover:scale-110"
              />
              <div
                class="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent flex items-end"
              >
                <div class="p-6 w-full">
                  <h3 class="text-white text-2xl font-bold mb-2">ANIME</h3>
                  <button
                    class="bg-purple-600 text-white px-6 py-2 rounded-md hover:bg-purple-700 transition-colors"
                    @click="navigateToCollection('anime')"
                  >
                    Ver Coleção
                  </button>
                </div>
              </div>
            </div>
          </div>
        </section>

        <NewReleases
          v-if="newReleases.length > 0"
          :items="newReleases"
          :loading="loading"
          :error="error"
          @see-all="navigateToCollection('new')"
        />
      </div>
    </main>
  </div>
</template>

<script setup lang="ts">
import HeroBanner from "~/components/HeroBanner.vue";
import NewReleases from "~/components/NewReleases.vue";
import { ref, onMounted } from "vue";
import { useRouter } from "vue-router";
import { useProductsStore } from "~/stores/productsStore";
import { useGoogleAdsConversion } from "~/composables/useGoogleAdsConversion";
import type { Figure } from "~/types/figures";

// Dados estruturados da loja
const storeStructuredData = {
  "@context": "https://schema.org",
  "@type": "Store",
  name: "Fidel Figures",
  description:
    "Loja especializada em action figures em resina de alta qualidade",
  image: "URL_DA_SUA_LOGO",
  address: {
    "@type": "PostalAddress",
    addressCountry: "BR",
  },
  priceRange: "R$$$",
  offers: {
    "@type": "AggregateOffer",
    priceCurrency: "BRL",
  },
};

// Configurar meta tags e dados estruturados
useHead({
  title: "Fidel Figures | Action Figures em Resina de Alta Qualidade",
  meta: [
    {
      name: "description",
      content:
        "Loja especializada em action figures em resina de alta qualidade. Colecionáveis Marvel, DC, Star Wars e Anime. Peças exclusivas e edições limitadas.",
    },
    {
      name: "keywords",
      content:
        "action figures, resina, colecionáveis, marvel, dc comics, star wars, anime, estátuas colecionáveis",
    },
    {
      property: "og:title",
      content: "Fidel Figures | Action Figures em Resina de Alta Qualidade",
    },
    {
      property: "og:description",
      content:
        "Loja especializada em action figures em resina de alta qualidade. Colecionáveis Marvel, DC, Star Wars e Anime.",
    },
    { property: "og:type", content: "website" },
  ],
  link: [{ rel: "canonical", href: "https://fidelfigures.com.br" }],
  script: [
    {
      type: "application/ld+json",
      children: JSON.stringify(storeStructuredData),
    },
  ],
});

const router = useRouter();
const productsStore = useProductsStore();

interface ReleaseItem {
  image: string;
  title: string;
  description?: string | undefined;
  buttonText: string;
}

const newReleases = ref<ReleaseItem[]>([]);
const loading = ref(true);
const error = ref<string | null>(null);

onMounted(async () => {
  try {
    loading.value = true;
    console.log("[index] Carregando novos lançamentos");

    // Verificar se a store já foi inicializada
    if (!productsStore.initialized) {
      console.log("[index] Store não inicializada, carregando produtos...");
      await productsStore.fetchAllProducts();
    }

    // Obter os lançamentos da store
    const releases = productsStore.getNewReleases;
    console.log(`[index] Encontrados ${releases.length} lançamentos`);

    newReleases.value = releases.map((figure: Figure) => ({
      image: figure.gallery_imgs[0],
      title: figure.title.toUpperCase(),
      description: figure.description,
      buttonText: "Pré-Venda",
    }));

    // Registrar evento de conversão de visita ao site
    // Nota: Este evento também é registrado no plugin google-ads.client.ts,
    // mas adicionamos aqui para garantir que seja registrado após a navegação
    setTimeout(() => {
      const { trackVisit } = useGoogleAdsConversion();
      trackVisit();
    }, 2000);
  } catch (err: any) {
    console.error("[index] Erro ao carregar lançamentos:", err);
    error.value =
      "Erro ao carregar lançamentos. Por favor, tente novamente mais tarde.";
  } finally {
    loading.value = false;
  }
});

const navigateToCollection = (category: string) => {
  router.push({
    path: "/collection",
    query: { category: category.toLowerCase() },
  });
};

// Definir o layout padrão
definePageMeta({
  layout: "default",
});
</script>
