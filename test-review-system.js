// Script para testar o sistema de avaliações
// Execute este código no console do navegador (F12) na página de pedidos

console.log('🧪 TESTE DO SISTEMA DE AVALIAÇÕES');
console.log('================================');

// Função para criar um pedido de teste
async function createTestOrder() {
  try {
    console.log('📦 Criando pedido de teste...');
    
    // Obter ID do usuário atual
    const userId = getUserId();
    if (!userId) {
      console.error('❌ Usuário não encontrado. Faça login primeiro.');
      return;
    }
    
    // Usar um produto de exemplo (você pode alterar este ID)
    const productId = 'exemplo-produto-id';
    
    const response = await fetch('/api/debug/create-test-order', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        userId: userId,
        productId: productId
      })
    });
    
    const result = await response.json();
    
    if (result.success) {
      console.log('✅ Pedido de teste criado com sucesso!');
      console.log('📋 ID do pedido:', result.order.id);
      console.log('🔄 Recarregue a página para ver o botão de avaliação');
      
      // Recarregar a página automaticamente
      setTimeout(() => {
        window.location.reload();
      }, 2000);
    } else {
      console.error('❌ Erro ao criar pedido:', result.message);
    }
    
  } catch (error) {
    console.error('❌ Erro na requisição:', error);
  }
}

// Função para obter o ID do usuário
function getUserId() {
  // Tentar várias formas de obter o ID do usuário
  try {
    // Método 1: Nuxt Auth
    if (window.$nuxt && window.$nuxt.$auth && window.$nuxt.$auth.user) {
      return window.$nuxt.$auth.user.uid;
    }
    
    // Método 2: Store do Nuxt
    if (window.$nuxt && window.$nuxt.$store) {
      const authUser = window.$nuxt.$store.state.auth?.user;
      if (authUser) return authUser.uid;
    }
    
    // Método 3: LocalStorage
    const authData = localStorage.getItem('auth.user');
    if (authData) {
      const user = JSON.parse(authData);
      return user.uid;
    }
    
    // Método 4: Procurar no DOM
    const userElement = document.querySelector('[data-user-id]');
    if (userElement) {
      return userElement.getAttribute('data-user-id');
    }
    
    return null;
  } catch (error) {
    console.error('Erro ao obter ID do usuário:', error);
    return null;
  }
}

// Função para ativar modo debug
function enableDebugMode() {
  console.log('🔧 Ativando modo debug...');
  localStorage.setItem('debug-reviews', 'true');
  console.log('✅ Modo debug ativado! Recarregue a página.');
}

// Função para desativar modo debug
function disableDebugMode() {
  console.log('🔧 Desativando modo debug...');
  localStorage.removeItem('debug-reviews');
  console.log('✅ Modo debug desativado! Recarregue a página.');
}

// Função para verificar status atual
function checkCurrentStatus() {
  console.log('\n🔍 VERIFICANDO STATUS ATUAL');
  console.log('==========================');
  
  const userId = getUserId();
  console.log('👤 ID do usuário:', userId || 'Não encontrado');
  
  const debugMode = localStorage.getItem('debug-reviews') === 'true';
  console.log('🔧 Modo debug:', debugMode ? 'ATIVADO' : 'DESATIVADO');
  
  const orderCards = document.querySelectorAll('.order-card, [class*="order"]');
  console.log('📦 Cards de pedidos encontrados:', orderCards.length);
  
  const reviewButtons = document.querySelectorAll('button:contains("Avaliar")');
  console.log('⭐ Botões de avaliação visíveis:', reviewButtons.length);
  
  const debugSections = document.querySelectorAll('[class*="yellow-50"]');
  console.log('🔍 Seções de debug visíveis:', debugSections.length);
}

// Função principal de teste
function runFullTest() {
  console.log('\n🚀 EXECUTANDO TESTE COMPLETO');
  console.log('============================');
  
  checkCurrentStatus();
  
  console.log('\n📋 OPÇÕES DISPONÍVEIS:');
  console.log('1. createTestOrder() - Criar pedido de teste');
  console.log('2. enableDebugMode() - Ativar modo debug');
  console.log('3. disableDebugMode() - Desativar modo debug');
  console.log('4. checkCurrentStatus() - Verificar status');
  
  console.log('\n💡 PASSOS RECOMENDADOS:');
  console.log('1. Execute: enableDebugMode()');
  console.log('2. Recarregue a página');
  console.log('3. Verifique as caixas amarelas de debug');
  console.log('4. Se necessário, execute: createTestOrder()');
}

// Disponibilizar funções globalmente
window.createTestOrder = createTestOrder;
window.enableDebugMode = enableDebugMode;
window.disableDebugMode = disableDebugMode;
window.checkCurrentStatus = checkCurrentStatus;
window.runFullTest = runFullTest;

// Executar teste inicial
runFullTest();

console.log('\n✅ Script carregado! Use as funções acima para testar.');
