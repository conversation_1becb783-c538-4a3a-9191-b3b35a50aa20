<template>
  <div class="space-y-6">
    <!-- Cabe<PERSON>lho -->
    <div class="flex items-center justify-between">
      <div>
        <h2 class="text-xl font-semibold text-gray-900">Minhas Avaliações</h2>
        <p class="text-sm text-gray-600 mt-1">
          Gerencie suas avaliações de produtos
        </p>
      </div>
      
      <!-- Estatísticas -->
      <div v-if="reviews.length > 0" class="text-right">
        <p class="text-2xl font-bold text-gray-900">{{ reviews.length }}</p>
        <p class="text-sm text-gray-600">avaliação{{ reviews.length > 1 ? 'ões' : '' }}</p>
      </div>
    </div>

    <!-- Filtros -->
    <div v-if="reviews.length > 0" class="flex flex-wrap gap-3">
      <select
        v-model="filterRating"
        class="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
      >
        <option value="">Todas as avaliações</option>
        <option value="5">5 estrelas</option>
        <option value="4">4 estrelas</option>
        <option value="3">3 estrelas</option>
        <option value="2">2 estrelas</option>
        <option value="1">1 estrela</option>
      </select>
      
      <select
        v-model="sortBy"
        class="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
      >
        <option value="newest">Mais recentes</option>
        <option value="oldest">Mais antigas</option>
        <option value="highest">Maior avaliação</option>
        <option value="lowest">Menor avaliação</option>
      </select>
    </div>

    <!-- Loading -->
    <div v-if="loading" class="space-y-4">
      <div v-for="i in 3" :key="i" class="animate-pulse">
        <div class="bg-gray-200 rounded-lg h-32"></div>
      </div>
    </div>

    <!-- Estado vazio -->
    <div v-else-if="reviews.length === 0" class="text-center py-12">
      <div class="w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
        <i class="fas fa-star text-2xl text-gray-400"></i>
      </div>
      <h3 class="text-lg font-medium text-gray-900 mb-2">Nenhuma avaliação ainda</h3>
      <p class="text-gray-600 mb-6">
        Você ainda não avaliou nenhum produto. Que tal começar avaliando seus pedidos?
      </p>
      <button
        @click="$router.push('/admin/area-do-cliente')"
        class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
      >
        <i class="fas fa-box mr-2"></i>
        Ver meus pedidos
      </button>
    </div>

    <!-- Lista de avaliações -->
    <div v-else class="space-y-4">
      <div
        v-for="review in filteredAndSortedReviews"
        :key="review.id"
        class="bg-white border border-gray-200 rounded-lg p-6 space-y-4"
      >
        <!-- Cabeçalho da avaliação -->
        <div class="flex items-start justify-between">
          <div class="flex-1">
            <!-- Produto -->
            <div class="flex items-center space-x-3 mb-3">
              <img
                v-if="review.productImage"
                :src="review.productImage"
                :alt="review.productName"
                class="w-12 h-12 object-cover rounded-lg"
              />
              <div class="w-12 h-12 bg-gray-200 rounded-lg flex items-center justify-center" v-else>
                <i class="fas fa-image text-gray-400"></i>
              </div>
              
              <div>
                <h3 class="font-medium text-gray-900">{{ review.productName || 'Produto' }}</h3>
                <p class="text-sm text-gray-600">Pedido #{{ review.orderId.substring(0, 8) }}</p>
              </div>
            </div>
            
            <!-- Avaliação -->
            <div class="flex items-center space-x-2 mb-2">
              <div class="flex items-center">
                <i
                  v-for="star in 5"
                  :key="star"
                  class="fas fa-star text-sm"
                  :class="star <= review.rating ? 'text-yellow-400' : 'text-gray-300'"
                ></i>
              </div>
              <span class="text-sm text-gray-600">{{ formatDate(review.createdAt) }}</span>
              <span v-if="review.updatedAt !== review.createdAt" class="text-xs text-gray-500">
                (editado)
              </span>
            </div>
            
            <!-- Comentário -->
            <p class="text-gray-700 leading-relaxed">{{ review.comment }}</p>
            
            <!-- Mídia -->
            <div v-if="review.images?.length || review.videos?.length" class="mt-3">
              <div v-if="review.images?.length" class="flex space-x-2 mb-2">
                <img
                  v-for="(image, index) in review.images.slice(0, 4)"
                  :key="index"
                  :src="image"
                  alt="Imagem da avaliação"
                  class="w-16 h-16 object-cover rounded-lg cursor-pointer hover:opacity-90"
                  @click="openImageModal(image)"
                />
                <div
                  v-if="review.images.length > 4"
                  class="w-16 h-16 bg-gray-100 rounded-lg flex items-center justify-center text-xs text-gray-600"
                >
                  +{{ review.images.length - 4 }}
                </div>
              </div>
              
              <div v-if="review.videos?.length" class="text-sm text-gray-600">
                <i class="fas fa-video mr-1"></i>
                {{ review.videos.length }} vídeo{{ review.videos.length > 1 ? 's' : '' }}
              </div>
            </div>
          </div>
          
          <!-- Ações -->
          <div class="flex flex-col space-y-2 ml-4">
            <button
              @click="editReview(review)"
              class="px-3 py-1 text-sm text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded-md transition-colors"
            >
              <i class="fas fa-edit mr-1"></i>
              Editar
            </button>
            
            <button
              @click="deleteReview(review)"
              class="px-3 py-1 text-sm text-red-600 hover:text-red-800 hover:bg-red-50 rounded-md transition-colors"
            >
              <i class="fas fa-trash mr-1"></i>
              Excluir
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Modal de edição -->
    <div
      v-if="showEditModal && editingReview"
      class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
      @click.self="closeEditModal"
    >
      <div class="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div class="p-6">
          <ReviewForm
            :user-id="userId"
            :product-id="editingReview.productId"
            :order-id="editingReview.orderId"
            :existing-review="editingReview"
            @submit="handleReviewUpdate"
            @cancel="closeEditModal"
          />
        </div>
      </div>
    </div>

    <!-- Modal de confirmação de exclusão -->
    <div
      v-if="showDeleteModal"
      class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
      @click.self="closeDeleteModal"
    >
      <div class="bg-white rounded-lg max-w-md w-full p-6">
        <div class="text-center">
          <div class="w-12 h-12 mx-auto mb-4 bg-red-100 rounded-full flex items-center justify-center">
            <i class="fas fa-exclamation-triangle text-red-600 text-xl"></i>
          </div>
          
          <h3 class="text-lg font-medium text-gray-900 mb-2">Excluir avaliação</h3>
          <p class="text-gray-600 mb-6">
            Tem certeza que deseja excluir esta avaliação? Esta ação não pode ser desfeita.
          </p>
          
          <div class="flex justify-center space-x-3">
            <button
              @click="closeDeleteModal"
              class="px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
            >
              Cancelar
            </button>
            
            <button
              @click="confirmDelete"
              :disabled="deleting"
              class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:opacity-50 transition-colors"
            >
              <i v-if="deleting" class="fas fa-spinner fa-spin mr-2"></i>
              Excluir
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, defineProps } from 'vue'
import type { IProductReview } from '~/types/customer'
import ReviewForm from './ReviewForm.vue'

const props = defineProps<{
  userId: string
}>()

// Estado
const reviews = ref<(IProductReview & { productName?: string; productImage?: string })[]>([])
const loading = ref(true)
const filterRating = ref('')
const sortBy = ref('newest')

// Modais
const showEditModal = ref(false)
const showDeleteModal = ref(false)
const editingReview = ref<IProductReview | null>(null)
const deletingReview = ref<IProductReview | null>(null)
const deleting = ref(false)

// Computed
const filteredAndSortedReviews = computed(() => {
  let filtered = reviews.value

  // Filtrar por rating
  if (filterRating.value) {
    filtered = filtered.filter(review => review.rating === Number(filterRating.value))
  }

  // Ordenar
  return filtered.sort((a, b) => {
    switch (sortBy.value) {
      case 'oldest':
        return new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
      case 'highest':
        return b.rating - a.rating
      case 'lowest':
        return a.rating - b.rating
      case 'newest':
      default:
        return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
    }
  })
})

// Métodos
const loadReviews = async () => {
  loading.value = true
  
  try {
    const response = await $fetch(`/api/reviews/user/${props.userId}`)
    
    if (response.success) {
      reviews.value = response.data.reviews
    }
  } catch (error) {
    console.error('Erro ao carregar avaliações:', error)
  } finally {
    loading.value = false
  }
}

const formatDate = (date: string | Date) => {
  const d = new Date(date)
  return d.toLocaleDateString('pt-BR', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

const editReview = (review: IProductReview) => {
  editingReview.value = review
  showEditModal.value = true
}

const closeEditModal = () => {
  showEditModal.value = false
  editingReview.value = null
}

const handleReviewUpdate = (updatedReview: IProductReview) => {
  // Atualizar a lista local
  const index = reviews.value.findIndex(r => r.id === updatedReview.id)
  if (index > -1) {
    reviews.value[index] = { ...reviews.value[index], ...updatedReview }
  }
  
  closeEditModal()
}

const deleteReview = (review: IProductReview) => {
  deletingReview.value = review
  showDeleteModal.value = true
}

const closeDeleteModal = () => {
  showDeleteModal.value = false
  deletingReview.value = null
}

const confirmDelete = async () => {
  if (!deletingReview.value) return
  
  deleting.value = true
  
  try {
    const response = await $fetch(`/api/reviews/${deletingReview.value.id}`, {
      method: 'DELETE',
      body: { userId: props.userId }
    })
    
    if (response.success) {
      // Remover da lista local
      reviews.value = reviews.value.filter(r => r.id !== deletingReview.value!.id)
      closeDeleteModal()
    }
  } catch (error) {
    console.error('Erro ao excluir avaliação:', error)
    alert('Erro ao excluir avaliação. Tente novamente.')
  } finally {
    deleting.value = false
  }
}

const openImageModal = (image: string) => {
  window.open(image, '_blank')
}

// Inicialização
onMounted(() => {
  loadReviews()
})
</script>
