import { collection, getDocs } from "firebase/firestore";
import { db } from "~/server/utils/firebase";
import { createError } from "h3";

const SECTIONS_COLLECTION = "sections";

// Cache em memória para seções
const CACHE_EXPIRATION_TIME = 60 * 60 * 1000; // 1 hora em milissegundos
let sectionsCache = {
  data: null,
  timestamp: 0
};

/**
 * Rota para listar todas as seções
 * GET /api/sections
 */
export default defineEventHandler(async (event) => {
  try {
    // Obter parâmetros de consulta
    const query = getQuery(event);
    const forceRefresh = query.refresh === 'true';

    // Verificar se podemos usar o cache
    const isCacheValid =
      !forceRefresh &&
      sectionsCache.data !== null &&
      (Date.now() - sectionsCache.timestamp) < CACHE_EXPIRATION_TIME;

    let sections = [];

    // Se o cache for válido, use o cache
    if (isCacheValid) {
      console.log('🔍 [API] Usando cache de seções');
      sections = sectionsCache.data;
    } else {
      console.log("🔍 [API] Buscando todas as seções do Firebase");

      // Buscar todas as seções usando Firebase Client SDK
      const sectionsRef = collection(db, SECTIONS_COLLECTION);
      const snapshot = await getDocs(sectionsRef);

      if (snapshot.empty) {
        console.log("🔍 [API] Nenhuma seção encontrada");
        return { sections: [] };
      }

      // Mapear os documentos para o formato da API
      sections = snapshot.docs.map(doc => {
        const data = doc.data();
        return {
          id: doc.id,
          title: data.title,
          description: data.description || "",
          products: data.products || [],
          isActive: data.isActive !== false,
          page: data.page || "home",
          createdAt: data.createdAt,
          updatedAt: data.updatedAt
        };
      });

      // Atualizar o cache
      sectionsCache.data = sections;
      sectionsCache.timestamp = Date.now();
      console.log('🔍 [API] Cache de seções atualizado');
    }

    console.log(`🔍 [API] ${sections.length} seções encontradas`);
    return { sections };
  } catch (error) {
    console.error("u274c [API] Erro ao buscar seções:", error);
    throw createError({
      statusCode: 500,
      statusMessage: "Erro ao buscar seções",
    });
  }
});
