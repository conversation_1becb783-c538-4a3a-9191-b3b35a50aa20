<template>
  <div>
    <main>
      <div class="container mx-auto px-4 py-8">
        <section class="mb-12">
          <h1 class="text-3xl font-bold text-center mb-8">
            PRODUTOS JÁ PRODUZIDOS
          </h1>

          <div v-if="error" class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6">
            <strong class="font-bold">Erro!</strong>
            <span class="block sm:inline"> {{ error }}</span>
          </div>

          <div v-else>
            <div class="bg-purple-100 border-l-4 border-purple-500 text-purple-700 p-4 mb-6 rounded">
              <p class="font-medium">Estes modelos já foram produzidos anteriormente.</p>
              <p class="text-sm mt-1">Diferentemente dos produtos em pré-lançamento, estes modelos já foram produzidos e testados, garantindo fidelidade às imagens apresentadas.</p>
            </div>

            <FiguresList
              :figures="previouslyProducedProducts"
              :loading="loading"
              :error="error"
              :hide-overlay="false"
            />
          </div>
        </section>
      </div>
    </main>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted } from "vue";
import type { FormattedFigure } from "~/types/figures";
import FiguresList from "~/components/FiguresList.vue";
import { usePreviouslyProducedStore } from "~/stores/previouslyProducedStore";

// SEO
useHead({
  title: "Produtos Já Produzidos | Fidel Figures",
  meta: [
    {
      name: "description",
      content: "Confira os produtos já produzidos da Fidel Figures. Action figures em resina de alta qualidade disponíveis para envio imediato.",
    },
  ],
  link: [{ rel: "canonical", href: "https://fidelfigures.com.br/produtos-ja-produzidos" }],
});

const previouslyProducedStore = usePreviouslyProducedStore();

// Computed properties
const previouslyProducedProducts = computed(() => previouslyProducedStore.products);
const loading = computed(() => previouslyProducedStore.loading);
const error = computed(() => previouslyProducedStore.error);

// Buscar produtos já produzidos
const fetchPreviouslyProducedProducts = async () => {
  await previouslyProducedStore.fetchPreviouslyProducedProducts();
};

// Inicialização
onMounted(() => {
  // Buscar produtos já produzidos
  fetchPreviouslyProducedProducts();
});
</script>
