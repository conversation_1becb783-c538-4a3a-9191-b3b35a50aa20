import { createError } from "h3";
import { collection, getDocs, query as firebaseQuery, where, orderBy, limit } from "firebase/firestore";
import { db } from "~/server/utils/firebase";
import { FigureData, formatFigureData } from "~/types/figures";
import { Coupon } from "~/types/coupons";
import { CACHE_EXPIRATION_TIME, getCachedCoupons, isCacheValid } from "~/server/utils/cache";

// Cache em memória para produtos
let productsCache = {
  data: null,
  timestamp: 0
};

// Usando a função getCachedCoupons do módulo de cache

export default defineEventHandler(async (event) => {
  try {
    // Obter parâmetros de consulta
    const queryParams = getQuery(event);
    const categoryId = queryParams.category as string;
    const search = queryParams.search as string;
    const page = parseInt(queryParams.page as string) || 1;
    const hasPageSize = queryParams.pageSize !== undefined;
    const pageSize = hasPageSize ? parseInt(queryParams.pageSize as string) : 0; // 0 significa sem limite
    const skipCount = (page - 1) * (pageSize || 0);
    const forceRefresh = queryParams.refresh === 'true';

    // Verificar se podemos usar o cache
    const cacheValid = isCacheValid(productsCache, forceRefresh);

    let products = [];
    let coupons = [];

    // Se o cache for válido e não houver filtros específicos, use o cache
    if (cacheValid && !categoryId) {
      console.log('🔍 [API] Usando cache de produtos');
      products = productsCache.data;
      // Buscar cupons do cache compartilhado
      coupons = await getCachedCoupons();
    } else {
      console.log('🔍 [API] Buscando produtos do Firebase');
      // Buscar todos os produtos
      const productsRef = collection(db, "products");
      let productsQuery;

      // Aplicar filtro por categoria se fornecido
      if (categoryId) {
        productsQuery = firebaseQuery(productsRef, where("categories", "array-contains", categoryId));
      } else {
        productsQuery = productsRef;
      }

      const querySnapshot = await getDocs(productsQuery);

      // Buscar cupons ativos para aplicar aos produtos usando o cache compartilhado
      coupons = await getCachedCoupons();

      // Processar os resultados
      products = querySnapshot.docs
        .filter(doc => {
          // Filtrar produtos com hidden = true
          const data = doc.data();
          return data.hidden !== true;
        })
        .map((doc) => {
          const figureData = { ...(doc.data() as FigureData) };
          return formatFigureData(figureData, doc.id, {
            coupons,
            ignoreCoupons: coupons.length === 0,
          });
        });

      // Atualizar o cache se não houver filtro de categoria
      if (!categoryId) {
        productsCache.data = products;
        productsCache.timestamp = Date.now();
        console.log('🔍 [API] Cache de produtos atualizado');
      }
    }

    // Aplicar filtro de busca se fornecido
    if (search) {
      const searchLower = search.toLowerCase();
      products = products.filter((product) => {
        return (
          product.title.toLowerCase().includes(searchLower) ||
          product.alias.toLowerCase().includes(searchLower) ||
          (product.description && product.description.toLowerCase().includes(searchLower))
        );
      });
    }

    // Aplicar paginação
    const totalCount = products.length;
    let paginatedProducts = products;

    // Só aplicar paginação se pageSize for definido
    if (hasPageSize && pageSize > 0) {
      paginatedProducts = products.slice(skipCount, skipCount + pageSize);
    }

    return {
      products: paginatedProducts,
      pagination: {
        total: totalCount,
        page,
        pageSize: hasPageSize ? pageSize : totalCount,
        pageCount: hasPageSize && pageSize > 0 ? Math.ceil(totalCount / pageSize) : 1,
      },
    };
  } catch (error) {
    console.error("Erro ao buscar produtos:", error);
    throw createError({
      statusCode: 500,
      message: "Erro ao buscar produtos",
    });
  }
});
