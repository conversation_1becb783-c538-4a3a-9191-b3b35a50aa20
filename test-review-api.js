// Script de teste para verificar se a API v2 de reviews está funcionando
const testReviewAPI = async () => {
  const baseURL = 'http://localhost:4001';
  
  // Dados de teste
  const testReviewData = {
    userId: 'test-user-123',
    productId: 'test-product-123', // Vamos testar com um ID que sabemos que não existe
    orderId: 'test-order-123',
    rating: 5,
    comment: 'Produto excelente! Muito bem feito e chegou rapidamente.',
    images: [],
    videos: []
  };

  console.log('🧪 Testando API v2 de Reviews...');
  console.log('📤 Dados de teste:', testReviewData);

  try {
    const response = await fetch(`${baseURL}/api/v2/reviews`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testReviewData),
    });

    console.log('📊 Status da resposta:', response.status);
    console.log('📊 Headers da resposta:', Object.fromEntries(response.headers.entries()));

    const responseData = await response.text();
    console.log('📊 Resposta bruta:', responseData);

    if (response.ok) {
      console.log('✅ API funcionando corretamente!');
      const jsonData = JSON.parse(responseData);
      console.log('📊 Dados da resposta:', jsonData);
    } else {
      console.log('❌ Erro na API:', response.status, response.statusText);
      try {
        const errorData = JSON.parse(responseData);
        console.log('📊 Detalhes do erro:', errorData);
      } catch (e) {
        console.log('📊 Resposta de erro (não JSON):', responseData);
      }
    }

  } catch (error) {
    console.error('❌ Erro ao fazer requisição:', error);
  }
};

// Executar o teste se este arquivo for executado diretamente
if (typeof window === 'undefined') {
  // Node.js environment
  testReviewAPI();
} else {
  // Browser environment
  window.testReviewAPI = testReviewAPI;
  console.log('🧪 Função testReviewAPI disponível no console do navegador');
}

module.exports = { testReviewAPI };
