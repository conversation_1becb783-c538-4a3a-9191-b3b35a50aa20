<template>
  <div v-if="show" class="fixed inset-0 z-50 overflow-y-auto">
    <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
      <!-- Overlay de fundo -->
      <div class="fixed inset-0 transition-opacity" @click="close">
        <div class="absolute inset-0 bg-gray-500 opacity-75"></div>
      </div>

      <!-- Modal -->
      <div
        class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-5xl sm:w-full"
      >
        <!-- Cabeçalho -->
        <div class="bg-gray-50 px-4 py-3 border-b flex justify-between items-center">
          <h3 class="text-lg font-medium text-gray-900">Detalhes do Cliente</h3>
          <button @click="close" class="text-gray-400 hover:text-gray-500">
            <i class="fas fa-times"></i>
          </button>
        </div>

        <!-- Conteúdo -->
        <div class="bg-white p-6">
          <div v-if="loading" class="flex justify-center py-8">
            <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-red-500"></div>
          </div>

          <div v-else-if="!cliente" class="text-center py-8 text-gray-500">
            Cliente não encontrado
          </div>

          <div v-else>
            <!-- Informações básicas -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
              <div class="bg-gray-50 p-4 rounded-lg">
                <h4 class="text-lg font-semibold mb-4 border-b pb-2">Informações Pessoais</h4>
                <div class="space-y-2">
                  <div class="flex">
                    <span class="font-medium w-24">Nome:</span>
                    <span>{{ cliente.name }}</span>
                  </div>
                  <div class="flex">
                    <span class="font-medium w-24">Email:</span>
                    <span>{{ cliente.email }}</span>
                  </div>
                  <div class="flex">
                    <span class="font-medium w-24">CPF:</span>
                    <span>{{ formatCPF(cliente.cpf) }}</span>
                  </div>
                  <div class="flex">
                    <span class="font-medium w-24">Telefone:</span>
                    <span>{{ formatPhone(cliente.phone) }}</span>
                  </div>
                  <div class="flex">
                    <span class="font-medium w-24">Cadastro:</span>
                    <span>{{ formatDate(cliente.createdAt) }}</span>
                  </div>
                  <div class="flex">
                    <span class="font-medium w-24">Última compra:</span>
                    <span>{{ cliente.lastPurchaseDate ? formatDate(cliente.lastPurchaseDate) : 'Nenhuma compra' }}</span>
                  </div>
                </div>
              </div>

              <div class="bg-gray-50 p-4 rounded-lg">
                <h4 class="text-lg font-semibold mb-4 border-b pb-2">Estatísticas</h4>
                <div class="grid grid-cols-2 gap-4">
                  <div class="bg-white p-4 rounded-lg shadow">
                    <div class="text-3xl font-bold text-red-600">{{ cliente.orders?.length || 0 }}</div>
                    <div class="text-sm text-gray-500">Pedidos Realizados</div>
                  </div>
                  <div class="bg-white p-4 rounded-lg shadow">
                    <div class="text-3xl font-bold text-blue-600">{{ cliente.wishlist?.length || 0 }}</div>
                    <div class="text-sm text-gray-500">Produtos Favoritos</div>
                  </div>
                  <div class="bg-white p-4 rounded-lg shadow">
                    <div class="text-3xl font-bold text-green-600">{{ cliente.addresses?.length || 0 }}</div>
                    <div class="text-sm text-gray-500">Endereços Cadastrados</div>
                  </div>
                  <div class="bg-white p-4 rounded-lg shadow">
                    <div class="text-3xl font-bold text-purple-600">{{ cliente.pendingOrders?.length || 0 }}</div>
                    <div class="text-sm text-gray-500">Pedidos Pendentes</div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Abas -->
            <div class="mb-4 border-b">
              <div class="flex">
                <button
                  v-for="tab in tabs"
                  :key="tab.id"
                  @click="activeTab = tab.id"
                  class="px-4 py-2 font-medium text-sm"
                  :class="activeTab === tab.id ? 'border-b-2 border-red-500 text-red-600' : 'text-gray-500 hover:text-gray-700'"
                >
                  {{ tab.name }}
                </button>
              </div>
            </div>

            <!-- Conteúdo das abas -->
            <div>
              <!-- Pedidos -->
              <div v-if="activeTab === 'pedidos'" class="space-y-4">
                <div v-if="!cliente.orders || cliente.orders.length === 0" class="text-center py-8 text-gray-500">
                  Este cliente não possui pedidos
                </div>
                <div v-else>
                  <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                      <thead class="bg-gray-50">
                        <tr>
                          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
                          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Data</th>
                          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Produto</th>
                          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Valor</th>
                          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Rastreio</th>
                        </tr>
                      </thead>
                      <tbody class="bg-white divide-y divide-gray-200">
                        <tr v-for="order in cliente.orders" :key="order.id" class="hover:bg-gray-50">
                          <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ order.id.substring(0, 8) }}</td>
                          <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ formatDate(order.createdAt) }}</td>
                          <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ getProductName(order.productId) }}</td>
                          <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ formatCurrency(order.total) }}</td>
                          <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center space-x-2">
                              <span
                                class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full"
                                :class="getStatusClass(order.status)"
                              >
                                {{ getStatusText(order.status) }}
                              </span>
                              <button
                                @click="openStatusModal(order)"
                                class="text-blue-600 hover:text-blue-800"
                                title="Editar status"
                              >
                                <i class="fas fa-edit text-xs"></i>
                              </button>
                            </div>
                          </td>
                          <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {{ order.trackCode || 'Não disponível' }}
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>

              <!-- Favoritos -->
              <div v-if="activeTab === 'favoritos'" class="space-y-4">
                <div v-if="!cliente.wishlist || cliente.wishlist.length === 0" class="text-center py-8 text-gray-500">
                  Este cliente não possui favoritos
                </div>
                <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  <div v-for="productId in cliente.wishlist" :key="productId" class="bg-white rounded-lg shadow overflow-hidden">
                    <div class="p-4 flex items-center space-x-4">
                      <div class="flex-shrink-0 w-16 h-16 bg-gray-100 rounded-md overflow-hidden">
                        <img :src="getProductImage(productId)" alt="Produto" class="w-full h-full object-cover" />
                      </div>
                      <div class="flex-1 min-w-0">
                        <p class="text-sm font-medium text-gray-900 truncate">{{ getProductName(productId) }}</p>
                        <p class="text-sm text-gray-500">{{ formatCurrency(getProductPrice(productId)) }}</p>
                      </div>
                      <div>
                        <button
                          @click="viewProduct(productId)"
                          class="inline-flex items-center px-2.5 py-1.5 border border-transparent text-xs font-medium rounded text-red-700 bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                        >
                          Ver
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Endereços -->
              <div v-if="activeTab === 'enderecos'" class="space-y-4">
                <div v-if="!cliente.addresses || cliente.addresses.length === 0" class="text-center py-8 text-gray-500">
                  Este cliente não possui endereços cadastrados
                </div>
                <div v-else class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div
                    v-for="(address, index) in cliente.addresses"
                    :key="index"
                    class="bg-white p-4 rounded-lg shadow"
                    :class="{ 'border-2 border-green-500': index === cliente.defaultAddressIndex }"
                  >
                    <div class="flex justify-between items-start mb-2">
                      <h5 class="font-medium">{{ address.name || 'Endereço ' + (index + 1) }}</h5>
                      <span
                        v-if="index === cliente.defaultAddressIndex"
                        class="px-2 py-1 bg-green-100 text-green-800 text-xs font-medium rounded-full"
                      >
                        Principal
                      </span>
                    </div>
                    <div class="text-sm text-gray-600 space-y-1">
                      <p>{{ address.street }}, {{ address.number }}</p>
                      <p v-if="address.complement">{{ address.complement }}</p>
                      <p>{{ address.neighborhood }}</p>
                      <p>{{ address.city }} - {{ address.state }}</p>
                      <p>CEP: {{ formatCEP(address.zipCode) }}</p>
                      <p v-if="address.phone">Telefone: {{ formatPhone(address.phone) }}</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Rodapé -->
        <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
          <button
            @click="close"
            class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm"
          >
            Fechar
          </button>
        </div>
      </div>
    </div>
    <!-- Modal de edição de status -->
    <div v-if="showStatusModal" class="fixed inset-0 z-50 overflow-y-auto">
      <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <!-- Overlay de fundo -->
        <div class="fixed inset-0 transition-opacity" @click="closeStatusModal">
          <div class="absolute inset-0 bg-gray-500 opacity-75"></div>
        </div>

        <!-- Modal -->
        <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
          <!-- Cabeçalho -->
          <div class="bg-gray-50 px-4 py-3 border-b flex justify-between items-center">
            <h3 class="text-lg font-medium text-gray-900">Atualizar Status do Pedido</h3>
            <button @click="closeStatusModal" class="text-gray-400 hover:text-gray-500">
              <i class="fas fa-times"></i>
            </button>
          </div>

          <!-- Conteúdo -->
          <div class="bg-white p-6">
            <div class="mb-4">
              <label class="block text-sm font-medium text-gray-700 mb-2">Status atual:</label>
              <span
                class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full"
                :class="getStatusClass(selectedOrder?.status)"
              >
                {{ getStatusText(selectedOrder?.status) }}
              </span>
            </div>

            <div class="mb-4">
              <label for="newStatus" class="block text-sm font-medium text-gray-700 mb-2">Novo status:</label>
              <select
                id="newStatus"
                v-model="newStatus"
                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-red-500 focus:border-red-500"
              >
                <option value="approved">Aprovado</option>
                <option value="pending">Pendente</option>
                <option value="rejected">Rejeitado</option>
                <option value="em produção">Em Produção</option>
                <option value="em fila de produção">Em Fila de Produção</option>
              </select>
            </div>
          </div>

          <!-- Rodapé -->
          <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
            <button
              @click="updateOrderStatus"
              class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm"
              :disabled="isUpdating"
            >
              <span v-if="isUpdating" class="mr-2">
                <i class="fas fa-spinner fa-spin"></i>
              </span>
              Salvar
            </button>
            <button
              @click="closeStatusModal"
              class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
            >
              Cancelar
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { useCustomerStore } from '~/stores/customerStore'
import { useProductsStore } from '~/stores/productsStore'
import type { IOrder } from '~/types/customer'
import { useFetch } from '~/composables/useFetch'

const props = defineProps<{
  show: boolean
  clienteId: string | null
}>()

const emit = defineEmits(['close'])

const customerStore = useCustomerStore()
const productsStore = useProductsStore()
const loading = ref(false)
const activeTab = ref('pedidos')

// Estado para o modal de edição de status
const showStatusModal = ref(false)
const selectedOrder = ref<IOrder | null>(null)
const newStatus = ref('')
const isUpdating = ref(false)

const tabs = [
  { id: 'pedidos', name: 'Pedidos' },
  { id: 'favoritos', name: 'Favoritos' },
  { id: 'enderecos', name: 'Endereços' }
]

const cliente = computed(() => {
  if (!props.clienteId) return null
  return customerStore.getCustomerById(props.clienteId)
})

watch(() => props.show, async (newVal) => {
  if (newVal && props.clienteId) {
    loading.value = true
    try {
      // Carregar cliente se necessário
      if (!cliente.value) {
        await customerStore.fetchCustomerById(props.clienteId)
      }

      // Carregar produtos se necessário
      if (!productsStore.initialized) {
        await productsStore.fetchAllProducts()
      }
    } catch (error) {
      console.error('Erro ao carregar dados do cliente:', error)
    } finally {
      loading.value = false
    }
  }
})

function close() {
  emit('close')
}

function formatCPF(cpf: string | undefined): string {
  if (!cpf) return ''
  return cpf.replace(/^(\d{3})(\d{3})(\d{3})(\d{2})$/, '$1.$2.$3-$4')
}

function formatPhone(phone: string | undefined): string {
  if (!phone) return ''
  return phone.replace(/^(\d{2})(\d{5})(\d{4})$/, '($1) $2-$3')
}

function formatCEP(cep: string | undefined): string {
  if (!cep) return ''
  return cep.replace(/^(\d{5})(\d{3})$/, '$1-$2')
}

function formatDate(timestamp: any): string {
  if (!timestamp) return ''

  try {
    // Verificar se é um timestamp do Firestore
    if (timestamp && timestamp.toDate && typeof timestamp.toDate === 'function') {
      return new Intl.DateTimeFormat('pt-BR', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      }).format(timestamp.toDate())
    }

    // Se for uma data normal
    if (timestamp instanceof Date) {
      return new Intl.DateTimeFormat('pt-BR', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      }).format(timestamp)
    }

    // Se for um número (timestamp em milissegundos)
    if (typeof timestamp === 'number') {
      return new Intl.DateTimeFormat('pt-BR', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      }).format(new Date(timestamp))
    }

    // Se for uma string, tentar converter para data
    if (typeof timestamp === 'string') {
      // Verificar se é uma string ISO
      if (/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/.test(timestamp)) {
        return new Intl.DateTimeFormat('pt-BR', {
          day: '2-digit',
          month: '2-digit',
          year: 'numeric',
          hour: '2-digit',
          minute: '2-digit'
        }).format(new Date(timestamp))
      }

      // Outras strings de data
      const date = new Date(timestamp)
      if (!isNaN(date.getTime())) {
        return new Intl.DateTimeFormat('pt-BR', {
          day: '2-digit',
          month: '2-digit',
          year: 'numeric',
          hour: '2-digit',
          minute: '2-digit'
        }).format(date)
      }
    }

    // Se chegou aqui, não conseguiu formatar
    console.warn('Formato de data não reconhecido:', timestamp)
    return 'Data inválida'
  } catch (error) {
    console.error('Erro ao formatar data:', error, timestamp)
    return 'Data inválida'
  }
}

function formatCurrency(value: number | undefined): string {
  if (value === undefined) return 'R$ 0,00'
  return new Intl.NumberFormat('pt-BR', {
    style: 'currency',
    currency: 'BRL'
  }).format(value)
}

function getProductName(productId: string): string {
  const product = productsStore.getProductById(productId)
  return product ? product.title : 'Produto não encontrado'
}

function getProductImage(productId: string): string {
  const product = productsStore.getProductById(productId)
  return product && product.gallery_imgs && product.gallery_imgs.length > 0
    ? product.gallery_imgs[0]
    : '/placeholder-image.jpg'
}

function getProductPrice(productId: string): number {
  const product = productsStore.getProductById(productId)
  return product ? product.base_price : 0
}

function getStatusClass(status: string | undefined): string {
  if (!status) return 'bg-gray-100 text-gray-800'

  switch (status.toLowerCase()) {
    case 'approved':
    case 'aprovado':
      return 'bg-green-100 text-green-800'
    case 'pending':
    case 'pendente':
      return 'bg-yellow-100 text-yellow-800'
    case 'rejected':
    case 'rejeitado':
      return 'bg-red-100 text-red-800'
    case 'em produção':
      return 'bg-blue-100 text-blue-800'
    case 'em fila de produção':
      return 'bg-purple-100 text-purple-800'
    case 'enviado':
      return 'bg-teal-100 text-teal-800'
    case 'concluída':
      return 'bg-emerald-100 text-emerald-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}

function getStatusText(status: string | undefined): string {
  if (!status) return 'Desconhecido'

  switch (status.toLowerCase()) {
    case 'approved':
    case 'aprovado':
      return 'Aprovado'
    case 'pending':
    case 'pendente':
      return 'Pendente'
    case 'rejected':
    case 'rejeitado':
      return 'Rejeitado'
    case 'em produção':
      return 'Em Produção'
    case 'em fila de produção':
      return 'Em Fila de Produção'
    case 'enviado':
      return 'Enviado'
    case 'concluída':
      return 'Concluída'
    default:
      return status
  }
}

function viewProduct(productId: string) {
  window.open(`/produto/${productId}`, '_blank')
}

// Função para abrir o modal de edição de status
function openStatusModal(order: IOrder) {
  selectedOrder.value = order
  newStatus.value = order.status || ''
  showStatusModal.value = true
}

// Função para fechar o modal de edição de status
function closeStatusModal() {
  showStatusModal.value = false
  selectedOrder.value = null
  newStatus.value = ''
}

// Função para atualizar o status do pedido
async function updateOrderStatus() {
  if (!selectedOrder.value || !props.clienteId) return

  try {
    isUpdating.value = true

    // Encontrar o pedido na lista de pedidos do cliente
    if (!cliente.value || !cliente.value.orders) {
      throw new Error('Cliente ou pedidos não encontrados')
    }

    // Encontrar o índice do pedido a ser atualizado
    const orderIndex = cliente.value.orders.findIndex(o => o.id === selectedOrder.value?.id)
    if (orderIndex === -1) {
      throw new Error('Pedido não encontrado na lista de pedidos do cliente')
    }

    // Criar uma cópia do pedido para atualização
    const updatedOrder = { ...cliente.value.orders[orderIndex] }
    updatedOrder.status = newStatus.value
    updatedOrder.updatedAt = new Date()

    // Atualizar o pedido usando a API do Nuxt
    const { error } = await useFetch(`/api/customers/${props.clienteId}/orders/${updatedOrder.id}`, {
      method: 'PUT',
      body: {
        status: newStatus.value
      }
    })

    if (error.value) {
      throw new Error(error.value.message || 'Erro ao atualizar status do pedido')
    }

    // Atualizar o cliente no store
    await customerStore.fetchCustomerById(props.clienteId)

    // Fechar o modal
    closeStatusModal()

  } catch (error) {
    console.error('Erro ao atualizar status do pedido:', error)
    alert('Erro ao atualizar status do pedido. Por favor, tente novamente.')
  } finally {
    isUpdating.value = false
  }
}

onMounted(async () => {
  // Garantir que os produtos estejam carregados
  if (!productsStore.initialized) {
    await productsStore.fetchAllProducts()
  }
})
</script>
