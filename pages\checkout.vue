<template>
  <div class="min-h-screen bg-gray-100">
    <HeaderBar />
    <main class="container mx-auto py-8 px-4 sm:px-6 lg:px-8">
      <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold text-gray-900 mb-6">Finalizar Compra</h1>

        <!-- Loading State -->
        <div v-if="loading" class="bg-white p-6 rounded-lg shadow-md mb-6">
          <div class="animate-pulse flex flex-col items-center">
            <div class="rounded-full bg-gray-300 h-12 w-12 mb-4"></div>
            <div class="h-4 bg-gray-300 rounded w-3/4 mb-2"></div>
            <div class="h-4 bg-gray-300 rounded w-1/2"></div>
          </div>
        </div>

        <!-- Error Message -->
        <div
          v-if="error"
          class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6"
          role="alert"
        >
          <p class="font-bold">Erro</p>
          <p>{{ error }}</p>
        </div>

        <!-- Success Message -->
        <div
          v-if="successMessage"
          class="bg-green-100 border-l-4 border-green-500 text-green-700 p-4 mb-6"
          role="alert"
        >
          <p class="font-bold">Sucesso</p>
          <p>{{ successMessage }}</p>
        </div>

        <!-- Product Summary -->
        <div
          v-if="product && !loading"
          class="bg-white p-6 rounded-lg shadow-md mb-6"
        >
          <h2 class="text-xl font-semibold text-gray-800 mb-4">
            Resumo do Pedido
          </h2>
          <div class="flex items-center">
            <div
              class="w-24 h-24 flex-shrink-0 overflow-hidden rounded-md border border-gray-200"
            >
              <img
                :src="product.gallery_imgs?.[0] || ''"
                :alt="product.alias"
                class="h-full w-full object-cover object-center"
              />
            </div>
            <div class="ml-4 flex-1">
              <h3 class="text-lg font-medium text-gray-900">
                {{ product.alias }}
              </h3>
              <p class="mt-1 text-sm text-gray-500">
                {{
                  product.description
                    ? product.description.substring(0, 100) +
                      (product.description.length > 100 ? "..." : "")
                    : ""
                }}
              </p>
              <div class="mt-2 flex justify-between">
                <p class="text-sm font-medium text-gray-900">Quantidade: 1</p>
                <div>
                  <p v-if="product.totalDiscount > 0" class="text-sm text-gray-500 line-through">
                    {{ product.formattedWithoutDiscount }}
                  </p>
                  <p class="text-lg font-bold text-red-600">
                    {{ product.formattedPrice }}
                  </p>
                  <p v-if="product.totalDiscount > 0" class="text-xs text-green-600 font-medium">
                    Economia de {{ product.totalDiscount }}%
                  </p>
                </div>
              </div>

              <!-- Informações de Produção e Entrega -->
              <div class="mt-4 p-3 bg-gray-50 rounded-lg">
                <div class="space-y-2">
                  <div class="flex items-center">
                    <svg class="h-5 w-5 mr-2 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <span class="text-sm text-gray-700">{{ formatProductionMessage }}</span>
                  </div>
                  <div class="flex items-center">
                    <svg class="h-5 w-5 mr-2 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                    <span class="text-sm font-medium text-green-700">{{ formatDeliveryDate }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Customer Information -->
        <div v-if="!loading" class="bg-white p-6 rounded-lg shadow-md mb-6">
          <h2 class="text-xl font-semibold text-gray-800 mb-4">
            Informações de Entrega
          </h2>

          <!-- Endereço existente -->
          <div v-if="customer?.addresses?.length" class="mb-4">
            <div
              v-for="(address, index) in customer.addresses"
              :key="index"
              class="border p-4 rounded-md mb-2 hover:border-blue-400 transition-colors"
              :class="{ 'border-blue-500 bg-blue-50': address.isDefault }"
            >
              <div class="flex justify-between items-start">
                <div>
                  <p class="font-medium">{{ address.name || customer.name }}</p>
                  <p>{{ address.street }}, {{ address.number }}</p>
                  <p v-if="address.complement">{{ address.complement }}</p>
                  <p>
                    {{ address.neighborhood }}, {{ address.city }} -
                    {{ address.state }}
                  </p>
                  <p>CEP: {{ address.zipCode }}</p>
                </div>
                <div class="flex flex-col items-end">
                  <div
                    v-if="address.isDefault"
                    class="bg-blue-500 text-white text-xs px-2 py-1 rounded mb-2"
                  >
                    Endereço de Entrega
                  </div>
                  <div class="flex space-x-2">
                    <button
                      v-if="!address.isDefault && address.id"
                      @click="setDefaultAddress(address.id)"
                      class="bg-green-500 hover:bg-green-600 text-white text-sm px-3 py-1 rounded transition-colors"
                    >
                      <i class="fas fa-check mr-1"></i> Usar este endereço
                    </button>
                    <button
                      @click="editAddress(address)"
                      class="text-blue-600 hover:text-blue-800 text-sm font-medium"
                    >
                      <i class="fas fa-edit mr-1"></i> Editar
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Sem endereço cadastrado -->
          <div
            v-else
            class="border border-dashed border-gray-300 p-4 rounded-md mb-4 text-center"
          >
            <p class="text-gray-600 mb-2">
              Você ainda não tem um endereço cadastrado
            </p>
            <button
              @click="showAddressModal = true"
              class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md transition-colors"
            >
              <i class="fas fa-plus-circle mr-2"></i> Adicionar Endereço
            </button>
          </div>

          <!-- Botão para adicionar novo endereço quando já existe algum -->
          <button
            v-if="customer?.addresses?.length"
            @click="showAddressModal = true"
            class="text-blue-600 hover:text-blue-800 text-sm font-medium"
          >
            <i class="fas fa-plus-circle mr-1"></i> Adicionar novo endereço
          </button>
        </div>

        <!-- Payment Methods -->
        <div v-if="!loading" class="bg-white p-6 rounded-lg shadow-md mb-6">
          <h2 class="text-xl font-semibold text-gray-800 mb-4">
            Método de Pagamento
          </h2>

          <!-- MercadoPago Payment -->
          <div
            v-if="loadingPreference"
            class="flex justify-center items-center py-8"
          >
            <div
              class="animate-spin rounded-full h-10 w-10 border-b-2 border-blue-500"
            ></div>
            <span class="ml-3 text-gray-600"
              >Carregando opções de pagamento...</span
            >
          </div>

          <!-- Botão para tentar novamente caso o carregamento falhe -->
          <div
            v-if="error && !loadingPreference"
            class="flex flex-col items-center justify-center py-4"
          >
            <p class="text-red-600 mb-4">{{ error }}</p>
            <button
              @click="createPreference"
              class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md transition-colors"
            >
              <i class="fas fa-sync-alt mr-2"></i> Tentar novamente
            </button>
          </div>

          <div id="wallet_container" class="w-full"></div>
        </div>
      </div>
    </main>

    <!-- Address Modal -->
    <AddressModal
      v-model="showAddressModal"
      :address="currentAddress"
      @address-saved="handleAddressSaved"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, onBeforeUnmount, computed } from "vue";
import { useRoute, useRouter } from "vue-router";
import { useAuthStore } from "~/stores/auth";
import { useCustomer } from "~/composables/useCustomer";
import { storeToRefs } from "pinia";
import type { MercadoPagoPayment } from "~/types/payment";
import type { IPendingOrder } from "~/types/customer";
import { Figure } from "~/types/figures";
import { v4 as uuidv4 } from "uuid";
import { parse, format, addDays } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import HeaderBar from "~/components/HeaderBar.vue";
import AddressModal from "~/components/AddressModal.vue";

// Não usamos middleware para evitar problemas de rota
// A verificação de autenticação é feita diretamente na página

const route = useRoute();
const router = useRouter();
const authStore = useAuthStore();
const { user } = authStore;
const { customer } = storeToRefs(useCustomer());

const loading = ref(true);
const error = ref("");

// Informações de tempo de produção
const config = useRuntimeConfig();
const productionTime = parseInt(config.public.PRODUCTION_TIME || '60');
const startDate = config.public.PRODUCTION_START_DATE || '20 de março';

const formatProductionMessage = computed(() => {
  return `Produto feito sob encomenda - Prazo de ${productionTime} dias corridos após o início da produção.`;
});

const formatDeliveryDate = computed(() => {
  // Converter a data de início para objeto Date
  const parsedStartDate = parse(startDate, 'd \'de\' MMMM', new Date(), { locale: ptBR });
  // Adicionar os dias de produção
  const deliveryDate = addDays(parsedStartDate, productionTime);
  // Formatar a data de entrega
  const formattedDeliveryDate = format(deliveryDate, "d 'de' MMMM", { locale: ptBR });

  return `Início da produção em ${startDate} com previsão de entrega para ${formattedDeliveryDate}`;
});
const successMessage = ref("");
const product = ref<Figure | null>(null);
const showAddressModal = ref(false);
const preferenceId = ref<string>();
const loadingPreference = ref(false);
const currentAddress = ref<any>(null);

// Verificamos a existência de endereços diretamente no template com customer?.addresses?.length

onMounted(async () => {
  // Verificar se o usuário está autenticado
  if (!user?.uid) {
    console.log("Usuário não autenticado, redirecionando para login");
    // Salvar a URL atual para redirecionamento após o login
    if (typeof localStorage !== "undefined") {
      localStorage.setItem("auth_return_url", route.fullPath);
    }
    router.push("/login");
    return;
  }

  // Garantir que o customer seja inicializado
  const customerStore = useCustomer();
  if (!customerStore.customer) {
    console.log("Inicializando dados do cliente...");
    await customerStore.initialize();
    console.log("Dados do cliente inicializados:", {
      "customer existe": !!customerStore.customer,
      "customer uid": customerStore.customer ? customerStore.customer.uid : null
    });
  }

  // Verificar se temos um ID de produto na URL
  const productId = route.query.productId as string;

  if (!productId) {
    error.value =
      "Produto não encontrado. Por favor, selecione um produto para comprar.";
    loading.value = false;
    return;
  }

  try {
    // Buscar dados do produto
    const { data } = await useFetch(`/api/figures/${productId}`, {
      key: productId,
      transform: (response: any) => {
        if (!response) return null;
        return response;
      },
    });

    if (!data.value) {
      error.value = "Produto não encontrado.";
      loading.value = false;
      return;
    }

    // Criar instância de Figure
    product.value = new Figure(data.value, data.value.id, data.value.coupons);

    // Log para debug do desconto
    console.log('Produto carregado com desconto:', {
      'base_price': product.value.base_price,
      'finalPrice': product.value.finalPrice,
      'totalDiscount': product.value.totalDiscount,
      'coupons': product.value.coupons
    });

    // Criar preferência de pagamento para MercadoPago após um pequeno atraso
    // para garantir que o componente esteja completamente montado
    console.log(
      "Produto carregado, aguardando montagem completa do componente antes de criar preferência"
    );

    // Usar setTimeout para garantir que o DOM esteja completamente renderizado
    setTimeout(async () => {
      console.log(
        "Chamando createPreference após montagem completa do componente"
      );
      await createPreference();
    }, 500); // 500ms de atraso para garantir que o DOM esteja pronto
  } catch (err: any) {
    console.error("Erro ao carregar produto:", err);
    error.value =
      "Erro ao carregar informações do produto. Por favor, tente novamente.";
  } finally {
    loading.value = false;
  }
});

// Criar preferência de pagamento para MercadoPago
const createPreference = async () => {
  // Iniciar criação de preferência
  // Verificar se já temos uma preferência ou se está carregando
  const container = document.getElementById("wallet_container");

  // Verificar se o container existe
  if (!container) {
    console.log(
      "Container do wallet não encontrado, tentando novamente em 500ms"
    );
    setTimeout(() => createPreference(), 500);
    return;
  }

  if (
    !product.value ||
    !user?.uid ||
    preferenceId.value ||
    (container.children && container.children.length > 0)
  ) {
    console.log("Condições para criar preferência não atendidas:", {
      produto: !!product.value,
      usuário: !!user?.uid,
      "preferenceId já existe": !!preferenceId.value,
      "container já tem filhos": container.children
        ? container.children.length > 0
        : false,
    });
    return;
  }

  console.log("Iniciando criação de preferência de pagamento");
  loadingPreference.value = true;

  try {
    // Obter o endereço de entrega (endereço padrão) - comentado pois não está sendo usado
    // const deliveryAddress = customer.value?.addresses?.find(addr => addr.isDefault);
    const pendingOrderId = uuidv4(); // Gerar um ID único para o pedido pendente

    // Criar um pedido pendente para o MercadoPago
    const pendingOrder: IPendingOrder = {
      id: pendingOrderId, // Usar o ID gerado anteriormente
      paymentId: "", // Será preenchido após o pagamento
      collectionId: "",
      collectionStatus: "pending",
      paymentType: "mercadopago",
      externalReference: product.value.id || "",
      merchantOrderId: "",
      preferenceId: "", // Será preenchido após criar a preferência
      siteId: "",
      processingMode: "",
      merchantAccountId: "",
      productId: product.value.id,
      total: Number(product.value.finalPrice),
      quantity: 1,
      createdAt: new Date(),
      updatedAt: new Date(),
      paymentProvider: "mercadopago",
    };

    // Garantir que o customer esteja inicializado antes de salvar o pedido pendente
    const customerStore = useCustomer();

    // Verificar se o customer existe e tem um UID válido
    if (!customerStore.customer || !customerStore.customer.uid) {
      console.log("Customer não inicializado ou sem UID, tentando inicializar...");
      await customerStore.initialize();

      // Verificar novamente após a inicialização
      if (!customerStore.customer || !customerStore.customer.uid) {
        console.error("Não foi possível inicializar o customer ou obter um UID válido");
        error.value = "Erro ao processar pagamento. Por favor, tente novamente ou faça login novamente.";
        loadingPreference.value = false;
        return;
      }
    }

    console.log("Salvando pedido pendente no Firebase primeiro...");
    console.log("Dados do customer:", {
      "customer existe": !!customerStore.customer,
      "customer uid": customerStore.customer.uid,
      "user uid": user?.uid
    });

    const savedPendingOrder = await customerStore.savePendingOrder(pendingOrder);

    // Se não conseguiu salvar o pedido pendente, verificar se é porque o produto já existe
    if (!savedPendingOrder) {
      console.error("Erro ao salvar pedido pendente no Firebase");

      // Verificar se o erro é porque o produto já está em um pedido pendente
      if (customerStore.error && customerStore.error.includes("já está em um pedido pendente")) {
        error.value = "Este produto já está em um pedido pendente. Finalize a compra anterior ou aguarde que ela expire.";
        loadingPreference.value = false;
        return;
      }

      error.value = customerStore.error || "Erro ao processar pagamento. Por favor, tente novamente.";
      loadingPreference.value = false;
      return;
    }

    console.log("Pedido pendente salvo no Firebase com sucesso:", savedPendingOrder);

    // O pedido já foi adicionado ao estado local pelo método savePendingOrder

    // Enviar notificação de pedido pendente
    try {
      console.log("Enviando notificação de pedido pendente para o produto:", product.value.alias);
      const notifyResponse = await $fetch(`/api/notify?type=pending_order&productName=${encodeURIComponent(product.value.alias)}&productId=${encodeURIComponent(product.value.id)}`, {
        method: "POST",
        body: {
          pendingOrder: savedPendingOrder,
          product: {
            id: product.value.id,
            name: product.value.alias,
            price: product.value.finalPrice,
            image: product.value.gallery_imgs?.[0] || ""
          }
        }
      });
      console.log("Notificação de pedido pendente enviada:", notifyResponse);
    } catch (notifyError) {
      console.error("Erro ao enviar notificação de pedido pendente:", notifyError);
      // Não interromper o fluxo se a notificação falhar
    }

    // Preparar os dados para a criação da preferência seguindo o formato da API do MercadoPago
    const response = await $fetch("/api/create_payment", {
      method: "POST",
      body: {
        items: [
          {
            title: product.value.alias || "Action Figure",
            description: `${product.value.alias || "Action Figure"} - Fidel Figures`,
            picture_url: product.value.gallery_imgs?.[0] || "",
            category_id: "collectibles",
            quantity: 1,
            currency_id: "BRL",
            unit_price: Number(product.value.finalPrice),
          },
        ],
        payer: {
          email: user.email || "",
          name: user.displayName || "",
          identification: {
            type: "CPF",
            number: customer.value?.cpf?.replace(/\D/g, '') || ""
          }
        },
        // Informações adicionais do produto para uso interno
        productId: product.value.id,
        productName: product.value.alias,
        productPrice: product.value.finalPrice?.toString(),
        productImage: product.value.gallery_imgs?.[0],
        pendingOrderId: pendingOrderId, // Incluir o ID do pedido pendente
      },
    });

    if ((response as unknown as MercadoPagoPayment).id) {
      const mpPreferenceId = (response as unknown as MercadoPagoPayment).id;
      console.log("Preferência criada com sucesso: ", response);
      preferenceId.value = mpPreferenceId;

      // Atualizar o pedido pendente com o ID da preferência
      // Primeiro encontrar o pedido pendente no estado local
      const pendingOrderToUpdate = savedPendingOrder;
      if (pendingOrderToUpdate) {
        // Atualizar o objeto com o ID da preferência
        const updatedOrder = {
          ...pendingOrderToUpdate,
          preferenceId: mpPreferenceId
        };

        // Verificar novamente se o customer existe e tem um UID válido
        if (!customerStore.customer || !customerStore.customer.uid) {
          console.log("Customer não inicializado ou sem UID ao atualizar preferência, tentando inicializar...");
          await customerStore.initialize();

          // Verificar novamente após a inicialização
          if (!customerStore.customer || !customerStore.customer.uid) {
            console.error("Não foi possível inicializar o customer ou obter um UID válido ao atualizar preferência");
            // Continuar mesmo assim, já que o pedido já foi salvo anteriormente
          }
        }

        // Salvar diretamente no Firebase primeiro
        console.log("Atualizando pedido pendente no Firebase com ID da preferência:", mpPreferenceId);
        await customerStore.savePendingOrder(updatedOrder);

        console.log("Pedido pendente atualizado com ID da preferência:", mpPreferenceId);
      } else {
        console.error("Não foi possível encontrar o pedido pendente para atualizar com o ID da preferência");
      }
    }
  } catch (err) {
    console.error("Erro ao criar preferência de pagamento:", err);
    error.value = "Erro ao processar pagamento. Por favor, tente novamente.";
  } finally {
    loadingPreference.value = false;
  }
};

// Inicializar MercadoPago quando preferenceId mudar
watch(preferenceId, async (newId, oldId) => {
  if (newId !== oldId) {
    await initWallet();
  }
});



// Inicializar wallet do MercadoPago
const initWallet = async () => {
  if (!preferenceId.value || typeof window === "undefined") {
    console.log("Não é possível inicializar o wallet:", {
      "preferenceId existe": !!preferenceId.value,
      "estamos no cliente": typeof window !== "undefined",
    });
    return;
  }

  console.log(
    "Inicializando wallet do MercadoPago com preferenceId:",
    preferenceId.value
  );

  try {
    // @ts-ignore
    const mp = new window.MercadoPago(
      useRuntimeConfig().public.PUBLIC_MERCADOPAGO_PUBLIC_KEY
    );
    const bricksBuilder = mp.bricks();

    const renderComponent = async () => {
      const container = document.getElementById("wallet_container");

      // Verificar se o container existe
      if (!container) {
        console.log(
          "Container do wallet não encontrado em initWallet, tentando novamente em 500ms"
        );
        setTimeout(() => initWallet(), 500);
        return;
      }

      // Se o container estiver vazio, cria o componente
      console.log(
        "Renderizando componente no container:",
        container.children.length
      );
      if (container.children.length === 0) {
        try {
          await bricksBuilder.create("wallet", "wallet_container", {
            initialization: {
              preferenceId: preferenceId.value,
            },
          });
          console.log("Componente do MercadoPago criado com sucesso");
        } catch (renderError) {
          console.error(
            "Erro ao renderizar componente do MercadoPago:",
            renderError
          );
          // Tentar novamente após um atraso
          setTimeout(() => initWallet(), 1000);
        }
      }
    };

    await renderComponent();
  } catch (err) {
    console.error("Erro ao inicializar o Mercado Pago:", err);
    error.value =
      "Erro ao carregar opções de pagamento. Por favor, tente novamente.";
  }
};

// Limpar o container do wallet quando o componente for destruído
onBeforeUnmount(() => {
  const container = document.getElementById("wallet_container");
  if (container) {
    container.innerHTML = "";
  }
});



// Função para editar um endereço
const editAddress = (address: any) => {
  currentAddress.value = address;
  showAddressModal.value = true;
};

// Handle address saved
const handleAddressSaved = () => {
  showAddressModal.value = false;
  currentAddress.value = null;
  // Recriar preferência de pagamento com o novo endereço
  createPreference();
};

// Definir endereço como padrão (endereço de entrega)
const setDefaultAddress = async (addressId: string) => {
  if (!customer.value) return;

  try {
    // Limpar mensagens anteriores
    error.value = "";
    successMessage.value = "";

    const customerStore = useCustomer();
    await customerStore.setDefaultAddress(addressId);

    // Mostrar mensagem de sucesso
    successMessage.value = "Endereço de entrega definido com sucesso!";
    console.log(`Endereço definido como padrão com sucesso!`);

    // Esconder a mensagem após 3 segundos
    setTimeout(() => {
      successMessage.value = "";
    }, 3000);

    // Recriar preferência de pagamento com o novo endereço
    createPreference();
  } catch (err: any) {
    console.error("Erro ao definir endereço como padrão:", err);
    error.value =
      err.message ||
      "Erro ao definir endereço como padrão. Por favor, tente novamente.";
  }
};
</script>
