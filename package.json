{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "dev:external": "nuxt dev --host", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "sitemap": "tsx scripts/generate-sitemap.ts", "teste": "env-cmd -f .env.teste nuxt dev"}, "dependencies": {"@headlessui/vue": "^1.7.23", "@mercadopago/sdk-js": "^0.0.3", "@nuxt/image": "^1.9.0", "@nuxtjs/robots": "^5.0.1", "@pinia-plugin-persistedstate/nuxt": "^1.2.1", "@pinia/nuxt": "^0.9.0", "@stripe/stripe-js": "^7.1.0", "@types/lodash": "^4.17.13", "date-fns": "^4.1.0", "firebase": "^11.0.2", "firebase-admin": "^13.0.2", "html2pdf.js": "^0.10.3", "lodash": "^4.17.21", "mercadopago": "^2.1.0", "nodemailer": "^6.10.0", "nuxt": "^3.14.1592", "pinia": "^2.3.1", "pinia-plugin-persistedstate": "^4.2.0", "sitemap": "^8.0.0", "stripe": "^18.0.0", "vue": "latest", "vue-router": "latest", "vue-stripe-js": "^2.0.2", "vue-the-mask": "^0.11.1", "vue3-google-signin": "^2.1.1", "vue3-touch-events": "^5.0.13", "xml-escape": "^1.1.0", "xmlbuilder2": "^3.1.1"}, "devDependencies": {"@nuxtjs/tailwindcss": "^6.12.2", "@types/xml-escape": "^1.1.3", "env-cmd": "^10.1.0", "tailwindcss": "^3.4.16", "tsx": "^4.19.2"}}