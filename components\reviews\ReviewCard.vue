<template>
  <div class="bg-white border border-gray-200 rounded-lg p-4 space-y-3">
    <!-- <PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON> a<PERSON>iação -->
    <div class="flex items-start justify-between">
      <div class="flex items-center space-x-3">
        <!-- Avatar do usuário -->
        <div class="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center overflow-hidden">
          <img
            v-if="review.userPhoto"
            :src="review.userPhoto"
            :alt="review.userName"
            class="w-full h-full object-cover"
          />
          <i v-else class="fas fa-user text-gray-400"></i>
        </div>
        
        <div>
          <h4 class="font-medium text-gray-900">{{ review.userName || 'Usuário Anônimo' }}</h4>
          <div class="flex items-center space-x-2">
            <!-- Estrelas -->
            <div class="flex items-center">
              <i
                v-for="star in 5"
                :key="star"
                class="fas fa-star text-sm"
                :class="star <= review.rating ? 'text-yellow-400' : 'text-gray-300'"
              ></i>
            </div>
            
            <!-- Badge de compra verificada -->
            <span
              v-if="review.isVerifiedPurchase"
              class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800"
            >
              <i class="fas fa-check-circle mr-1"></i>
              Compra verificada
            </span>
          </div>
          
          <!-- Data da avaliação -->
          <p class="text-sm text-gray-500">
            {{ formatDate(review.createdAt) }}
            <span v-if="review.updatedAt !== review.createdAt" class="ml-1">
              (editado)
            </span>
          </p>
        </div>
      </div>
      
      <!-- Menu de ações (apenas para o próprio usuário) -->
      <div v-if="canEdit" class="relative">
        <button
          @click="showMenu = !showMenu"
          class="p-1 text-gray-400 hover:text-gray-600 rounded-full hover:bg-gray-100"
        >
          <i class="fas fa-ellipsis-v"></i>
        </button>
        
        <div
          v-if="showMenu"
          v-click-outside="() => showMenu = false"
          class="absolute right-0 top-8 w-32 bg-white border border-gray-200 rounded-md shadow-lg z-10"
        >
          <button
            @click="editReview"
            class="w-full px-3 py-2 text-left text-sm text-gray-700 hover:bg-gray-50 flex items-center"
          >
            <i class="fas fa-edit mr-2"></i>
            Editar
          </button>
          <!-- Botão de excluir removido - apenas admins podem deletar avaliações -->
        </div>
      </div>
    </div>

    <!-- Comentário -->
    <div class="text-gray-700">
      <p class="leading-relaxed">{{ review.comment }}</p>
    </div>

    <!-- Mídia (fotos e vídeos) -->
    <div v-if="hasMedia" class="space-y-3">
      <!-- Imagens -->
      <div v-if="review.images && review.images.length > 0" class="grid grid-cols-2 md:grid-cols-3 gap-2">
        <div
          v-for="(image, index) in review.images"
          :key="`img-${index}`"
          class="aspect-square rounded-lg overflow-hidden cursor-pointer hover:opacity-90 transition-opacity"
          @click="openImageModal(image, index)"
        >
          <img
            :src="image"
            :alt="`Imagem ${index + 1} da avaliação`"
            class="w-full h-full object-cover"
          />
        </div>
      </div>
      
      <!-- Vídeos -->
      <div v-if="review.videos && review.videos.length > 0" class="space-y-2">
        <div
          v-for="(video, index) in review.videos"
          :key="`vid-${index}`"
          class="rounded-lg overflow-hidden"
        >
          <video
            :src="video"
            controls
            class="w-full max-h-64 bg-black"
            preload="metadata"
          >
            Seu navegador não suporta vídeos.
          </video>
        </div>
      </div>
    </div>

    <!-- Ações de interação -->
    <div class="flex items-center justify-between pt-2 border-t border-gray-100">
      <div class="flex items-center space-x-4 text-sm text-gray-500">
        <!-- Botão de útil -->
        <button
          @click="toggleHelpful"
          class="flex items-center space-x-1 hover:text-blue-600 transition-colors"
          :class="{ 'text-blue-600': isHelpful }"
        >
          <i class="fas fa-thumbs-up"></i>
          <span>Útil</span>
          <span v-if="helpfulCount > 0">({{ helpfulCount }})</span>
        </button>
        
        <!-- Botão de responder (se aplicável) -->
        <button
          v-if="canReply"
          @click="replyToReview"
          class="flex items-center space-x-1 hover:text-blue-600 transition-colors"
        >
          <i class="fas fa-reply"></i>
          <span>Responder</span>
        </button>
      </div>
      
      <!-- Indicador de tempo -->
      <span class="text-xs text-gray-400">
        {{ timeAgo(review.createdAt) }}
      </span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, defineEmits, defineProps } from 'vue'
import type { IProductReview } from '~/types/customer'
import type { IReview } from '~/types/reviews'

// Tipo unificado para compatibilidade
type ReviewType = IProductReview | IReview

const props = defineProps<{
  review: ReviewType
  currentUserId?: string
  canReply?: boolean
}>()

const emit = defineEmits<{
  'edit': [ReviewType]
  // 'delete' removido - apenas admins podem deletar
  'reply': [ReviewType]
  'helpful': [{ reviewId: string, helpful: boolean }]
}>()

// Estado local
const showMenu = ref(false)
const isHelpful = ref(false)
const helpfulCount = ref(0)

// Computed
const canEdit = computed(() => props.currentUserId === props.review.userId)
const hasMedia = computed(() => 
  (props.review.images && props.review.images.length > 0) || 
  (props.review.videos && props.review.videos.length > 0)
)

// Métodos
const formatDate = (date: string | Date) => {
  const d = new Date(date)
  return d.toLocaleDateString('pt-BR', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

const timeAgo = (date: string | Date) => {
  const now = new Date()
  const reviewDate = new Date(date)
  const diffInHours = Math.floor((now.getTime() - reviewDate.getTime()) / (1000 * 60 * 60))
  
  if (diffInHours < 1) return 'Agora há pouco'
  if (diffInHours < 24) return `${diffInHours}h atrás`
  
  const diffInDays = Math.floor(diffInHours / 24)
  if (diffInDays < 7) return `${diffInDays}d atrás`
  
  const diffInWeeks = Math.floor(diffInDays / 7)
  if (diffInWeeks < 4) return `${diffInWeeks}sem atrás`
  
  const diffInMonths = Math.floor(diffInDays / 30)
  return `${diffInMonths}m atrás`
}

const editReview = () => {
  showMenu.value = false
  emit('edit', props.review)
}

// Função deleteReview removida - apenas admins podem deletar avaliações

const replyToReview = () => {
  emit('reply', props.review)
}

const toggleHelpful = () => {
  isHelpful.value = !isHelpful.value
  helpfulCount.value += isHelpful.value ? 1 : -1
  
  emit('helpful', {
    reviewId: props.review.id,
    helpful: isHelpful.value
  })
}

const openImageModal = (image: string, index: number) => {
  // Implementar modal de imagem se necessário
  window.open(image, '_blank')
}

// Diretiva para fechar menu ao clicar fora
const vClickOutside = {
  mounted(el: HTMLElement, binding: any) {
    el.clickOutsideEvent = (event: Event) => {
      if (!(el === event.target || el.contains(event.target as Node))) {
        binding.value()
      }
    }
    document.addEventListener('click', el.clickOutsideEvent)
  },
  unmounted(el: HTMLElement) {
    document.removeEventListener('click', el.clickOutsideEvent)
  }
}
</script>
