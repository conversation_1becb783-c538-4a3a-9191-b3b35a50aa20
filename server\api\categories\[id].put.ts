import { createError } from "h3";
import { doc, getDoc, updateDoc } from "firebase/firestore";
import { db } from "~/server/utils/firebase";
import { Category, formatCategoryData } from "~/types/categories";
import { invalidateCategoriesCache } from "./index.get";

const CATEGORIES_COLLECTION = "categories";

/**
 * Rota para atualizar uma categoria existente
 * PUT /api/categories/:id
 */
export default defineEventHandler(async (event) => {
  try {
    // Obter o ID da categoria da URL
    const id = getRouterParam(event, "id");

    if (!id) {
      throw createError({
        statusCode: 400,
        statusMessage: "ID da categoria não fornecido",
      });
    }

    // Obter os dados da requisição
    const body = await readBody(event);

    console.log(`🔍 [API] Atualizando categoria com ID: ${id}`);

    // Verificar se a categoria existe
    const categoryRef = doc(db, CATEGORIES_COLLECTION, id);
    const categoryDoc = await getDoc(categoryRef);

    if (!categoryDoc.exists()) {
      console.log(`🔍 [API] Categoria com ID ${id} não encontrada`);
      throw createError({
        statusCode: 404,
        statusMessage: "Categoria não encontrada",
      });
    }

    // Dados a serem atualizados
    const updateData: Record<string, any> = {
      updatedAt: new Date().toISOString(),
    };

    // Adicionar apenas os campos que foram fornecidos
    if (body.name !== undefined) updateData.name = body.name;
    if (body.fatherId !== undefined) updateData.fatherId = body.fatherId;
    if (body.description !== undefined) updateData.description = body.description;

    // Atualizar a categoria
    await updateDoc(categoryRef, updateData);

    // Obter a categoria atualizada
    const updatedCategoryDoc = await getDoc(categoryRef);
    const data = updatedCategoryDoc.data();

    // Formatar a resposta
    const category: Category = {
      id: updatedCategoryDoc.id,
      name: data?.name || "",
      fatherId: data?.fatherId || null,
      description: data?.description || "",
      createdAt: data?.createdAt || "",
      updatedAt: data?.updatedAt || "",
    };

    console.log(`u2705 [API] Categoria ${category.name} atualizada com sucesso`);

    // Invalidar o cache de categorias para forçar recarregamento
    invalidateCategoriesCache();

    return category;
  } catch (error: any) {
    console.error("u274c [API] Erro ao atualizar categoria:", error);
    throw createError({
      statusCode: error.statusCode || 500,
      statusMessage: error.statusMessage || "Erro ao atualizar categoria",
    });
  }
});
