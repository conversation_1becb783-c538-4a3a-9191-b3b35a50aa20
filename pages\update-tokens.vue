<template>
  <div>
    <!-- Header Admin -->
    <HeaderAdmin />
    
    <div class="min-h-screen bg-gray-100 py-8">
      <div class="container mx-auto px-4">
        <div class="max-w-2xl mx-auto bg-white p-6 rounded-lg shadow-lg">
          <h1 class="text-2xl font-bold mb-6">Atualizar Palavras de Busca</h1>
          
          <div class="mb-6">
            <p class="text-gray-600 mb-4">
              Esta ferramenta verifica todas as figuras e adiciona palavras-chave para melhorar a busca.
              Por exemplo, para uma figura com título "Dragon Ball Goku", as palavras extraídas serão: ["dragon ball goku", "dragon", "ball", "goku"].
            </p>
            <p class="text-gray-600">
              Apenas figuras que não têm palavras-chave ou que tiveram o título alterado serão atualizadas.
            </p>
          </div>

          <div class="space-y-4">
            <button
              @click="updateTokens"
              class="w-full bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg font-medium disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              :disabled="loading"
            >
              <span v-if="loading">
                <span class="inline-block animate-spin mr-2">⟳</span>
                Processando... (Isso pode levar alguns minutos)
              </span>
              <span v-else>
                Verificar e Atualizar Palavras
              </span>
            </button>

            <!-- Aviso sobre tempo de processamento -->
            <div v-if="loading" class="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded">
              <div class="flex items-center">
                <span class="inline-block animate-pulse mr-2">⚠️</span>
                <div>
                  <p class="font-medium">Processamento em andamento</p>
                  <p class="text-sm">Esta operação pode levar alguns minutos para ser concluída. Por favor, não feche esta página.</p>
                </div>
              </div>
            </div>

            <div v-if="result" :class="[
              'p-4 rounded-lg space-y-2',
              result.success ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'
            ]">
              <div class="font-medium">
                {{ result.success ? '✅ Verificação concluída!' : '❌ Erro na verificação' }}
              </div>
              
              <div v-if="result.success" class="space-y-1 text-sm">
                <div>Total de figuras encontradas: {{ result.stats.total }}</div>
                <div>Figuras processadas: {{ result.stats.processado }}</div>
                <div>Figuras atualizadas: {{ result.stats.atualizado }}</div>
                <div>Figuras puladas: {{ result.stats.pulado }}</div>
              </div>
              <div v-else>
                {{ result.message }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useAdmin } from '~/composables/useAdmin'
import HeaderAdmin from '~/components/HeaderAdmin.vue'


const loading = ref(false)
const result = ref(null)
const { updateAllFiguresWithSearchTokens } = useAdmin()

const updateTokens = async () => {
  loading.value = true
  result.value = null
  
  try {
    result.value = await updateAllFiguresWithSearchTokens()
  } catch (error) {
    result.value = {
      success: false,
      message: 'Ocorreu um erro ao verificar as figuras. Por favor, tente novamente.'
    }
  } finally {
    loading.value = false
  }
}
</script>
