import { doc, getDoc, updateDoc, arrayUnion, Timestamp } from "firebase/firestore";
import { db } from "~/server/utils/firebase";
import type { IProductReview } from "~/types/customer";
import { v4 as uuidv4 } from "uuid";

const USERS_COLLECTION = "users";

/**
 * Rota para criar uma nova avaliação de produto
 * POST /api/reviews
 */
export default defineEventHandler(async (event) => {
  try {
    // Obter os dados da requisição
    const body = await readBody(event);
    
    const { userId, productId, orderId, rating, comment, images, videos } = body;
    
    // Validações básicas
    if (!userId) {
      throw createError({
        statusCode: 400,
        statusMessage: "ID do usuário é obrigatório",
      });
    }
    
    if (!productId) {
      throw createError({
        statusCode: 400,
        statusMessage: "ID do produto é obrigatório",
      });
    }
    
    if (!orderId) {
      throw createError({
        statusCode: 400,
        statusMessage: "ID do pedido é obrigatório",
      });
    }
    
    if (!rating || rating < 1 || rating > 5) {
      throw createError({
        statusCode: 400,
        statusMessage: "Avaliação deve ser entre 1 e 5 estrelas",
      });
    }
    
    if (!comment || comment.trim().length === 0) {
      throw createError({
        statusCode: 400,
        statusMessage: "Comentário é obrigatório",
      });
    }
    
    console.log(`🔍 [API] Criando avaliação para produto ${productId} pelo usuário ${userId}`);
    
    // Verificar se o usuário existe
    const userRef = doc(db, USERS_COLLECTION, userId);
    const userDoc = await getDoc(userRef);
    
    if (!userDoc.exists()) {
      console.log(`❌ [API] Usuário com ID ${userId} não encontrado`);
      throw createError({
        statusCode: 404,
        statusMessage: "Usuário não encontrado",
      });
    }
    
    const userData = userDoc.data();
    
    // Verificar se o usuário realmente comprou o produto
    const userOrder = userData.orders?.find((order: any) =>
      order.id === orderId &&
      order.productId === productId &&
      (order.status === 'approved' || order.collectionStatus === 'approved')
    );

    if (!userOrder) {
      console.log(`❌ [API] Usuário ${userId} não tem pedido aprovado para o produto ${productId}`);
      throw createError({
        statusCode: 403,
        statusMessage: "Você só pode avaliar produtos que comprou",
      });
    }

    // Verificar se o pedido foi feito há pelo menos 1 hora (para evitar avaliações muito rápidas)
    const orderDate = userOrder.createdAt?.toDate ? userOrder.createdAt.toDate() : new Date(userOrder.createdAt);
    const hoursSinceOrder = (new Date().getTime() - orderDate.getTime()) / (1000 * 60 * 60);

    if (hoursSinceOrder < 1) {
      console.log(`❌ [API] Pedido ${orderId} foi feito há menos de 1 hora`);
      throw createError({
        statusCode: 400,
        statusMessage: "Aguarde pelo menos 1 hora após a compra para avaliar o produto",
      });
    }
    
    // Verificar se o usuário já avaliou este produto
    const existingReview = userData.reviews?.find((review: any) => 
      review.productId === productId
    );
    
    if (existingReview) {
      console.log(`❌ [API] Usuário ${userId} já avaliou o produto ${productId}`);
      throw createError({
        statusCode: 409,
        statusMessage: "Você já avaliou este produto",
      });
    }
    
    // Criar a nova avaliação
    const newReview: IProductReview = {
      id: uuidv4(),
      userId,
      productId,
      orderId,
      rating: Number(rating),
      comment: comment.trim(),
      images: Array.isArray(images) ? images : [],
      videos: Array.isArray(videos) ? videos : [],
      createdAt: new Date(),
      updatedAt: new Date(),
      isVerifiedPurchase: true,
    };
    
    // Adicionar a avaliação ao usuário
    await updateDoc(userRef, {
      reviews: arrayUnion(newReview),
      updatedAt: Timestamp.now(),
    });
    
    console.log(`✅ [API] Avaliação criada com sucesso para produto ${productId}`);
    
    return {
      success: true,
      message: "Avaliação criada com sucesso",
      review: {
        ...newReview,
        createdAt: newReview.createdAt.toISOString(),
        updatedAt: newReview.updatedAt.toISOString(),
      },
    };
    
  } catch (error: any) {
    console.error("❌ [API] Erro ao criar avaliação:", error);
    
    // Se for um erro já tratado, repassar
    if (error.statusCode) {
      throw error;
    }
    
    // Erro genérico
    throw createError({
      statusCode: 500,
      statusMessage: "Erro interno do servidor ao criar avaliação",
    });
  }
});
