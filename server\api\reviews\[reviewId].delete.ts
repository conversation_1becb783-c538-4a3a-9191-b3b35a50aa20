import { doc, getDoc, updateDoc, Timestamp } from "firebase/firestore";
import { db } from "~/server/utils/firebase";

const USERS_COLLECTION = "users";

/**
 * Rota para deletar uma avaliação
 * DELETE /api/reviews/:reviewId
 */
export default defineEventHandler(async (event) => {
  try {
    // Obter o ID da avaliação da URL
    const reviewId = getRouterParam(event, "reviewId");
    
    if (!reviewId) {
      throw createError({
        statusCode: 400,
        statusMessage: "ID da avaliação não fornecido",
      });
    }
    
    // Obter os dados da requisição
    const body = await readBody(event);
    const { userId } = body;
    
    // Validações básicas
    if (!userId) {
      throw createError({
        statusCode: 400,
        statusMessage: "ID do usuário é obrigatório",
      });
    }
    
    console.log(`🔍 [API] Deletando avaliação ${reviewId} do usuário ${userId}`);
    
    // Verificar se o usuário existe
    const userRef = doc(db, USERS_COLLECTION, userId);
    const userDoc = await getDoc(userRef);
    
    if (!userDoc.exists()) {
      console.log(`❌ [API] Usuário com ID ${userId} não encontrado`);
      throw createError({
        statusCode: 404,
        statusMessage: "Usuário não encontrado",
      });
    }
    
    const userData = userDoc.data();
    const reviews = userData.reviews || [];
    
    // Encontrar a avaliação
    const reviewIndex = reviews.findIndex((review: any) => review.id === reviewId);
    
    if (reviewIndex === -1) {
      console.log(`❌ [API] Avaliação ${reviewId} não encontrada`);
      throw createError({
        statusCode: 404,
        statusMessage: "Avaliação não encontrada",
      });
    }
    
    // Verificar se a avaliação pertence ao usuário
    if (reviews[reviewIndex].userId !== userId) {
      console.log(`❌ [API] Usuário ${userId} não tem permissão para deletar avaliação ${reviewId}`);
      throw createError({
        statusCode: 403,
        statusMessage: "Você só pode deletar suas próprias avaliações",
      });
    }
    
    // Remover a avaliação do array
    const updatedReviews = reviews.filter((review: any) => review.id !== reviewId);
    
    // Salvar no Firebase
    await updateDoc(userRef, {
      reviews: updatedReviews,
      updatedAt: Timestamp.now(),
    });
    
    console.log(`✅ [API] Avaliação ${reviewId} deletada com sucesso`);
    
    return {
      success: true,
      message: "Avaliação deletada com sucesso",
    };
    
  } catch (error: any) {
    console.error("❌ [API] Erro ao deletar avaliação:", error);
    
    // Se for um erro já tratado, repassar
    if (error.statusCode) {
      throw error;
    }
    
    // Erro genérico
    throw createError({
      statusCode: 500,
      statusMessage: "Erro interno do servidor ao deletar avaliação",
    });
  }
});
