import { createError } from "h3";
import { collection, doc, getDoc, getDocs } from "firebase/firestore";
import { db } from "~/server/utils/firebase";
import { Figure, FigureData, formatFigureData } from "~/types/figures";
import { Coupon } from "~/types/coupons";
const COUPONS_COLLECTION = "coupons";

async function getCoupons() {
  try {

    const couponsRef = collection(db, COUPONS_COLLECTION);

    const querySnapshot = await getDocs(couponsRef);

    const coupons = querySnapshot.docs
      .map((doc) => {
        const data = doc.data();

        try {
          const endAt = data.endAt?.toDate();
          // Se o cupom já expirou, retorna null
          if (endAt && endAt < new Date()) {
            return null;
          }

          return {
            id: doc.id,
            ...data,
            createdAt: data.createdAt?.toDate(),
            endAt: endAt,
          };
        } catch (dateError) {
          console.error("Erro ao converter datas do cupom:", doc.id, dateError);
          return null;
        }
      })
      .filter(Boolean) as unknown as Coupon[];

    return coupons;
  } catch (error) {
    console.error("Erro ao buscar cupons:", error);
    return [];
  }
}

export default defineEventHandler(async (event) => {
  const id = event.context.params?.id;
  console.log(`🔍 [API] [figures] Buscando figura com ID: ${id}`);

  if (!id) {
    console.log(`❌ [API] [figures] ID não fornecido`);
    throw createError({
      statusCode: 400,
      message: "ID não fornecido",
    });
  }

  try {
    // Buscar o produto
    console.log(`🔍 [API] [figures] Buscando produto no Firestore: ${id}`);
    const productRef = doc(db, "products", id);
    const productSnap = await getDoc(productRef);

    if (!productSnap.exists()) {
      console.log(`❌ [API] [figures] Produto não encontrado no Firestore: ${id}`);
      throw createError({
        statusCode: 404,
        message: "Produto não encontrado",
      });
    }

    // Verificar se o produto está oculto
    const productData = productSnap.data();
    console.log(`✅ [API] [figures] Produto encontrado: ${id}`, {
      hidden: productData.hidden,
      title: productData.title,
      base_price: productData.base_price
    });

    if (productData.hidden === true) {
      console.log(`❌ [API] [figures] Produto está oculto: ${id}`);
      throw createError({
        statusCode: 404,
        message: "Produto não encontrado",
      });
    }

    // Buscar cupons ativos para o produto
    console.log(`🔍 [API] [figures] Buscando cupons para o produto: ${id}`);
    const _coupons = await getCoupons();


    try {
      const figureData = { ...(productSnap.data() as FigureData) };
      console.log(`🔍 [API] [figures] Dados brutos do produto:`, figureData);

      // Verificar se os dados essenciais estão presentes
      if (!figureData.title || !figureData.base_price) {
        console.error('❌ [API] [figures] Dados incompletos para o produto:', productSnap.id, {
          title: figureData.title,
          base_price: figureData.base_price
        });
        throw createError({
          statusCode: 404,
          message: "Produto com dados incompletos",
        });
      }

      console.log(`🔍 [API] [figures] Formatando dados do produto: ${productSnap.id}`);
      const figure = formatFigureData(
        figureData,
        productSnap.id,
        {
          coupons: _coupons,
          ignoreCoupons: _coupons.length === 0,
        }
      );
      console.log(`✅ [API] [figures] Dados formatados com sucesso:`, {
        id: figure.id,
        title: figure.title,
        base_price: figure.base_price,
        finalPrice: figure.finalPrice
      });

      console.log(`🔍 [API] [figures] Criando instância de Figure`);
      const figureInstance = new Figure(figure, figure.id, _coupons);
      console.log(`✅ [API] [figures] Instância criada com sucesso`);

      const result = figureInstance.toJSON();
      console.log(`✅ [API] [figures] Retornando JSON da figura`);
      return result;
    } catch (formatError) {
      console.error('❌ [API] [figures] Erro ao formatar dados do produto:', formatError);
      throw createError({
        statusCode: 500,
        message: "Erro ao processar dados do produto",
      });
    }
  } catch (error) {
    console.error("❌ [API] [figures] Erro ao buscar produto:", error);

    // Verificar se o erro já é um erro HTTP
    if (error.statusCode) {
      console.log(`❌ [API] [figures] Retornando erro HTTP: ${error.statusCode}`);
      throw error;
    }

    // Se for um erro do Firebase, pode ter detalhes específicos
    if (error.code) {
      console.log(`❌ [API] [figures] Erro do Firebase: ${error.code}`);
    }

    throw createError({
      statusCode: 500,
      message: "Erro ao buscar produto",
    });
  }
});
