import { collection, getDocs, doc, updateDoc, query, limit } from "firebase/firestore";
import { db } from "~/server/utils/firebase";
import { FILTER_WORDS_TO_REMOVE, FILTER_REGEX_PATTERNS } from "~/config/constants";

const COLLECTION_NAME = "products";

// Gerar tokens de busca para um título
const generateSearchTokens = (title: string): string[] => {
  if (!title) return [];

  const tokens = new Set<string>();
  const normalizedTitle = title.toLowerCase().trim();

  // Adiciona o título completo após remover palavras filtradas
  let processedTitle = normalizedTitle;

  // Remove padrões de regex primeiro
  FILTER_REGEX_PATTERNS.forEach((pattern) => {
    processedTitle = processedTitle.replace(pattern, "");
  });

  // Remove palavras específicas
  FILTER_WORDS_TO_REMOVE.forEach((word) => {
    processedTitle = processedTitle.replace(
      new RegExp(`\\b${word}\\b`, "gi"),
      ""
    );
  });

  processedTitle = processedTitle.trim();

  // Adiciona cada palavra individual, excluindo as palavras filtradas
  const words = processedTitle.split(/\s+/);
  words.forEach((word) => {
    const normalizedWord = word.toLowerCase();
    if (
      word.length >= 2 &&
      !FILTER_WORDS_TO_REMOVE.includes(normalizedWord) &&
      !FILTER_REGEX_PATTERNS.some((pattern) => pattern.test(normalizedWord))
    ) {
      tokens.add(normalizedWord);
    }
  });

  return Array.from(tokens);
};

// Remover campos undefined de um objeto
const removeUndefinedFields = (obj: any) => {
  const clean: any = {};
  Object.entries(obj).forEach(([key, value]) => {
    if (value !== undefined && value !== null) {
      clean[key] = value;
    }
  });
  return clean;
};

export default defineEventHandler(async (event) => {
  try {
    console.log("🔄 [API TEST] Iniciando verificação de figures (TESTE - apenas 5 figuras)...");
    
    let totalProcessado = 0;
    let totalAtualizado = 0;
    let totalPulado = 0;

    // TESTE: Buscar apenas 5 figuras para teste
    const testQuery = query(collection(db, COLLECTION_NAME), limit(5));
    const snapshot = await getDocs(testQuery);
    console.log("📦 [API TEST] Total de figures encontradas para teste:", snapshot.size);

    for (const document of snapshot.docs) {
      totalProcessado++;
      const data = document.data();
      console.log(
        `[API TEST] [${totalProcessado}/${snapshot.size}] Verificando: ${
          data.title || "Sem título"
        }`
      );

      if (!data.title) {
        console.log(`⚠️ [API TEST] Figure sem título, pulando...`);
        totalPulado++;
        continue;
      }

      // Gerar novos tokens
      const newTokens = generateSearchTokens(data.title);
      console.log(`[API TEST] Tokens gerados para "${data.title}":`, newTokens);

      // Verificar se precisa atualizar
      const needsUpdate =
        !data.search_tokens || // Não tem tokens
        !Array.isArray(data.search_tokens) || // Tokens não é um array
        data.search_tokens.length === 0;
      
      if (needsUpdate) {
        console.log(`[API TEST] - Atualizando palavras:`, newTokens);
        const update = removeUndefinedFields({ search_tokens: newTokens });
        await updateDoc(doc(db, COLLECTION_NAME, document.id), update);
        totalAtualizado++;
        console.log(`✅ [API TEST] Palavras atualizadas para figure: ${data.title}`);
      } else {
        console.log(`[API TEST] - Palavras já existentes:`, data.search_tokens);
        console.log(`[API TEST] - Pulando...`);
        totalPulado++;
      }
    }

    const mensagem = `
🎉 Verificação de TESTE concluída!
- Total de figures testadas: ${snapshot.size}
- Total processado: ${totalProcessado}
- Total atualizado: ${totalAtualizado}
- Total pulado: ${totalPulado} (já tinham palavras ou sem título)
    `;
    
    console.log(mensagem);
    
    return {
      success: true,
      message: mensagem,
      stats: {
        total: snapshot.size,
        processado: totalProcessado,
        atualizado: totalAtualizado,
        pulado: totalPulado,
      },
      isTest: true
    };
  } catch (error) {
    console.error("❌ [API TEST] Erro ao atualizar figures:", error);
    
    return {
      success: false,
      message: "Ocorreu um erro ao atualizar as figures no teste.",
      error: error.message || "Erro desconhecido",
      isTest: true
    };
  }
});
