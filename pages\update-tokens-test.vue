<template>
  <div>
    <!-- Header Admin -->
    <HeaderAdmin />
    
    <div class="min-h-screen bg-gray-100 py-8">
      <div class="container mx-auto px-4">
        <div class="max-w-2xl mx-auto bg-white p-6 rounded-lg shadow-lg">
          <h1 class="text-2xl font-bold mb-6">🧪 Teste - Atualizar Palavras de Busca</h1>
          
          <div class="mb-6">
            <div class="bg-blue-100 border border-blue-400 text-blue-700 px-4 py-3 rounded mb-4">
              <p class="font-medium">🧪 Modo de Teste</p>
              <p class="text-sm">Esta versão processa apenas 5 figuras para teste local.</p>
            </div>
            
            <p class="text-gray-600 mb-4">
              Esta ferramenta verifica algumas figuras e adiciona palavras-chave para melhorar a busca.
              Por exemplo, para uma figura com título "Dragon Ball Goku", as palavras extraídas serão: ["dragon ball goku", "dragon", "ball", "goku"].
            </p>
            <p class="text-gray-600">
              Apenas figuras que não têm palavras-chave ou que tiveram o título alterado serão atualizadas.
            </p>
          </div>

          <div class="space-y-4">
            <button 
              @click="updateTokens" 
              class="w-full bg-green-500 hover:bg-green-600 text-white px-6 py-3 rounded-lg font-medium disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              :disabled="loading"
            >
              <span v-if="loading">
                <span class="inline-block animate-spin mr-2">⟳</span>
                Testando... (apenas 5 figuras)
              </span>
              <span v-else>
                🧪 Testar com 5 Figuras
              </span>
            </button>

            <!-- Aviso sobre teste -->
            <div v-if="loading" class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
              <div class="flex items-center">
                <span class="inline-block animate-pulse mr-2">🧪</span>
                <div>
                  <p class="font-medium">Teste em andamento</p>
                  <p class="text-sm">Processando apenas 5 figuras para validar a funcionalidade.</p>
                </div>
              </div>
            </div>

            <div v-if="result" :class="[
              'p-4 rounded-lg space-y-2',
              result.success ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'
            ]">
              <div class="font-medium">
                {{ result.success ? '✅ Teste concluído!' : '❌ Erro no teste' }}
                <span v-if="result.isTest" class="text-sm font-normal">(Modo Teste)</span>
              </div>
              
              <div v-if="result.success" class="space-y-1 text-sm">
                <div>Total de figuras testadas: {{ result.stats.total }}</div>
                <div>Figuras processadas: {{ result.stats.processado }}</div>
                <div>Figuras atualizadas: {{ result.stats.atualizado }}</div>
                <div>Figuras puladas: {{ result.stats.pulado }}</div>
              </div>
              <div v-else>
                {{ result.message }}
              </div>
            </div>

            <!-- Botão para testar versão otimizada -->
            <div v-if="result && result.success" class="space-y-3">
              <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
                <p class="font-medium">✅ Teste bem-sucedido!</p>
                <p class="text-sm">A lógica está funcionando corretamente.</p>
              </div>

              <button
                @click="testOptimizedVersion"
                class="w-full bg-purple-500 hover:bg-purple-600 text-white px-6 py-3 rounded-lg font-medium disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                :disabled="loadingOptimized"
              >
                <span v-if="loadingOptimized">
                  <span class="inline-block animate-spin mr-2">⟳</span>
                  Testando versão otimizada...
                </span>
                <span v-else>
                  🚀 Testar Versão Otimizada (só figuras sem tokens)
                </span>
              </button>

              <div class="bg-blue-100 border border-blue-400 text-blue-700 px-4 py-3 rounded">
                <p class="font-medium">🚀 Versão Otimizada</p>
                <p class="text-sm mb-2">Esta versão busca apenas figuras que não têm tokens, sendo muito mais rápida!</p>
                <NuxtLink
                  to="/update-tokens"
                  class="inline-block bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded text-sm font-medium transition-colors"
                >
                  Ir para Versão Completa
                </NuxtLink>
              </div>
            </div>

            <!-- Resultado da versão otimizada -->
            <div v-if="optimizedResult" :class="[
              'p-4 rounded-lg space-y-2',
              optimizedResult.success ? 'bg-purple-100 text-purple-700' : 'bg-red-100 text-red-700'
            ]">
              <div class="font-medium">
                {{ optimizedResult.success ? '🚀 Versão otimizada concluída!' : '❌ Erro na versão otimizada' }}
              </div>

              <div v-if="optimizedResult.success" class="space-y-1 text-sm">
                <div>Total de figuras no banco: {{ optimizedResult.stats.total }}</div>
                <div>Figuras processadas: {{ optimizedResult.stats.processado }}</div>
                <div>Figuras atualizadas: {{ optimizedResult.stats.atualizado }}</div>
                <div>Figuras que já tinham tokens: {{ optimizedResult.stats.pulado }}</div>
              </div>
              <div v-else>
                {{ optimizedResult.message }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import HeaderAdmin from '~/components/HeaderAdmin.vue'

const loading = ref(false)
const result = ref(null)
const loadingOptimized = ref(false)
const optimizedResult = ref(null)

const updateTokens = async () => {
  loading.value = true
  result.value = null
  
  try {
    // Usar a API de teste
    const { data, error: apiError } = await useFetch('/api/admin/update-tokens-test', {
      method: 'POST',
      timeout: 30000 // 30 segundos para teste
    });

    if (apiError.value) {
      throw new Error(apiError.value.message || 'Erro ao testar tokens');
    }

    console.log("✅ Resposta da API de teste:", data.value);
    result.value = data.value;
  } catch (error) {
    console.error("❌ Erro no teste:", error);
    result.value = {
      success: false,
      message: 'Ocorreu um erro ao testar as figuras. Por favor, tente novamente.',
      isTest: true
    }
  } finally {
    loading.value = false
  }
}

const testOptimizedVersion = async () => {
  loadingOptimized.value = true
  optimizedResult.value = null

  try {
    // Usar a API otimizada
    const { data, error: apiError } = await useFetch('/api/admin/update-tokens', {
      method: 'POST',
      timeout: 60000 // 1 minuto para versão otimizada
    });

    if (apiError.value) {
      throw new Error(apiError.value.message || 'Erro ao testar versão otimizada');
    }

    console.log("🚀 Resposta da API otimizada:", data.value);
    optimizedResult.value = data.value;
  } catch (error) {
    console.error("❌ Erro na versão otimizada:", error);
    optimizedResult.value = {
      success: false,
      message: 'Ocorreu um erro ao testar a versão otimizada. Por favor, tente novamente.'
    }
  } finally {
    loadingOptimized.value = false
  }
}
</script>
