<template>
  <div class="min-h-screen bg-gray-50">
    <!-- Header -->
    <div class="bg-white shadow">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center py-6">
          <div>
            <h1 class="text-3xl font-bold text-gray-900">Gerenciar Transportadoras</h1>
            <p class="mt-1 text-sm text-gray-600">Configure as transportadoras disponíveis para envio</p>
          </div>
          <button
            @click="openCreateModal"
            class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium flex items-center"
          >
            <i class="fas fa-plus mr-2"></i>
            Nova Transportadora
          </button>
        </div>
      </div>
    </div>

    <!-- Content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- Loading State -->
      <div v-if="loading" class="flex justify-center items-center py-12">
        <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>

      <!-- Error State -->
      <div v-else-if="error" class="bg-red-50 border border-red-200 rounded-md p-4 mb-6">
        <div class="flex">
          <i class="fas fa-exclamation-circle text-red-400 mr-3 mt-0.5"></i>
          <div>
            <h3 class="text-sm font-medium text-red-800">Erro ao carregar transportadoras</h3>
            <p class="mt-1 text-sm text-red-700">{{ error }}</p>
          </div>
        </div>
      </div>

      <!-- Transportadoras List -->
      <div v-else class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
          <h2 class="text-lg font-medium text-gray-900">Transportadoras Cadastradas</h2>
        </div>

        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Nome
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Código
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  URL de Rastreamento
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Ações
                </th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr v-for="provider in providers" :key="provider.id" class="hover:bg-gray-50">
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm font-medium text-gray-900">{{ provider.name }}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-500">{{ provider.code }}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-500 max-w-xs truncate">{{ provider.trackingUrl }}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span
                    :class="provider.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'"
                    class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full"
                  >
                    {{ provider.isActive ? 'Ativa' : 'Inativa' }}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <button
                    @click="editProvider(provider)"
                    class="text-blue-600 hover:text-blue-900 mr-4"
                  >
                    <i class="fas fa-edit"></i>
                  </button>
                  <button
                    @click="toggleProviderStatus(provider)"
                    :class="provider.isActive ? 'text-red-600 hover:text-red-900' : 'text-green-600 hover:text-green-900'"
                  >
                    <i :class="provider.isActive ? 'fas fa-ban' : 'fas fa-check'"></i>
                  </button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- Empty State -->
        <div v-if="providers.length === 0" class="text-center py-12">
          <i class="fas fa-truck text-gray-400 text-4xl mb-4"></i>
          <h3 class="text-lg font-medium text-gray-900 mb-2">Nenhuma transportadora cadastrada</h3>
          <p class="text-gray-500 mb-4">Comece adicionando uma nova transportadora</p>
          <button
            @click="openCreateModal"
            class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium"
          >
            <i class="fas fa-plus mr-2"></i>
            Adicionar Transportadora
          </button>
        </div>
      </div>
    </div>

    <!-- Modal para Criar/Editar Transportadora -->
    <div v-if="showModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3">
          <h3 class="text-lg font-medium text-gray-900 mb-4">
            {{ isEditing ? 'Editar Transportadora' : 'Nova Transportadora' }}
          </h3>

          <form @submit.prevent="saveProvider">
            <div class="mb-4">
              <label class="block text-sm font-medium text-gray-700 mb-2">Nome</label>
              <input
                v-model="formData.name"
                type="text"
                required
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Ex: Correios"
              />
            </div>

            <div class="mb-4">
              <label class="block text-sm font-medium text-gray-700 mb-2">Código</label>
              <input
                v-model="formData.code"
                type="text"
                required
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Ex: CORREIOS"
              />
            </div>

            <div class="mb-4">
              <label class="block text-sm font-medium text-gray-700 mb-2">URL de Rastreamento</label>
              <input
                v-model="formData.trackingUrl"
                type="url"
                required
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Ex: https://rastreamento.correios.com.br/app/index.php?objeto="
              />
              <p class="text-xs text-gray-500 mt-1">
                A URL deve terminar com o parâmetro onde o código de rastreamento será anexado
              </p>
            </div>

            <div class="mb-6">
              <label class="flex items-center">
                <input
                  v-model="formData.isActive"
                  type="checkbox"
                  class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                />
                <span class="ml-2 text-sm text-gray-700">Transportadora ativa</span>
              </label>
            </div>

            <div class="flex justify-end space-x-3">
              <button
                type="button"
                @click="closeModal"
                class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md"
              >
                Cancelar
              </button>
              <button
                type="submit"
                :disabled="saving"
                class="px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-md disabled:opacity-50"
              >
                {{ saving ? 'Salvando...' : (isEditing ? 'Atualizar' : 'Criar') }}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { IShippingProvider } from '~/types/customer';
import { DEFAULT_SHIPPING_PROVIDERS } from '~/constants/shippingProviders';

// Middleware para proteger a rota
definePageMeta({
  middleware: ['admin']
});

// Estado
const providers = ref<IShippingProvider[]>([]);
const loading = ref(true);
const error = ref('');
const showModal = ref(false);
const isEditing = ref(false);
const saving = ref(false);
const editingId = ref('');

// Dados do formulário
const formData = ref({
  name: '',
  code: '',
  trackingUrl: '',
  isActive: true
});

// Métodos
const loadProviders = async () => {
  try {
    loading.value = true;
    error.value = '';

    const response = await $fetch('/api/admin/shipping-providers');

    if (response.success) {
      providers.value = response.data;
      console.log('✅ Transportadoras carregadas:', providers.value.length);
    } else {
      throw new Error('Resposta da API inválida');
    }
  } catch (e) {
    console.error('❌ Erro ao carregar transportadoras:', e);
    error.value = 'Erro ao carregar transportadoras. Tente novamente.';
  } finally {
    loading.value = false;
  }
};

const openCreateModal = () => {
  isEditing.value = false;
  editingId.value = '';
  formData.value = {
    name: '',
    code: '',
    trackingUrl: '',
    isActive: true
  };
  showModal.value = true;
};

const editProvider = (provider: IShippingProvider) => {
  isEditing.value = true;
  editingId.value = provider.id;
  formData.value = {
    name: provider.name,
    code: provider.code,
    trackingUrl: provider.trackingUrl,
    isActive: provider.isActive
  };
  showModal.value = true;
};

const closeModal = () => {
  showModal.value = false;
  isEditing.value = false;
  editingId.value = '';
  formData.value = {
    name: '',
    code: '',
    trackingUrl: '',
    isActive: true
  };
};

const saveProvider = async () => {
  try {
    saving.value = true;

    const payload = {
      ...formData.value,
      id: isEditing.value ? editingId.value : undefined
    };

    const response = await $fetch('/api/admin/shipping-providers', {
      method: 'POST',
      body: payload
    });

    if (response.success) {
      if (isEditing.value) {
        // Atualizar transportadora existente na lista
        const index = providers.value.findIndex(p => p.id === editingId.value);
        if (index !== -1) {
          providers.value[index] = response.data;
        }
        console.log('✅ Transportadora atualizada');
      } else {
        // Adicionar nova transportadora à lista
        providers.value.push(response.data);
        console.log('✅ Nova transportadora criada');
      }
      closeModal();
    } else {
      throw new Error(response.message || 'Erro ao salvar transportadora');
    }
  } catch (e) {
    console.error('❌ Erro ao salvar transportadora:', e);
    alert(`Erro ao salvar transportadora: ${e.message || 'Tente novamente.'}`);
  } finally {
    saving.value = false;
  }
};

const toggleProviderStatus = async (provider: IShippingProvider) => {
  try {
    const payload = {
      ...provider,
      isActive: !provider.isActive
    };

    const response = await $fetch('/api/admin/shipping-providers', {
      method: 'POST',
      body: payload
    });

    if (response.success) {
      const index = providers.value.findIndex(p => p.id === provider.id);
      if (index !== -1) {
        providers.value[index] = response.data;
        console.log(`✅ Status da transportadora ${provider.name} alterado para ${response.data.isActive ? 'ativa' : 'inativa'}`);
      }
    } else {
      throw new Error(response.message || 'Erro ao alterar status');
    }
  } catch (e) {
    console.error('❌ Erro ao alterar status da transportadora:', e);
    alert(`Erro ao alterar status da transportadora: ${e.message || 'Tente novamente.'}`);
  }
};

// Carregar transportadoras ao montar o componente
onMounted(() => {
  loadProviders();
});
</script>
