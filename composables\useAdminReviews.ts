/**
 * Composable para funcionalidades administrativas de avaliações
 * Apenas administradores podem usar essas funções
 */

export const useAdminReviews = () => {
  /**
   * Deletar uma avaliação (apenas para admins)
   */
  const deleteReview = async (reviewId: string, adminUserId: string): Promise<boolean> => {
    try {
      console.log(`🗑️ [useAdminReviews] Admin ${adminUserId} deletando avaliação ${reviewId}`);

      const response = await $fetch(`/api/admin/reviews/${reviewId}`, {
        method: 'DELETE',
        body: { adminUserId }
      });

      if (response.success) {
        console.log(`✅ [useAdminReviews] Avaliação ${reviewId} deletada com sucesso`);
        return true;
      } else {
        throw new Error(response.message || 'Erro ao deletar avaliação');
      }
    } catch (error: any) {
      console.error(`❌ [useAdminReviews] Erro ao deletar avaliação:`, error);
      throw new Error(error.data?.message || error.message || 'Erro ao deletar avaliação');
    }
  };

  /**
   * Ocultar/mostrar uma avaliação (moderação)
   */
  const toggleReviewVisibility = async (reviewId: string, adminUserId: string, isHidden: boolean): Promise<boolean> => {
    try {
      console.log(`👁️ [useAdminReviews] Admin ${adminUserId} ${isHidden ? 'ocultando' : 'mostrando'} avaliação ${reviewId}`);

      const response = await $fetch(`/api/admin/reviews/${reviewId}/visibility`, {
        method: 'PATCH',
        body: { adminUserId, isHidden }
      });

      if (response.success) {
        console.log(`✅ [useAdminReviews] Visibilidade da avaliação ${reviewId} alterada`);
        return true;
      } else {
        throw new Error(response.message || 'Erro ao alterar visibilidade da avaliação');
      }
    } catch (error: any) {
      console.error(`❌ [useAdminReviews] Erro ao alterar visibilidade:`, error);
      throw new Error(error.data?.message || error.message || 'Erro ao alterar visibilidade da avaliação');
    }
  };

  /**
   * Buscar todas as avaliações para moderação
   */
  const getAllReviewsForModeration = async (filters?: {
    limit?: number;
    offset?: number;
    isHidden?: boolean;
    minRating?: number;
    maxRating?: number;
    sortBy?: 'newest' | 'oldest' | 'rating_high' | 'rating_low';
  }) => {
    try {
      console.log(`🔍 [useAdminReviews] Buscando avaliações para moderação`);

      const params = new URLSearchParams();
      if (filters?.limit) params.append('limit', filters.limit.toString());
      if (filters?.offset) params.append('offset', filters.offset.toString());
      if (filters?.isHidden !== undefined) params.append('isHidden', filters.isHidden.toString());
      if (filters?.minRating) params.append('minRating', filters.minRating.toString());
      if (filters?.maxRating) params.append('maxRating', filters.maxRating.toString());
      if (filters?.sortBy) params.append('sortBy', filters.sortBy);

      const response = await $fetch(`/api/admin/reviews?${params.toString()}`);

      if (response.success) {
        console.log(`✅ [useAdminReviews] ${response.data.reviews.length} avaliações encontradas`);
        return response.data;
      } else {
        throw new Error(response.message || 'Erro ao buscar avaliações');
      }
    } catch (error: any) {
      console.error(`❌ [useAdminReviews] Erro ao buscar avaliações:`, error);
      throw new Error(error.data?.message || error.message || 'Erro ao buscar avaliações');
    }
  };

  /**
   * Buscar estatísticas de avaliações para dashboard admin
   */
  const getReviewsStatistics = async () => {
    try {
      console.log(`📊 [useAdminReviews] Buscando estatísticas de avaliações`);

      const response = await $fetch('/api/admin/reviews/statistics');

      if (response.success) {
        console.log(`✅ [useAdminReviews] Estatísticas obtidas com sucesso`);
        return response.data;
      } else {
        throw new Error(response.message || 'Erro ao buscar estatísticas');
      }
    } catch (error: any) {
      console.error(`❌ [useAdminReviews] Erro ao buscar estatísticas:`, error);
      throw new Error(error.data?.message || error.message || 'Erro ao buscar estatísticas');
    }
  };

  return {
    // Funcionalidades administrativas
    deleteReview,
    toggleReviewVisibility,
    getAllReviewsForModeration,
    getReviewsStatistics,
  };
};
