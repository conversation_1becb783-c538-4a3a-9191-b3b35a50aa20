<template>
  <div class="space-y-6">
    <!-- Cabe<PERSON><PERSON>ho -->
    <div class="text-center">
      <h3 class="text-lg font-semibold text-gray-900">
        {{ isEditing ? 'Editar Avaliação' : 'Avaliar Produto' }}
      </h3>
      <p class="text-sm text-gray-600 mt-1">
        Compartilhe sua experiência com este produto
      </p>
    </div>

    <!-- Informações do produto -->
    <div v-if="product" class="bg-gray-50 rounded-lg p-4">
      <div class="flex items-center space-x-3">
        <img
          :src="product.mainImage || product.gallery_imgs?.[0]"
          :alt="product.title"
          class="w-16 h-16 object-cover rounded-lg"
        />
        <div>
          <h4 class="font-medium text-gray-900">{{ product.title }}</h4>
          <p class="text-sm text-gray-600">Pedido #{{ orderId?.substring(0, 8) }}</p>
        </div>
      </div>
    </div>

    <!-- Sistema de estrelas -->
    <div class="space-y-2">
      <label class="block text-sm font-medium text-gray-700">
        Avaliação *
      </label>
      <div class="flex items-center space-x-1">
        <button
          v-for="star in 5"
          :key="star"
          type="button"
          @click="setRating(star)"
          @mouseover="hoverRating = star"
          @mouseleave="hoverRating = 0"
          class="text-2xl transition-colors focus:outline-none"
          :class="[
            (hoverRating >= star || rating >= star) 
              ? 'text-yellow-400' 
              : 'text-gray-300'
          ]"
        >
          <i class="fas fa-star"></i>
        </button>
        <span v-if="rating > 0" class="ml-2 text-sm text-gray-600">
          {{ ratingLabels[rating - 1] }}
        </span>
      </div>
      <p v-if="errors.rating" class="text-sm text-red-600">{{ errors.rating }}</p>
    </div>

    <!-- Campo de comentário -->
    <div class="space-y-2">
      <label for="comment" class="block text-sm font-medium text-gray-700">
        Comentário *
      </label>
      <textarea
        id="comment"
        v-model="comment"
        rows="4"
        placeholder="Conte-nos sobre sua experiência com este produto..."
        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        :class="{ 'border-red-500': errors.comment }"
      ></textarea>
      <div class="flex justify-between text-sm">
        <p v-if="errors.comment" class="text-red-600">{{ errors.comment }}</p>
        <p class="text-gray-500 ml-auto">{{ comment.length }}/500</p>
      </div>
    </div>

    <!-- Upload de mídia -->
    <div class="space-y-2">
      <label class="block text-sm font-medium text-gray-700">
        Fotos e Vídeos (opcional)
      </label>
      <p class="text-sm text-gray-600">
        Adicione fotos ou vídeos para enriquecer sua avaliação
      </p>
      
      <MediaUpload
        v-if="userId && tempReviewId"
        :user-id="userId"
        :review-id="tempReviewId"
        :max-files="8"
        @upload-complete="handleMediaUpload"
        @upload-error="handleMediaError"
      />
      
      <!-- Mídia já existente (para edição) -->
      <div v-if="existingMedia.length > 0" class="space-y-2">
        <h5 class="text-sm font-medium text-gray-700">Mídia atual:</h5>
        <div class="grid grid-cols-3 md:grid-cols-4 gap-2">
          <div
            v-for="(media, index) in existingMedia"
            :key="index"
            class="relative group"
          >
            <img
              v-if="media.type === 'image'"
              :src="media.url"
              alt="Imagem da avaliação"
              class="w-full aspect-square object-cover rounded-lg"
            />
            <div
              v-else
              class="w-full aspect-square bg-gray-100 rounded-lg flex items-center justify-center"
            >
              <i class="fas fa-video text-gray-400"></i>
            </div>
            
            <button
              type="button"
              @click="removeExistingMedia(index)"
              class="absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity"
            >
              <i class="fas fa-times text-xs"></i>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Mensagens de erro/sucesso -->
    <div v-if="submitError" class="p-3 bg-red-50 border border-red-200 rounded-md">
      <p class="text-sm text-red-600">{{ submitError }}</p>
    </div>

    <div v-if="submitSuccess" class="p-3 bg-green-50 border border-green-200 rounded-md">
      <p class="text-sm text-green-600">{{ submitSuccess }}</p>
    </div>

    <!-- Botões de ação -->
    <div class="flex justify-end space-x-3 pt-4 border-t">
      <button
        type="button"
        @click="$emit('cancel')"
        class="px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
      >
        Cancelar
      </button>
      
      <button
        type="button"
        @click="submitReview"
        :disabled="isSubmitting || !isFormValid"
        class="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
      >
        <i v-if="isSubmitting" class="fas fa-spinner fa-spin mr-2"></i>
        {{ isSubmitting ? 'Salvando...' : (isEditing ? 'Atualizar' : 'Publicar') }} Avaliação
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, defineEmits, defineProps } from 'vue'
import { v4 as uuidv4 } from 'uuid'
import MediaUpload from './MediaUpload.vue'
import type { IProductReview } from '~/types/customer'

interface Product {
  id: string
  title: string
  mainImage?: string
  gallery_imgs?: string[]
}

const props = defineProps<{
  userId: string
  productId: string
  orderId: string
  product?: Product
  existingReview?: IProductReview
}>()

const emit = defineEmits<{
  'submit': [IProductReview]
  'cancel': []
}>()

// Estado do formulário
const rating = ref(0)
const hoverRating = ref(0)
const comment = ref('')
const uploadedImages = ref<string[]>([])
const uploadedVideos = ref<string[]>([])
const existingMedia = ref<Array<{ url: string; type: 'image' | 'video' }>>([])

// Estado de submissão
const isSubmitting = ref(false)
const submitError = ref('')
const submitSuccess = ref('')
const errors = ref<Record<string, string>>({})

// ID temporário para upload de mídia
const tempReviewId = ref(uuidv4())

// Labels para as estrelas
const ratingLabels = ['Péssimo', 'Ruim', 'Regular', 'Bom', 'Excelente']

// Computed
const isEditing = computed(() => !!props.existingReview)
const isFormValid = computed(() => rating.value > 0 && comment.value.trim().length > 0)

// Métodos
const setRating = (value: number) => {
  rating.value = value
  errors.value.rating = ''
}

const handleMediaUpload = (media: { images: string[], videos: string[] }) => {
  uploadedImages.value.push(...media.images)
  uploadedVideos.value.push(...media.videos)
}

const handleMediaError = (error: string) => {
  submitError.value = error
  setTimeout(() => {
    submitError.value = ''
  }, 5000)
}

const removeExistingMedia = (index: number) => {
  existingMedia.value.splice(index, 1)
}

const validateForm = () => {
  errors.value = {}
  
  if (rating.value === 0) {
    errors.value.rating = 'Por favor, selecione uma avaliação'
  }
  
  if (!comment.value.trim()) {
    errors.value.comment = 'Comentário é obrigatório'
  } else if (comment.value.length > 500) {
    errors.value.comment = 'Comentário deve ter no máximo 500 caracteres'
  }
  
  return Object.keys(errors.value).length === 0
}

const submitReview = async () => {
  if (!validateForm()) return
  
  isSubmitting.value = true
  submitError.value = ''
  submitSuccess.value = ''
  
  try {
    const reviewData = {
      userId: props.userId,
      productId: props.productId,
      orderId: props.orderId,
      rating: rating.value,
      comment: comment.value.trim(),
      images: [...(existingMedia.value.filter(m => m.type === 'image').map(m => m.url)), ...uploadedImages.value],
      videos: [...(existingMedia.value.filter(m => m.type === 'video').map(m => m.url)), ...uploadedVideos.value],
    }
    
    let response
    
    if (isEditing.value && props.existingReview) {
      // Atualizar avaliação existente
      response = await $fetch(`/api/reviews/${props.existingReview.id}`, {
        method: 'PUT',
        body: reviewData,
      })
    } else {
      // Criar nova avaliação
      response = await $fetch('/api/reviews', {
        method: 'POST',
        body: reviewData,
      })
    }
    
    if (response.success) {
      submitSuccess.value = isEditing.value 
        ? 'Avaliação atualizada com sucesso!' 
        : 'Avaliação publicada com sucesso!'
      
      // Emitir evento de sucesso
      emit('submit', response.review)
      
      // Fechar formulário após um tempo
      setTimeout(() => {
        emit('cancel')
      }, 2000)
    }
    
  } catch (error: any) {
    console.error('Erro ao submeter avaliação:', error)
    submitError.value = error.data?.message || 'Erro ao salvar avaliação. Tente novamente.'
  } finally {
    isSubmitting.value = false
  }
}

// Inicialização
onMounted(() => {
  if (props.existingReview) {
    rating.value = props.existingReview.rating
    comment.value = props.existingReview.comment
    
    // Preparar mídia existente
    const images = props.existingReview.images || []
    const videos = props.existingReview.videos || []
    
    existingMedia.value = [
      ...images.map(url => ({ url, type: 'image' as const })),
      ...videos.map(url => ({ url, type: 'video' as const }))
    ]
  }
})
</script>
