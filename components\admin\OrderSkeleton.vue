<template>
  <div class="bg-white rounded-lg shadow-md p-4 mb-4 animate-pulse">
    <div class="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
      <!-- Informações do pedido -->
      <div class="flex-1">
        <!-- ID do pedido e data -->
        <div class="flex flex-col sm:flex-row sm:items-center gap-2 mb-3">
          <div class="h-6 bg-gray-200 rounded w-32"></div>
          <div class="h-5 bg-gray-200 rounded w-24"></div>
        </div>
        
        <!-- Cliente -->
        <div class="mb-3">
          <div class="h-5 bg-gray-200 rounded w-48 mb-2"></div>
          <div class="h-4 bg-gray-200 rounded w-64"></div>
        </div>
        
        <!-- Produto -->
        <div class="flex items-center gap-3 mb-3">
          <div class="w-16 h-16 bg-gray-200 rounded"></div>
          <div class="flex-1">
            <div class="h-5 bg-gray-200 rounded w-3/4 mb-2"></div>
            <div class="h-4 bg-gray-200 rounded w-1/2"></div>
          </div>
        </div>
      </div>
      
      <!-- Status e ações -->
      <div class="flex flex-col items-start md:items-end gap-2">
        <div class="h-8 bg-gray-200 rounded w-32 mb-2"></div>
        <div class="flex gap-2">
          <div class="h-10 bg-gray-200 rounded w-24"></div>
          <div class="h-10 bg-gray-200 rounded w-24"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// Componente de skeleton para pedidos na área de administração
</script>
