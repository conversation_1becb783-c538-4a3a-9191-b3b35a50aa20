<template>
    <div>
        <!-- Header Admin -->
        <HeaderAdmin />

        <div class="min-h-screen bg-gray-100 py-8">
            <div class="container mx-auto px-4">
                <div class="flex items-center justify-between mb-8">
                    <h1 class="text-3xl font-bold text-gray-900">Nova Figura</h1>
                    <NuxtLink to="/admin/figures" class="flex items-center space-x-2 text-gray-600 hover:text-gray-900">
                        <i class="fas fa-arrow-left"></i>
                        <span>Voltar</span>
                    </NuxtLink>
                </div>

                <div class="bg-white rounded-lg shadow-md p-6">
                    <h2 class="text-2xl font-semibold mb-6">Cadastrar Nova Figura</h2>

                    <form @submit.prevent="handleSubmit">
                        <!-- Título -->
                        <div class="mb-4">
                            <label class="block text-gray-700 mb-2">Título</label>
                            <input v-model="figure.title" type="text" required
                                class="w-full px-4 py-2 rounded border focus:outline-none focus:ring-2 focus:ring-blue-500" />
                        </div>

                        <!-- Preço Base -->
                        <div class="mb-4">
                            <label class="block text-gray-700 mb-2">Preço Base</label>
                            <input v-model="figure.base_price" type="number" required min="0" step="0.01"
                                class="w-full px-4 py-2 rounded border focus:outline-none focus:ring-2 focus:ring-blue-500" />
                        </div>

                        <!-- Características -->
                        <div class="mb-4">
                            <h3 class="text-lg font-medium mb-3">Características</h3>
                            <div class="grid grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-gray-700 mb-2">Altura</label>
                                    <input v-model="figure.characteristics.height" type="text" placeholder="Ex: 30cm"
                                        class="w-full px-4 py-2 rounded border focus:outline-none focus:ring-2 focus:ring-blue-500" />
                                </div>
                                <div>
                                    <label class="block text-gray-700 mb-2">Peso</label>
                                    <input v-model="figure.characteristics.weight" type="text" placeholder="Ex: 500g"
                                        class="w-full px-4 py-2 rounded border focus:outline-none focus:ring-2 focus:ring-blue-500" />
                                </div>
                            </div>
                        </div>

                        <!-- Imagens -->
                        <div class="mb-4">
                            <label class="block text-gray-700 mb-2">Imagens e videos</label>
                            <input type="file" multiple accept="image/*, video/*" @change="handleFileUpload"
                                class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100" />
                            <div class="mt-1 text-xs text-gray-500">
                                <p>Recomendações:</p>
                                <p>- Imagens: formatos JPG ou PNG</p>
                                <p>- Vídeos: formato MP4, tamanho máximo recomendado 100MB</p>
                            </div>
                            <div v-if="imagePreview.length" class="mt-4 grid grid-cols-4 gap-4">
                                <div v-for="(preview, index) in imagePreview" :key="index"
                                    class="relative aspect-square rounded-lg overflow-hidden">
                                    <template v-if="imageFiles[index].type.startsWith('image')">
                                        <img :src="preview" class="w-full h-full object-cover" />
                                    </template>
                                    <template v-else-if="imageFiles[index].type.startsWith('video')">
                                        <video class="w-full h-full object-cover" controls>
                                            <source :src="preview" :type="imageFiles[index].type">
                                        </video>
                                        <div class="absolute bottom-1 right-1 text-xs bg-black bg-opacity-50 text-white px-1 rounded">
                                            {{ (imageFiles[index].size / (1024 * 1024)).toFixed(1) }} MB
                                        </div>
                                    </template>
                                    <button type="button" @click="removeFile(index)"
                                        class="absolute top-2 right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600">
                                        <i class="fas fa-times"></i>
                                    </button>
                                    <div v-if="imageFiles[index].type.startsWith('video')"
                                        class="absolute top-1 left-1 bg-blue-500 text-white text-xs px-2 py-1 rounded-full">
                                        Vídeo
                                    </div>
                                </div>
                            </div>
                            <div v-if="isSaving" class="mt-2 flex items-center justify-center">
                                <div class="inline-block animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-blue-500 mr-2"></div>
                                <span class="text-sm text-blue-500">Processando arquivos...</span>
                            </div>
                        </div>

                        <!-- Herdar de outra figure -->
                        <InheritFromFigure @inherit="handleInherit" />

                        <!-- Tokens de Busca -->
                        <div class="mb-4">
                            <label class="block text-gray-700 mb-2">Tokens de Busca</label>
                            <div class="flex items-center space-x-2">
                                <input v-model="newToken" type="text" placeholder="Adicionar token"
                                    @keyup.enter="addToken"
                                    class="w-full px-4 py-2 rounded border focus:outline-none focus:ring-2 focus:ring-blue-500" />
                                <button type="button" @click="addToken"
                                    class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">
                                    Adicionar
                                </button>
                            </div>
                            <div class="mt-2 flex flex-wrap gap-2">
                                <div v-for="token in figure.search_tokens" :key="token"
                                    class="px-3 py-1 bg-gray-100 rounded-full flex items-center space-x-2">
                                    <span>{{ token }}</span>
                                    <button type="button" @click="removeToken(token)"
                                        class="text-gray-500 hover:text-red-500">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Categorias -->
                        <div class="mt-4">
                            <label class="block text-gray-700 mb-2">Categorias</label>
                            <div class="mb-2">
                                <input v-model="categorySearch" type="text" placeholder="Buscar categorias..."
                                    class="w-full px-4 py-2 rounded border focus:outline-none focus:ring-2 focus:ring-blue-500" />
                            </div>
                            <div class="border rounded-lg p-4 max-h-60 overflow-y-auto">
                                <div v-if="categories.length === 0" class="text-gray-500 text-center py-2">
                                    Carregando categorias...
                                </div>
                                <div v-else-if="filteredRootCategories.length === 0"
                                    class="text-gray-500 text-center py-2">
                                    Nenhuma categoria encontrada
                                </div>
                                <div v-else class="space-y-4">
                                    <div v-for="category in filteredRootCategories" :key="category.id"
                                        class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                                        <CategoryTree :category-id="category.id" :categories="filteredCategories"
                                            :hierarchy="filteredHierarchy" :selected-categories="selectedCategories"
                                            :depth="0" @select="handleCategorySelect" />
                                    </div>
                                </div>
                            </div>
                            <div class="mt-2 text-sm text-gray-500">
                                Categorias selecionadas: {{ selectedCategories.length }}
                            </div>
                        </div>

                        <!-- Checkboxes -->
                        <div class="grid grid-cols-2 gap-4 mt-6">
                            <label class="flex items-center space-x-2">
                                <input type="checkbox" v-model="figure.ready_to_ship"
                                    class="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500" />
                                <span>Pronto para Envio</span>
                            </label>
                            <label class="flex items-center space-x-2">
                                <input type="checkbox" v-model="figure.is_exclusive"
                                    class="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500" />
                                <span>Exclusivo</span>
                            </label>
                            <label class="flex items-center space-x-2">
                                <input type="checkbox" v-model="figure.colored"
                                    class="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500" />
                                <span>Colorido</span>
                            </label>
                            <label class="flex items-center space-x-2">
                                <input type="checkbox" v-model="figure.hidden"
                                    class="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500" />
                                <span>Oculto</span>
                            </label>
                            <label class="flex items-center space-x-2">
                                <input type="checkbox" v-model="figure.updated"
                                    class="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500" />
                                <span>Atualizado</span>
                            </label>
                            <label class="flex items-center space-x-2">
                                <input type="checkbox" v-model="figure.previously_produced"
                                    class="w-4 h-4 text-green-600 border-gray-300 rounded focus:ring-green-500" />
                                <span>Já Produzido Anteriormente</span>
                            </label>
                            <label class="flex items-center space-x-2">
                                <input type="checkbox" v-model="figure.no_discount"
                                    class="w-4 h-4 text-red-600 border-gray-300 rounded focus:ring-red-500" />
                                <span>Sem Desconto Base</span>
                            </label>
                        </div>

                        <!-- Botões -->
                        <div class="flex justify-end space-x-4 mt-8">
                            <button type="button" @click="handleCancel"
                                class="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50">
                                Cancelar
                            </button>
                            <button type="submit"
                                class="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-70 disabled:cursor-not-allowed flex items-center gap-2"
                                :disabled="isSaving">
                                <svg v-if="isSaving" class="animate-spin h-5 w-5" xmlns="http://www.w3.org/2000/svg"
                                    fill="none" viewBox="0 0 24 24">
                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor"
                                        stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor"
                                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                                    </path>
                                </svg>
                                {{ isSaving ? 'Salvando...' : 'Salvar' }}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from '#app'
import { doc, runTransaction } from 'firebase/firestore'
import { useStorage } from '~/composables/useStorage'
import { useCategoriesStore } from '~/stores/categoriesStore'
import InheritFromFigure from '~/components/InheritFromFigure.vue'

const router = useRouter()
const { uploadProductFiles } = useStorage()
const categoriesStore = useCategoriesStore()
const categories = computed(() => categoriesStore.getCategories)

// Obter referência ao Firestore através do plugin Nuxt
const { $db } = useNuxtApp()

const loading = ref(false)
const imageFiles = ref<File[]>([])
const selectedCategories = ref<string[]>([])
const newToken = ref('')
const imagePreview = ref<string[]>([])
const isSaving = ref(false)

// Carregar categorias se ainda não estiverem carregadas
onMounted(async () => {
    if (!categoriesStore.isInitialized) {
        console.log('[admin/new] Store de categorias não inicializada, carregando categorias...');
        await categoriesStore.fetchAllCategories();
    }
});

// Usando shallowRef para o objeto figure
const figure = ref({
    title: ' | Dragon Ball',
    base_price: 1500,
    characteristics: {
        height: '24',
        weight: '1200'
    },
    gallery_imgs: [] as string[],
    gallery_videos: [] as string[],
    ready_to_ship: false,
    is_exclusive: false,
    colored: false,
    search_tokens: [] as string[],
    categories: [] as string[],
    hidden: false,
    updated: false,
    previously_produced: false,
    no_discount: false,
    idML: ''
})

// Função para resetar o formulário
const resetForm = () => {
    // Limpa o formulário
    figure.value = {
        title: ' | Dragon Ball',
        base_price: 1500,
        characteristics: {
            height: '24',
            weight: '1200'
        },
        gallery_imgs: [],
        gallery_videos: [],
        ready_to_ship: false,
        is_exclusive: false,
        colored: false,
        search_tokens: [],
        categories: [],
        hidden: false,
        updated: false,
        previously_produced: false,
        no_discount: false,
        idML: ''
    }

    // Limpa os arrays auxiliares
    selectedCategories.value = []
    imagePreview.value = []
    imageFiles.value = [] // Limpa os arquivos de imagem
    newToken.value = ''

    // Limpa os inputs de arquivo
    const fileInputs = document.querySelectorAll('input[type="file"]')
    fileInputs.forEach(input => {
        if (input instanceof HTMLInputElement) {
            input.value = ''
        }
    })
}

// Função para encontrar todas as categorias pai de uma categoria
const findAllParentCategories = (categoryId: string): string[] => {
    const parentIds: string[] = [];
    let currentCategory = categories.value.find(c => c.id === categoryId);

    while (currentCategory?.fatherId) {
        parentIds.push(currentCategory.fatherId);
        const fatherId = currentCategory.fatherId;
        currentCategory = categories.value.find(c => c.id === fatherId);
    }

    return parentIds;
};

// Função para lidar com herança
const handleInherit = ({ categories, tokens }: { categories?: string[], tokens?: string[] }) => {
    if (categories?.length) {
        // Para cada categoria herdada, encontrar todas as categorias pai
        const allCategories = [...categories];

        // Adicionar todas as categorias pai
        categories.forEach(categoryId => {
            const parentCategories = findAllParentCategories(categoryId);
            allCategories.push(...parentCategories);
        });

        // Adicionar todas as categorias sem duplicatas
        selectedCategories.value = [...new Set([...selectedCategories.value, ...allCategories])];
    }

    if (tokens?.length) {
        figure.value.search_tokens = [...new Set([...figure.value.search_tokens || [], ...tokens])];
    }
}

const getNextSequentialNumber = async () => {
    try {
        // Obter o próximo número sequencial através da API
        const response = await $fetch('/api/products/next-sequential', {
            method: 'GET'
        })
        debugger
        return (response as { number: number }).number.toString().padStart(4, '0')
    } catch (error) {
        console.error('Erro ao gerar número sequencial:', error)
        throw error
    }
}

const handleFileUpload = (event: Event) => {
    const input = event.target as HTMLInputElement
    if (input.files) {
        const files = Array.from(input.files)
        imageFiles.value.push(...files)

        files.forEach(file => {
            const reader = new FileReader()
            reader.onload = e => {
                if (e.target?.result) {
                    imagePreview.value.push(e.target.result as string)
                }
            }
            reader.readAsDataURL(file)
        })
    }
}

const removeFile = (index: number) => {
    imageFiles.value.splice(index, 1)
    imagePreview.value.splice(index, 1)
}

const addToken = () => {
    if (newToken.value && !figure.value.search_tokens.includes(newToken.value.toLowerCase())) {
        figure.value.search_tokens.push(newToken.value.toLowerCase())
        newToken.value = ''
    }
}

const removeToken = (tokenToRemove: string) => {
    figure.value.search_tokens = figure.value.search_tokens.filter(
        token => token !== tokenToRemove
    )
}

// Função para lidar com a seleção de categorias
const handleCategorySelect = (categoryId: string, category: any, action: string, affectedIds: string[]) => {
    if (!selectedCategories.value) {
        selectedCategories.value = []
    }

    // Atualiza as categorias com base na ação
    if (action === 'select') {
        selectedCategories.value = [...new Set([...selectedCategories.value, ...affectedIds])]
    } else {
        selectedCategories.value = selectedCategories.value.filter(id => !affectedIds.includes(id))
    }
}

const handleSubmit = async () => {
    try {
        isSaving.value = true
        // Gerar número sequencial
        const sequentialNumber = await getNextSequentialNumber()
        figure.value.idML = sequentialNumber
        console.log('📝 Número sequencial gerado:', sequentialNumber)

        // Upload das imagens e vídeos
        if (imageFiles.value?.length) {
            console.log('📸 Iniciando upload de', imageFiles.value.length, 'arquivos')

            // Separar imagens e vídeos
            const images = imageFiles.value.filter(file => file.type.startsWith('image'))
            const videos = imageFiles.value.filter(file => file.type.startsWith('video'))

            // Upload de imagens
            const uploadedImageUrls = await uploadProductFiles(images, figure.value.idML, 'images')
            figure.value.gallery_imgs = uploadedImageUrls
            console.log('✅ Upload de imagens concluído. URLs:', uploadedImageUrls)

            // Upload de vídeos
            if (videos.length) {
                console.log(`🎬 Iniciando upload de ${videos.length} vídeos`)

                // Processar vídeos um por um para evitar timeouts
                const uploadedVideoUrls: string[] = []

                for (let i = 0; i < videos.length; i++) {
                    const file = videos[i]
                    console.log(`📤 Enviando vídeo ${i+1} de ${videos.length}: ${file.name} (${(file.size / (1024 * 1024)).toFixed(2)} MB)`)

                    try {
                        // Fazer upload de um vídeo por vez
                        const urls = await uploadProductFiles([file], figure.value.idML, 'videos')

                        if (urls && urls.length > 0) {
                            uploadedVideoUrls.push(urls[0])
                            console.log(`✅ Vídeo ${i+1} enviado com sucesso: ${urls[0]}`)
                        }
                    } catch (videoError) {
                        console.error(`❌ Erro ao enviar vídeo ${i+1}:`, videoError)
                    }
                }

                figure.value.gallery_videos = uploadedVideoUrls
                console.log('✅ Upload de vídeos concluído. URLs:', uploadedVideoUrls)
            }
        }

        // Preparar dados para salvar
        const productData = {
            ...figure.value,
            base_price: figure.value.base_price, // Usar base_price ao invés de price
            date_created: new Date().toISOString(),
            categories: selectedCategories.value || [],
            search_tokens: (figure.value.search_tokens || []).map(token => token.toLowerCase()),
            gallery_imgs: figure.value.gallery_imgs || [],
            gallery_videos: figure.value.gallery_videos || [],
            site_exclusive: true,
            updated: figure.value.updated,
            previously_produced: figure.value.previously_produced
        }

        // Salvar através da API
        const response = await $fetch('/api/products', {
            method: 'POST',
            body: productData
        })

        console.log('✅ Figura salva com sucesso! ID:', response.id)
        console.log('📦 Dados salvos:', response)

        resetForm()

    } catch (error) {
        console.error('❌ Erro ao salvar figura:', error)
        alert('Erro ao cadastrar figura. Tente novamente.')
    } finally {
        isSaving.value = false
    }
}

const handleCancel = () => {
    router.push('/admin/figures')
}

// Computed properties para filtrar categorias
const filteredCategories = computed(() => {
    if (!categorySearch.value) return categories.value
    const search = categorySearch.value.toLowerCase()
    return categories.value.filter(cat =>
        cat.name.toLowerCase().includes(search)
    )
})

const hierarchy = computed(() => {
    const result = new Map<string, string[]>()
    for (const category of categories.value) {
        if (category.fatherId) {
            if (!result.has(category.fatherId)) {
                result.set(category.fatherId, [])
            }
            const children = result.get(category.fatherId)
            if (children) {
                children.push(category.id)
            }
        }
    }
    return result
})

const filteredHierarchy = computed(() => {
    const result = new Map<string, string[]>()
    const filteredIds = new Set(filteredCategories.value.map(c => c.id))

    for (const [fatherId, children] of hierarchy.value) {
        const filteredChildren = children.filter(id => filteredIds.has(id))
        if (filteredChildren.length > 0) {
            result.set(fatherId, filteredChildren)
        }
    }
    return result
})

const filteredRootCategories = computed(() =>
    filteredCategories.value.filter(cat => !cat.fatherId)
)

const categorySearch = ref('')
</script>