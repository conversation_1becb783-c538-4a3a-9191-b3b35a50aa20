# 🔄 Migração do Sistema de Avaliações

## 📋 Visão Geral

Este documento descreve a reestruturação completa do sistema de avaliações para resolver problemas de performance e escalabilidade.

### 🔴 Problema Anterior

- **Armazenamento ineficiente**: Avaliações armazenadas no array `reviews` dentro de cada objeto `ICustomer`
- **Query ineficiente**: Para buscar avaliações de um produto, era necessário:
  - Buscar TODOS os usuários (`getDocs(usersRef)`)
  - Iterar por cada usuário para filtrar avaliações do produto
  - Complexidade O(n) onde n = número total de usuários
- **Escalabilidade**: Performance degradava exponencialmente com o crescimento da base de usuários

### ✅ Nova Arquitetura

- **Coleção separada**: Avaliações agora ficam na coleção `reviews` indexada por `productId`
- **Query otimizada**: `where('productId', '==', productId)` - Complexidade O(log n)
- **Desnormalização estratégica**: Dados do usuário e produto são duplicados para evitar joins
- **Índices otimizados**: Firebase pode indexar por `productId`, `userId`, `rating`, etc.

## 🏗️ Nova Estrutura de Dados

### Interface IReview (Nova)

```typescript
interface IReview {
  id: string;
  userId: string;
  productId: string; // ÍNDICE PRINCIPAL
  orderId: string;
  rating: number;
  comment: string;
  images?: string[];
  videos?: string[];
  createdAt: Date;
  updatedAt: Date;
  isVerifiedPurchase: boolean;
  
  // Dados desnormalizados para performance
  userName: string;
  userPhoto?: string;
  productTitle: string;
  productImage?: string;
  
  // Metadados
  helpfulCount?: number;
  reportCount?: number;
  isHidden?: boolean;
}
```

## 🚀 Novos Endpoints (API v2)

### Buscar Avaliações por Produto
```
GET /api/v2/reviews/product/:productId
Query params: limit, offset, sortBy, minRating, hasMedia, verified
```

### Buscar Avaliações de Usuário
```
GET /api/v2/reviews/user/:userId
```

### Criar Avaliação
```
POST /api/v2/reviews
Body: { userId, productId, orderId, rating, comment, images?, videos? }
```

### Atualizar Avaliação
```
PUT /api/v2/reviews/:reviewId
Body: { userId, rating?, comment?, images?, videos? }
```

### Deletar Avaliação
```
DELETE /api/v2/reviews/:reviewId
Body: { userId }
```

## 🔧 Composable useReviews

```typescript
const { 
  getProductReviews,
  getUserReviews,
  createReview,
  updateReview,
  deleteReview,
  hasUserReviewedProduct,
  getUserProductReview 
} = useReviews();
```

## 📦 Componentes Atualizados

### ProductReviews.vue
- Exibe avaliações de um produto com filtros e paginação
- Estatísticas de avaliação em tempo real
- Suporte a ordenação e filtros avançados

### ReviewForm.vue
- Atualizado para usar nova API v2
- Mantém compatibilidade com tipos antigos

### ReviewCard.vue
- Suporte a ambos os tipos (IProductReview e IReview)
- Exibição otimizada de dados desnormalizados

## 🔄 Processo de Migração

### 1. Verificar Status Atual
```bash
GET /api/admin/migration-status
```

### 2. Executar Migração (Dry Run)
```bash
POST /api/admin/migrate-reviews
{
  "adminKey": "MIGRATE_REVIEWS_2024",
  "dryRun": true
}
```

### 3. Executar Migração Real
```bash
POST /api/admin/migrate-reviews
{
  "adminKey": "MIGRATE_REVIEWS_2024",
  "dryRun": false
}
```

### 4. Verificar Duplicatas
```bash
POST /api/admin/cleanup-duplicate-reviews
{
  "adminKey": "CLEANUP_REVIEWS_2024",
  "dryRun": true
}
```

### 5. Limpar Duplicatas
```bash
POST /api/admin/cleanup-duplicate-reviews
{
  "adminKey": "CLEANUP_REVIEWS_2024",
  "dryRun": false
}
```

## 📊 Benefícios da Nova Arquitetura

### Performance
- **Query direta por produto**: O(log n) vs O(n)
- **Índices otimizados**: Firebase pode criar índices específicos
- **Paginação eficiente**: Suporte nativo a limit/offset
- **Filtros em tempo real**: Sem necessidade de processar todos os dados

### Escalabilidade
- **Performance constante**: Independente do número de usuários
- **Agregações eficientes**: Estatísticas calculadas diretamente na query
- **Cache otimizado**: Dados desnormalizados reduzem necessidade de joins

### Funcionalidades
- **Filtros avançados**: Por rating, mídia, compra verificada
- **Ordenação flexível**: Por data, rating, utilidade
- **Paginação**: Suporte completo com metadados
- **Estatísticas**: Distribuição de ratings, contadores automáticos

## 🔍 Monitoramento

### Métricas Importantes
- Tempo de resposta das queries de avaliação
- Número de avaliações por produto
- Taxa de conversão de avaliações
- Performance dos índices do Firebase

### Logs de Debug
- Todas as operações são logadas com prefixos específicos
- `[API v2]` para novos endpoints
- `[Migration]` para processo de migração
- `[Cleanup]` para limpeza de duplicatas

## ⚠️ Considerações Importantes

### Compatibilidade
- Componentes mantêm compatibilidade com tipos antigos durante transição
- APIs antigas continuam funcionando até migração completa
- Dados antigos são preservados durante migração

### Desnormalização
- `userName` e `productTitle` são duplicados intencionalmente
- Reduz necessidade de joins mas requer sincronização
- Considerar atualização em lote se dados principais mudarem

### Índices Firebase
- Criar índices compostos para queries complexas
- Monitorar uso de quota de índices
- Otimizar baseado em padrões de uso reais

## 🎯 Próximos Passos

1. **Executar migração** em ambiente de produção
2. **Monitorar performance** das novas queries
3. **Implementar cache** para estatísticas frequentes
4. **Adicionar funcionalidades** como moderação e relatórios
5. **Otimizar índices** baseado em métricas reais
