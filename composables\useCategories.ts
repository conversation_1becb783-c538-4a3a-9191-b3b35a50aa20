import { ref } from 'vue'
import { collection, getDocs, addDoc, deleteDoc, updateDoc, doc, setDoc, getDoc } from 'firebase/firestore'
import { useNuxtApp } from '#app'

interface Category {
  id: string
  name: string
  fatherId?: string
}

export function useCategories() {
  const categories = ref<Category[]>([])
  const { $db } = useNuxtApp()

  // Adicionar uma nova categoria
  const addCategory = async (categoryData: { name: string; fatherId?: string; id?: string }) => {
    try {
      // Gerar ID da categoria a partir do nome (kebab-case)
      const categoryId = categoryData.id || categoryData.name
        .toLowerCase()
        .trim()
        .replace(/\s+/g, '-')
        .replace(/[^a-z0-9-]/g, '');

      // Verificar se a categoria já existe
      const categoryDocRef = doc($db, 'categories', categoryId);
      const existingCategory = await getDoc(categoryDocRef);

      if (existingCategory.exists()) {
        throw new Error('Categoria já existe');
      }

      // Criar a categoria usando setDoc com ID personalizado
      await setDoc(categoryDocRef, {
        name: categoryData.name,
        fatherId: categoryData.fatherId,
        createdAt: new Date().toISOString(),
      });

      const newCategory: Category = {
        id: categoryId,
        name: categoryData.name,
        fatherId: categoryData.fatherId
      }

      categories.value.push(newCategory)
      return newCategory
    } catch (error) {
      console.error('Erro ao adicionar categoria:', error)
      throw error
    }
  }

  // Remover uma categoria
  const removeCategory = async (categoryId: string) => {
    try {
      await deleteDoc(doc($db, 'categories', categoryId))
      const index = categories.value.findIndex(c => c.id === categoryId)
      if (index !== -1) {
        categories.value.splice(index, 1)
      }
    } catch (error) {
      console.error('Erro ao remover categoria:', error)
      throw error
    }
  }

  // Atualizar uma categoria
  const updateCategory = async (categoryId: string, data: Partial<Category>) => {
    try {
      await updateDoc(doc($db, 'categories', categoryId), data)
      const category = categories.value.find(c => c.id === categoryId)
      if (category) {
        Object.assign(category, data)
      }
    } catch (error) {
      console.error('Erro ao atualizar categoria:', error)
      throw error
    }
  }

  // Carregar categorias
  const loadCategories = async () => {
    try {
      const querySnapshot = await getDocs(collection($db, 'categories'))
      categories.value = querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as Category[]
    } catch (error) {
      console.error('Erro ao carregar categorias:', error)
      throw error
    }
  }

  // Obter subcategorias de uma categoria
  const getSubcategories = (categoryId: string): Category[] => {
    return categories.value.filter(c => c.fatherId === categoryId)
  }

  // Obter categoria pai
  const getParentCategory = (categoryId: string): Category | undefined => {
    const category = categories.value.find(c => c.id === categoryId)
    if (category?.fatherId) {
      return categories.value.find(c => c.id === category.fatherId)
    }
    return undefined
  }

  // Carregar categorias ao inicializar
  loadCategories()

  return {
    categories,
    addCategory,
    removeCategory,
    updateCategory,
    loadCategories,
    getSubcategories,
    getParentCategory
  }
}
