<template>
  <div>
    <main class="container mx-auto px-4 py-8 mt-[64px]">
      <div v-if="categoryData" class="mb-8">
        <h1 class="text-4xl font-bold mb-4">Coleção {{ categoryData.name }}</h1>
        <p class="text-gray-600">Explore nossa coleção exclusiva de figuras {{ categoryData.name }}</p>
      </div>

      <div v-if="loading || sectionsLoading" class="flex justify-center items-center min-h-[400px]">
        <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-neutral-900"></div>
      </div>

      <div v-else-if="error || sectionsError" class="text-center py-12">
        <p class="text-red-600">{{ error || sectionsError }}</p>
      </div>

      <template v-else>
        <!-- Seções Dinâmicas -->
        <DynamicSection
          v-for="section in categorySections"
          :key="section.id"
          :section="section"
        />

        <!-- Lista de Figuras -->
        <FiguresList
          :figures="figures"
          :loading="loading"
          :hide-overlay="false"
          :error="error"
        />
      </template>
    </main>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRoute } from 'vue-router'
import { Figure } from '~/types/figures'
import { useProductsStore } from '~/stores/productsStore'
import { useSectionsStore } from '~/stores/sections'
import { useCategoriesStore } from '~/stores/categoriesStore'
import type { Category } from '~/types/categories'
import HeaderBar from '~/components/HeaderBar.vue'
import DynamicSection from '~/components/DynamicSection.vue'

const route = useRoute()
const categoryId = ref(route.query.category as string)
const figures = ref<Figure[]>([])
const loading = ref(true)
const error = ref('')

// Usar a store de produtos
const productsStore = useProductsStore()

// Usar a store de categorias
const categoriesStore = useCategoriesStore()

// Sections Store
const sectionsStore = useSectionsStore()
const sectionsLoading = computed(() => sectionsStore.isLoading)
const sectionsError = computed(() => sectionsStore.getError)

// Obter dados da categoria atual
const categoryData = computed<Category | null>(() => {
  if (!categoryId.value) return null
  return categoriesStore.getCategoryById(categoryId.value) || null
})

// Filtra as seções pela categoria atual
const categorySections = computed(() => {
  if (!categoryData.value) return []
  const categorySlug = categoryData.value.name.toLowerCase().replace(' ', '-')
  return sectionsStore.sections.filter(
    section => section.page === categorySlug && section.isActive
  )
})

onMounted(async () => {
  if (!categoryId.value) {
    error.value = 'Categoria não especificada'
    loading.value = false
    return
  }

  try {
    loading.value = true
    console.log(`[collection] Carregando figuras da categoria: ${categoryId.value}`)

    // Verificar se a store de categorias já foi inicializada
    if (!categoriesStore.isInitialized) {
      console.log('[collection] Store de categorias não inicializada, carregando categorias...')
      await categoriesStore.fetchAllCategories()
    }

    // Verificar se a store de produtos já foi inicializada
    if (!productsStore.isInitialized) {
      console.log('[collection] Store de produtos não inicializada, carregando produtos...')
      await productsStore.fetchAllProducts()
    }

    // Buscar os produtos da categoria usando a store
    figures.value = productsStore.getProductsByCategory(categoryId.value)
    console.log(`[collection] Encontradas ${figures.value.length} figuras na categoria ${categoryId.value}`)

    // Carregar as seções
    await sectionsStore.fetchSections()
  } catch (e) {
    error.value = 'Erro ao carregar as figuras'
    console.error('[collection] Erro:', e)
  } finally {
    loading.value = false
  }
})
</script>
