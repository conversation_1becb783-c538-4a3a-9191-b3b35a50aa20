import { defineStore } from 'pinia'
import type { Category } from '~/types/categories'

// Tempo de expiração do cache em milissegundos (1 hora)
const CACHE_EXPIRATION_TIME = 60 * 60 * 1000

interface CategoriesState {
  categories: Category[];
  loading: boolean;
  error: string | null;
  initialized: boolean;
  lastFetchTime: number | null;
}

export const useCategoriesStore = defineStore('categories', {
  state: (): CategoriesState => ({
    categories: [],
    loading: false,
    error: null,
    initialized: false,
    lastFetchTime: null
  }),

  persist: true,

  getters: {
    getCategories: (state) => state.categories,
    getCategoryById: (state) => (id: string) => state.categories.find(c => c.id === id),
    getParentCategories: (state) => state.categories.filter(c => !c.fatherId),
    getSubcategories: (state) => (parentId: string) => state.categories.filter(c => c.fatherId === parentId),
    getParentCategory: (state) => (categoryId: string) => {
      const category = state.categories.find(c => c.id === categoryId);
      if (!category || !category.fatherId) return undefined;
      return state.categories.find(c => c.id === category.fatherId);
    },
    isLoading: (state) => state.loading,
    getError: (state) => state.error,
    isInitialized: (state) => state.initialized,
    isCacheExpired: (state) => {
      if (!state.initialized || state.categories.length === 0 || state.lastFetchTime === null) {
        return true
      }
      return (Date.now() - state.lastFetchTime) >= CACHE_EXPIRATION_TIME
    }
  },

  actions: {
    // Função para invalidar o cache e forçar recarregamento
    invalidateCache() {
      console.log('🔄 [categoriesStore] Cache invalidado, forçando recarregamento');
      this.lastFetchTime = null;
      this.initialized = false;
    },

    async fetchAllCategories() {
      // Verificar se o cache está válido
      const isCacheValid = this.initialized &&
                          this.categories.length > 0 &&
                          this.lastFetchTime !== null &&
                          (Date.now() - this.lastFetchTime) < CACHE_EXPIRATION_TIME

      if (isCacheValid) {
        console.log('🔍 [API] [categoriesStore] Categorias já carregadas, usando cache')
        return this.categories
      }

      // Se o cache expirou, mas temos categorias, informar que estamos atualizando
      if (this.initialized && this.categories.length > 0) {
        console.log(`🔍 [API] [categoriesStore] Cache expirado (${Math.round((Date.now() - (this.lastFetchTime || 0)) / 60000)} minutos), atualizando categorias...`)
      }

      console.log('ud83dudd0d [API] [categoriesStore] Iniciando consulta de TODAS as categorias')
      this.loading = true
      this.error = null

      try {
        const response = await fetch('/api/categories')

        if (!response.ok) {
          throw new Error(`Erro ao buscar categorias: ${response.status}`)
        }

        const data = await response.json()
        this.categories = data || []
        this.initialized = true
        this.lastFetchTime = Date.now()

        console.log(`ud83dudd0d [API] [categoriesStore] Carregadas ${this.categories.length} categorias`)
        return this.categories
      } catch (err: any) {
        this.error = err.message
        console.error('u274c [API] [categoriesStore] Erro ao carregar categorias:', err)
        return []
      } finally {
        this.loading = false
      }
    },

    async addCategory(category: Omit<Category, 'id'>) {
      this.loading = true
      this.error = null

      try {
        const response = await fetch('/api/categories', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(category)
        })

        if (!response.ok) {
          throw new Error(`Erro ao criar categoria: ${response.status}`)
        }

        const newCategory = await response.json()

        // Invalidar cache e recarregar todas as categorias para garantir consistência
        this.invalidateCache()
        await this.fetchAllCategories()

        console.log(`ud83dudd0d [API] [categoriesStore] Categoria ${newCategory.name} criada com sucesso`)
        return newCategory
      } catch (err: any) {
        this.error = err.message
        console.error('u274c [API] [categoriesStore] Erro ao criar categoria:', err)
        throw err
      } finally {
        this.loading = false
      }
    },

    async updateCategory(id: string, data: Partial<Omit<Category, 'id'>>) {
      this.loading = true
      this.error = null

      try {
        const response = await fetch(`/api/categories/${id}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(data)
        })

        if (!response.ok) {
          throw new Error(`Erro ao atualizar categoria: ${response.status}`)
        }

        // Atualizar no estado local
        const updatedCategory = await response.json()
        const index = this.categories.findIndex(c => c.id === id)

        if (index !== -1) {
          this.categories[index] = updatedCategory
        }

        console.log(`ud83dudd0d [API] [categoriesStore] Categoria ${id} atualizada com sucesso`)
        return true
      } catch (err: any) {
        this.error = err.message
        console.error(`u274c [API] [categoriesStore] Erro ao atualizar categoria ${id}:`, err)
        throw err
      } finally {
        this.loading = false
      }
    },

    async removeCategory(id: string) {
      this.loading = true
      this.error = null

      try {
        const response = await fetch(`/api/categories/${id}`, {
          method: 'DELETE'
        })

        if (!response.ok) {
          throw new Error(`Erro ao excluir categoria: ${response.status}`)
        }

        // Remover do estado local
        this.categories = this.categories.filter(c => c.id !== id)

        console.log(`ud83dudd0d [API] [categoriesStore] Categoria ${id} excluída com sucesso`)
        return true
      } catch (err: any) {
        this.error = err.message
        console.error(`u274c [API] [categoriesStore] Erro ao excluir categoria ${id}:`, err)
        throw err
      } finally {
        this.loading = false
      }
    }
  }
})
