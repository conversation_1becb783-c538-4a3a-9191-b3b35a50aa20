<template>
  <div>
    <div class="min-h-screen bg-gray-50 pt-16">
      <!-- <PERSON><PERSON> de <PERSON>gação lateral + Conteúdo -->
      <div class="container mx-auto px-4 py-8">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
          <!-- Menu lateral -->
          <div class="md:col-span-1">
            <div
              class="bg-white rounded-lg shadow overflow-hidden sticky top-24"
            >
              <!-- Perfil resumido -->
              <div class="p-4 border-b border-gray-200">
                <div class="flex items-center space-x-3">
                  <img
                    :src="localUserPhoto"
                    :alt="user?.displayName"
                    class="w-10 h-10 rounded-full object-cover border-2 border-gray-200"
                  />
                  <div class="truncate">
                    <h3 class="font-medium text-gray-900 truncate">
                      {{ user?.displayName || "Cliente" }}
                    </h3>
                    <p class="text-sm text-gray-500 truncate">
                      {{ user?.email }}
                    </p>
                  </div>
                </div>
              </div>

              <!-- Links de navegação -->
              <nav class="p-2">
                <button
                  v-for="(section, index) in sections"
                  :key="index"
                  @click="handleSectionChange(section.id)"
                  class="w-full text-left px-4 py-3 rounded-lg transition-colors flex items-center space-x-3"
                  :class="
                    currentSection === section.id
                      ? 'bg-red-50 text-red-600'
                      : 'text-gray-700 hover:bg-gray-50'
                  "
                >
                  <i :class="`fas ${section.icon} w-5`"></i>
                  <span>{{ section.name }}</span>
                </button>

                <div class="border-t border-gray-200 my-2"></div>

                <button
                  @click="logout"
                  class="w-full text-left px-4 py-3 rounded-lg transition-colors flex items-center space-x-3 text-gray-700 hover:bg-gray-50"
                >
                  <i class="fas fa-sign-out-alt w-5"></i>
                  <span>Sair</span>
                </button>
              </nav>
            </div>
          </div>

          <!-- Conteúdo principal -->
          <div class="md:col-span-3">
            <!-- Dashboard -->
            <div
              v-if="currentSection === 'dashboard'"
              class="bg-white rounded-lg shadow p-6"
            >
              <!-- Header do usuário -->
              <div class="flex items-center space-x-4 mb-8">
                <img
                  :src="localUserPhoto"
                  :alt="user?.displayName"
                  class="w-16 h-16 rounded-full object-cover border-2 border-gray-200"
                />
                <div>
                  <h1 class="text-2xl font-bold text-gray-900">
                    {{ user?.displayName || "Cliente" }}
                  </h1>
                  <p class="text-gray-600">{{ user?.email }}</p>
                </div>
              </div>

              <!-- Cards de resumo -->
              <div
                class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8"
              >
                <div
                  class="bg-white border border-gray-200 rounded-lg p-6 shadow-sm"
                >
                  <div class="flex items-center justify-between">
                    <div>
                      <p class="text-gray-500 text-sm">Pedidos</p>
                      <p class="text-2xl font-bold">{{ ordersCount }}</p>
                    </div>
                    <div class="bg-blue-100 p-3 rounded-full">
                      <i class="fas fa-box text-blue-600"></i>
                    </div>
                  </div>
                </div>

                <div
                  class="bg-white border border-gray-200 rounded-lg p-6 shadow-sm"
                >
                  <div class="flex items-center justify-between">
                    <div>
                      <p class="text-gray-500 text-sm">Favoritos</p>
                      <p class="text-2xl font-bold">{{ favoritesCount }}</p>
                    </div>
                    <div class="bg-red-100 p-3 rounded-full">
                      <i class="fas fa-heart text-red-600"></i>
                    </div>
                  </div>
                </div>

                <div
                  class="bg-white border border-gray-200 rounded-lg p-6 shadow-sm"
                >
                  <div class="flex items-center justify-between">
                    <div>
                      <p class="text-gray-500 text-sm">Endereços</p>
                      <p class="text-2xl font-bold">{{ addressesCount }}</p>
                    </div>
                    <div class="bg-green-100 p-3 rounded-full">
                      <i class="fas fa-map-marker-alt text-green-600"></i>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Últimos pedidos -->
              <div class="mb-8">
                <div class="flex items-center justify-between mb-4">
                  <h2 class="text-xl font-bold">Últimos Pedidos</h2>
                  <button
                    v-if="ordersCount > 0"
                    @click="handleSectionChange('pedidos')"
                    class="text-red-600 hover:text-red-700 font-medium text-sm"
                  >
                    Ver todos
                  </button>
                </div>

                <div
                  v-if="ordersCount === 0"
                  class="bg-gray-50 rounded-lg p-8 text-center"
                >
                  <i class="fas fa-box text-4xl text-gray-400 mb-4"></i>
                  <p class="text-gray-600">Você ainda não tem pedidos</p>
                  <NuxtLink
                    to="/"
                    class="inline-block mt-4 px-6 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
                  >
                    Explorar Figures
                  </NuxtLink>
                </div>

                <div v-else class="space-y-4">
                  <div
                    v-for="(order, index) in recentOrders"
                    :key="index"
                    class="bg-white rounded-lg shadow-sm p-4 border"
                  >
                    <div class="flex justify-between items-start mb-4">
                      <div>
                        <p class="text-sm text-gray-500">
                          Pedido #{{ order.paymentId }}
                        </p>
                        <p class="text-sm text-gray-500">
                          {{ convertTimestamp(order.createdAt) }}
                        </p>
                      </div>
                      <span
                        :class="
                          getStatusClass(order.status || order.collectionStatus)
                        "
                        class="px-3 py-1 rounded-full text-xs font-medium"
                      >
                        {{
                          getStatusText(order.trackCode ? 'shipped' : order.status || order.collectionStatus)
                        }}
                      </span>
                    </div>

                    <div class="flex items-center space-x-4">
                      <div
                        class="w-16 h-16 bg-gray-100 rounded-md overflow-hidden"
                      >
                        <img
                          v-if="order.figure?.gallery_imgs[0]"
                          :src="order.figure.gallery_imgs[0]"
                          :alt="order.figure?.title || 'Produto'"
                          class="w-full h-full object-contain"
                        />
                        <div
                          v-else
                          class="w-full h-full flex items-center justify-center text-gray-400"
                        >
                          <i class="fas fa-box text-2xl"></i>
                        </div>
                      </div>

                      <div class="flex-1">
                        <h3 class="font-medium">
                          {{ order.figure?.title || "Produto" }}
                        </h3>

                        <!-- Preço base e final (similar à página do produto) -->
                        <div class="space-y-1 mt-1">
                          <!-- Se houver diferença entre preço base e final, mostra ambos -->

                          <p class="text-sm text-gray-500 line-through">
                            {{ order.figure.formattedWithoutDiscount }}
                          </p>
                          <p class="text-red-600 font-medium">
                            {{ order.figure.formattedPrice }}
                          </p>
                          <!-- <p class="text-xs text-green-600 font-medium">
                            Economia de {{ order.figure.totalDiscount }}%
                          </p> -->
                        </div>
                      </div>
                    </div>

                    <div
                      class="mt-4 pt-4 border-t border-gray-100"
                      v-if="order.trackCode"
                    >
                      <div class="flex justify-between items-start">
                        <div class="flex-grow">
                          <span class="text-sm text-gray-500 font-medium">{{
                            order.trackCode
                          }}</span>
                          <div v-if="order.shippingTracking?.provider" class="text-xs text-gray-400 mt-1">
                            <i class="fas fa-truck mr-1"></i>{{ order.shippingTracking.provider.name }}
                          </div>
                        </div>
                        <button
                          @click="trackOrder(order)"
                          class="text-blue-600 hover:text-blue-800 text-sm font-medium"
                        >
                          Rastrear pedido
                        </button>
                      </div>
                    </div>
                    <span v-else class="text-gray-400 text-sm italic"
                      >Pedido em produção</span
                    >
                  </div>
                </div>
              </div>

              <!-- Favoritos recentes -->
              <div>
                <div class="flex items-center justify-between mb-4">
                  <h2 class="text-xl font-bold">Favoritos Recentes</h2>
                  <button
                    v-if="favoritesCount > 0"
                    @click="handleSectionChange('favoritos')"
                    class="text-red-600 hover:text-red-700 font-medium text-sm"
                  >
                    Ver todos
                  </button>
                </div>

                <div
                  v-if="!favoritesCount"
                  class="bg-gray-50 rounded-lg p-8 text-center"
                >
                  <i class="fas fa-heart text-4xl text-gray-400 mb-4"></i>
                  <p class="text-gray-600">
                    Você ainda não tem figures favoritas
                  </p>
                  <NuxtLink
                    to="/"
                    class="inline-block mt-4 px-6 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
                  >
                    Explorar Figures
                  </NuxtLink>
                </div>

                <div
                  v-else
                  class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
                >
                  <FigureCard
                    v-for="figure in recentFavorites"
                    :key="figure.id"
                    :figure="figure"
                    hide-overlay
                  />
                </div>
              </div>
            </div>

            <!-- Pedidos -->
            <div
              v-if="currentSection === 'pedidos'"
              class="bg-white rounded-lg shadow"
            >
              <!-- Header -->
              <div class="p-6 border-b">
                <h1 class="text-2xl font-bold text-gray-900">Meus Pedidos</h1>
              </div>

              <!-- Lista de pedidos -->
              <div class="p-6">
                <!-- Estado vazio -->
                <div
                  v-if="ordersCount === 0"
                  class="bg-gray-50 rounded-lg p-8 text-center"
                >
                  <i class="fas fa-box text-4xl text-gray-400 mb-4"></i>
                  <p class="text-gray-600">Você ainda não tem pedidos</p>
                  <button
                    @click="$router.push('/')"
                    class="mt-4 px-4 py-2 bg-red-600 text-white rounded-md"
                  >
                    Explorar produtos
                  </button>
                </div>

                <div v-else class="space-y-4">
                  <OrderCard
                    v-for="order in allOrders"
                    :key="order.id"
                    :order="order"
                    @track-order="trackOrder"
                    @update:order="handleOrderUpdate"
                  />
                </div>
              </div>
            </div>

            <!-- Favoritos -->
            <div
              v-if="currentSection === 'favoritos'"
              class="bg-white rounded-lg shadow"
            >
              <!-- Header -->
              <div class="p-6 border-b">
                <h1 class="text-2xl font-bold text-gray-900">Meus Favoritos</h1>
              </div>

              <!-- Lista de favoritos -->
              <div class="p-6">
                <!-- Estado vazio -->
                <div
                  v-if="!favoritesCount"
                  class="bg-gray-50 rounded-lg p-8 text-center"
                >
                  <i class="fas fa-heart text-4xl text-gray-400 mb-4"></i>
                  <p class="text-gray-600">
                    Você ainda não tem figures favoritas
                  </p>
                  <NuxtLink
                    to="/"
                    class="inline-block mt-4 px-6 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
                  >
                    Explorar Figures
                  </NuxtLink>
                </div>

                <!-- Grid de favoritos -->
                <div
                  v-else
                  class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
                >
                  <FigureCard
                    v-for="figure in favoritesFigures"
                    :key="figure.id"
                    :figure="figure"
                  />
                </div>
              </div>
            </div>

            <!-- Perfil / Meus Dados -->
            <div
              v-if="currentSection === 'perfil'"
              class="bg-white rounded-lg shadow"
            >
              <!-- Header -->
              <div class="p-6 border-b">
                <h1 class="text-2xl font-bold text-gray-900">Meus Dados</h1>
              </div>

              <!-- Formulário de dados pessoais -->
              <div class="p-6">
                <div class="max-w-2xl mx-auto">
                  <div v-if="profileUpdateSuccess" class="mb-4 bg-green-100 border-l-4 border-green-500 text-green-700 p-4">
                    <p>Dados atualizados com sucesso!</p>
                  </div>

                  <div v-if="profileUpdateError" class="mb-4 bg-red-100 border-l-4 border-red-500 text-red-700 p-4">
                    <p>{{ profileUpdateError }}</p>
                  </div>

                  <form @submit.prevent="updateProfile" class="space-y-6">
                    <!-- Nome -->
                    <div>
                      <label for="profile-name" class="block text-sm font-medium text-gray-700">Nome completo</label>
                      <input
                        type="text"
                        id="profile-name"
                        v-model="profileData.name"
                        class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-red-500 focus:border-red-500 sm:text-sm"
                        required
                      />
                    </div>

                    <!-- Email (somente leitura) -->
                    <div>
                      <label for="profile-email" class="block text-sm font-medium text-gray-700">Email</label>
                      <input
                        type="email"
                        id="profile-email"
                        v-model="profileData.email"
                        class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 bg-gray-50 sm:text-sm"
                        readonly
                      />
                      <p class="mt-1 text-xs text-gray-500">O email não pode ser alterado</p>
                    </div>

                    <!-- Telefone -->
                    <div>
                      <label for="profile-phone" class="block text-sm font-medium text-gray-700">Telefone</label>
                      <input
                        type="tel"
                        id="profile-phone"
                        v-model="profileData.phone"
                        class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-red-500 focus:border-red-500 sm:text-sm"
                        v-mask="'(##) #####-####'"
                        placeholder="(00) 00000-0000"
                        required
                      />
                    </div>

                    <!-- CPF -->
                    <div>
                      <label for="profile-cpf" class="block text-sm font-medium text-gray-700">CPF</label>
                      <input
                        type="text"
                        id="profile-cpf"
                        v-model="profileData.cpf"
                        class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-red-500 focus:border-red-500 sm:text-sm"
                        v-mask="'###.###.###-##'"
                        placeholder="000.000.000-00"
                        required
                      />
                      <p class="mt-1 text-xs text-gray-500">Obrigatório para envio pelos Correios</p>
                    </div>

                    <!-- Botões -->
                    <div class="flex justify-end">
                      <button
                        type="submit"
                        class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                        :disabled="updatingProfile"
                      >
                        <span v-if="updatingProfile">
                          <i class="fas fa-spinner fa-spin mr-2"></i>Salvando...
                        </span>
                        <span v-else>Salvar alterações</span>
                      </button>
                    </div>
                  </form>
                </div>
              </div>
            </div>

            <!-- Endereços -->
            <div
              v-if="currentSection === 'enderecos'"
              class="bg-white rounded-lg shadow"
            >
              <!-- Header -->
              <div class="p-6 border-b flex justify-between items-center">
                <h1 class="text-2xl font-bold text-gray-900">Meus Endereços</h1>
                <button
                  class="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
                  @click="addAddress"
                >
                  Adicionar Endereço
                </button>
              </div>

              <!-- Lista de endereços -->
              <div class="p-6">
                <!-- Estado vazio -->
                <div
                  v-if="!addressesCount"
                  class="bg-gray-50 rounded-lg p-8 text-center"
                >
                  <i
                    class="fas fa-map-marker-alt text-4xl text-gray-400 mb-4"
                  ></i>
                  <p class="text-gray-600">
                    Você ainda não tem endereços cadastrados
                  </p>
                  <button
                    class="inline-block mt-4 px-6 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
                    @click="addAddress"
                  >
                    Adicionar Endereço
                  </button>
                </div>

                <!-- Lista de endereços (quando houver) -->
                <div v-else class="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div
                    v-for="(address, index) in customer?.addresses"
                    :key="index"
                    class="bg-white rounded-lg shadow-sm p-4 border relative"
                  >
                    <!-- Badge de endereço padrão -->
                    <div
                      v-if="address.isDefault"
                      class="absolute top-2 right-2 bg-green-100 text-green-800 text-xs font-medium px-2 py-1 rounded-full"
                    >
                      Endereço padrão
                    </div>

                    <!-- Informações do endereço -->
                    <div class="mb-4">
                      <h3 class="font-semibold text-lg mb-1">
                        {{ address.name || `Endereço ${index + 1}` }}
                      </h3>
                      <p class="text-gray-600 text-sm">
                        {{ address.street }}, {{ address.number }}
                        {{
                          address.complement ? `, ${address.complement}` : ""
                        }}
                      </p>
                      <p class="text-gray-600 text-sm">
                        {{ address.neighborhood }}, {{ address.city }} -
                        {{ address.state }}
                      </p>
                      <p class="text-gray-600 text-sm">
                        CEP: {{ address.zipCode }}
                      </p>
                    </div>

                    <!-- Ações -->
                    <div class="flex justify-end space-x-2">
                      <button
                        v-if="!address.isDefault"
                        @click="handleRemoveAddress(address.id)"
                        class="text-sm text-red-600 hover:text-red-800"
                      >
                        Excluir
                      </button>
                      <button
                        v-if="!address.isDefault"
                        @click="handleSetDefaultAddress(address.id)"
                        class="text-sm text-blue-600 hover:text-blue-800"
                      >
                        Definir como padrão
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Modal de adicionar endereço -->
    <div
      v-if="showAddAddress"
      class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50"
    >
      <div class="bg-white rounded-lg w-full max-w-lg">
        <div class="p-6 border-b flex justify-between items-center">
          <h2 class="text-xl font-bold">Adicionar Endereço</h2>
          <button @click="showAddAddress = false">
            <i class="fas fa-times"></i>
          </button>
        </div>

        <!-- Formulário de adição de endereço -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
          <h3 class="text-lg font-semibold mb-4">Adicionar novo endereço</h3>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1"
                >Nome do endereço</label
              >
              <input
                v-model="newAddress.name"
                type="text"
                placeholder="Ex: Casa, Trabalho"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
              />
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1"
                >CEP</label
              >
              <div class="relative">
                <input
                  v-model="newAddress.zipCode"
                  v-mask="'#####-###'"
                  type="text"
                  placeholder="00000-000"
                  @blur="searchCEP"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
                />
                <div v-if="loadingCEP" class="absolute right-3 top-2">
                  <i class="fas fa-spinner fa-spin text-gray-400"></i>
                </div>
              </div>
              <p v-if="cepError" class="mt-1 text-sm text-red-600">{{ cepError }}</p>
            </div>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
            <div class="md:col-span-2">
              <label class="block text-sm font-medium text-gray-700 mb-1"
                >Rua</label
              >
              <input
                v-model="newAddress.street"
                type="text"
                placeholder="Rua, Avenida, etc"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
              />
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1"
                >Número</label
              >
              <input
                v-model="newAddress.number"
                type="text"
                placeholder="123"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
              />
            </div>
          </div>

          <div class="mb-4">
            <label class="block text-sm font-medium text-gray-700 mb-1"
              >Complemento (opcional)</label
            >
            <input
              v-model="newAddress.complement"
              type="text"
              placeholder="Apto, Bloco, etc"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
            />
          </div>

          <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1"
                >Bairro</label
              >
              <input
                v-model="newAddress.neighborhood"
                type="text"
                placeholder="Bairro"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
              />
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1"
                >Cidade</label
              >
              <input
                v-model="newAddress.city"
                type="text"
                placeholder="Cidade"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
              />
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1"
                >Estado</label
              >
              <select
                v-model="newAddress.state"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
              >
                <option value="">Selecione</option>
                <option value="AC">Acre</option>
                <option value="AL">Alagoas</option>
                <option value="AP">Amapá</option>
                <option value="AM">Amazonas</option>
                <option value="BA">Bahia</option>
                <option value="CE">Ceará</option>
                <option value="DF">Distrito Federal</option>
                <option value="ES">Espírito Santo</option>
                <option value="GO">Goiás</option>
                <option value="MA">Maranhão</option>
                <option value="MT">Mato Grosso</option>
                <option value="MS">Mato Grosso do Sul</option>
                <option value="MG">Minas Gerais</option>
                <option value="PA">Pará</option>
                <option value="PB">Paraíba</option>
                <option value="PR">Paraná</option>
                <option value="PE">Pernambuco</option>
                <option value="PI">Piauí</option>
                <option value="RJ">Rio de Janeiro</option>
                <option value="RN">Rio Grande do Norte</option>
                <option value="RS">Rio Grande do Sul</option>
                <option value="RO">Rondônia</option>
                <option value="RR">Roraima</option>
                <option value="SC">Santa Catarina</option>
                <option value="SP">São Paulo</option>
                <option value="SE">Sergipe</option>
                <option value="TO">Tocantins</option>
              </select>
            </div>
          </div>

          <div class="flex items-center mb-4">
            <input
              v-model="newAddress.isDefault"
              type="checkbox"
              id="defaultAddress"
              class="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 rounded"
            />
            <label for="defaultAddress" class="ml-2 block text-sm text-gray-700"
              >Definir como endereço padrão</label
            >
          </div>

          <div class="flex justify-end space-x-3">
            <button
              @click="showAddAddress = false"
              class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors"
              :disabled="savingAddress"
            >
              Cancelar
            </button>
            <button
              @click="saveAddress"
              class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
              :disabled="savingAddress"
            >
              <span v-if="savingAddress">
                <i class="fas fa-spinner fa-spin mr-2"></i>Salvando...
              </span>
              <span v-else>Salvar endereço</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import { useAuthStore } from "~/stores/auth";
import { useCustomer } from "~/composables/useCustomer";
import { useProductsStore } from "~/stores/productsStore";
import type { IOrder } from "~/types/customer";
import type { Figure } from "~/types/figures";
import type { IAddress } from "~/types/customer";
import { vMask } from 'vue-the-mask';

// Stores
const authStore = useAuthStore();
const productsStore = useProductsStore();
// Usar computed para manter a reatividade
const user = computed(() => authStore.user);

// Customer composable para gerenciar dados do cliente
const customerStore = useCustomer();
const { customer, ordersWithFigures } = storeToRefs(customerStore);

// Seções do menu lateral
const sections = [
  { id: "dashboard", name: "Dashboard", icon: "fa-tachometer-alt" },
  { id: "perfil", name: "Meus Dados", icon: "fa-user" },
  { id: "pedidos", name: "Meus Pedidos", icon: "fa-box" },
  { id: "favoritos", name: "Meus Favoritos", icon: "fa-heart" },
  { id: "enderecos", name: "Meus Endereços", icon: "fa-map-marker-alt" },
];

const currentSection = ref("dashboard");
const showAddAddress = ref(false);

// Dados do perfil
const profileData = reactive({
  name: '',
  email: '',
  phone: '',
  cpf: ''
});

// Estado do formulário de perfil
const updatingProfile = ref(false);
const profileUpdateSuccess = ref(false);
const profileUpdateError = ref('');

// Configuração para favoritos
const setupFavoritedFigures = async () => {
  if (!customer.value?.wishlist?.length) return;

  // Carrega todos os produtos se ainda não estiverem carregados
  if (!productsStore.initialized) {
    await productsStore.fetchAllProducts();
  }

  console.log(
    `🔍 [area-do-cliente] Buscando ${customer.value.wishlist.length} figures favoritas`
  );
};

// Inicializa os dados
setupFavoritedFigures();

// Garantir que os produtos sejam carregados e atualizar ordersWithFigures
const initializeData = async () => {
  try {
    // Carregar produtos se ainda não foram inicializados
    if (!productsStore.isInitialized) {
      console.log("[area-do-cliente] Inicializando produtos...");
      await productsStore.fetchAllProducts();
      console.log("[area-do-cliente] Produtos inicializados com sucesso!");

      // Forçar a reavaliação dos pedidos com figuras após o carregamento dos produtos
      if (customer.value?.orders?.length) {
        console.log(
          "[area-do-cliente] Atualizando pedidos com figuras após carregamento de produtos"
        );
        // ordersWithFigures é um computed que retorna uma função
        const ordersFn = ordersWithFigures.value;
        if (ordersFn && typeof ordersFn === "function") {
          ordersFn();
        }
      }
    }
  } catch (error) {
    console.error("[area-do-cliente] Erro ao inicializar dados:", error);
  }
};

// Chamar inicialização
initializeData();

// Computed properties para contagens
const favoritesCount = computed(() => customer.value?.wishlist?.length || 0);
const ordersCount = computed(() => customer.value?.orders?.length || 0);
const addressesCount = computed(() => customer.value?.addresses?.length || 0);

// Computed para obter os favoritos
const favoritesFigures = computed(() => {
  if (!customer.value?.wishlist?.length || !productsStore.products.length)
    return [];

  return customer.value.wishlist
    .map((figureId: string) =>
      productsStore.products.find((p) => p.idML === figureId)
    )
    .filter(Boolean) as Figure[];
});

// Obter os pedidos recentes (últimos 3)
const recentOrders = computed(() => {
  if (!customer.value?.orders?.length) return [];

  // Usar o getter ordersWithFigures para obter os pedidos com as figuras
  // ordersWithFigures é um computed que retorna uma função
  const ordersFn = ordersWithFigures.value;
  const orders = ordersFn && typeof ordersFn === "function" ? ordersFn() : [];

  if (orders && orders.length > 0) {
    return [...orders]
      .sort(
        (a, b) =>
          new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      )
      .slice(0, 3);
  }

  // Fallback para os pedidos sem figuras
  return [...customer.value.orders]
    .sort(
      (a, b) =>
        new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
    )
    .slice(0, 3);
});

// Obter todos os pedidos
const allOrders = computed(() => {
  if (!customer.value?.orders?.length) return [];

  // Usar o getter ordersWithFigures para obter os pedidos com as figuras
  // ordersWithFigures é um computed que retorna uma função
  const ordersFn = ordersWithFigures.value;
  const orders = ordersFn && typeof ordersFn === "function" ? ordersFn() : [];

  if (orders && orders.length > 0) {
    return [...orders].sort(
      (a, b) =>
        new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
    );
  }

  // Fallback para os pedidos sem figuras
  return [...customer.value.orders].sort(
    (a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
  );
});

// Obter os favoritos recentes (últimos 3)
const recentFavorites = computed(() => {
  if (!favoritesFigures.value.length) return [];

  return favoritesFigures.value.slice(0, 3);
});

// Obter todos os endereços
const addressList = computed(() => {
  return customer.value?.addresses || [];
});

// Formatação de moeda
const formatCurrency = (value: number | undefined): string => {
  if (value === undefined) return "R$ 0,00";

  return new Intl.NumberFormat("pt-BR", {
    style: "currency",
    currency: "BRL",
  }).format(value);
};

// Calcular porcentagem de desconto
const calcularDesconto = (precoBase: number, precoFinal: number): number => {
  if (!precoBase || !precoFinal || precoBase <= 0) return 0;

  const desconto = ((precoBase - precoFinal) / precoBase) * 100;
  return Math.round(desconto); // Arredonda para o número inteiro mais próximo
};

function convertTimestamp(timestamp: any): string {
  if (!timestamp) return "Data não disponível";

  // Log para depuração - ver o formato exato do timestamp recebido
  console.log("Tipo de timestamp recebido:", typeof timestamp, timestamp);

  try {
    let date;

    // Verificar o tipo de timestamp
    if (timestamp instanceof Date) {
      date = timestamp;
      console.log("Timestamp é um objeto Date");
    } else if (
      typeof timestamp === "object" &&
      timestamp.seconds !== undefined
    ) {
      // Formato do Firestore timestamp
      const totalMilliseconds =
        timestamp.seconds * 1000 + (timestamp.nanoseconds || 0) / 1e6;
      date = new Date(totalMilliseconds);
      console.log("Timestamp é um objeto Firestore, convertido para:", date);
    } else if (typeof timestamp === "string") {
      // String de data
      date = new Date(timestamp);
      console.log("Timestamp é uma string, convertido para:", date);
    } else if (typeof timestamp === "number") {
      // Timestamp em milissegundos
      date = new Date(timestamp);
      console.log("Timestamp é um número, convertido para:", date);
    } else if (timestamp && typeof timestamp.toDate === "function") {
      // Objeto Firestore com método toDate()
      date = timestamp.toDate();
      console.log("Timestamp tem método toDate(), convertido para:", date);
    } else {
      // Tentar converter qualquer outro formato
      date = new Date(timestamp);
      console.log(
        "Timestamp é de outro formato, tentativa de conversão para:",
        date
      );
    }

    // Verificar se a data é válida
    if (isNaN(date.getTime())) {
      console.warn("Data inválida:", timestamp);
      return "Data inválida";
    }

    // Formatar a data e hora sem os segundos
    const options: Intl.DateTimeFormatOptions = {
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
      hour: "2-digit",
      minute: "2-digit",
    };

    // Tentar formatar sem especificar timezone primeiro
    const formattedDate =
      date.toLocaleDateString("pt-BR", options).replace(",", "") +
      " " +
      date.toLocaleTimeString("pt-BR", {
        hour: "2-digit",
        minute: "2-digit",
      });

    console.log("Data formatada:", formattedDate);
    return formattedDate;
  } catch (error) {
    console.error("Erro ao formatar data:", error, timestamp);
    return "Data inválida";
  }
}

// Obter texto de status do pedido
const getStatusText = (status: string): string => {
  const statusMap: Record<string, string> = {
    pending: "Pendente",
    approved: "Aprovado",
    rejected: "Rejeitado",
    shipped: "Enviado",
    delivered: "Entregue",
    cancelled: "Cancelado",
    "em produção": "Em Produção",
    "em fila de produção": "Em Fila de Produção",
    "enviado": "Enviado",
    "concluída": "Concluída",
  };

  return statusMap[status] || "Desconhecido";
};

// Obter texto do método de pagamento
const getPaymentType = (paymentType: string | undefined): string => {
  if (!paymentType) return "Não informado";

  const paymentTypes: Record<string, string> = {
    credit_card: "Cartão de crédito",
    debit_card: "Cartão de débito",
    pix: "PIX",
    bank_transfer: "Transferência bancária",
    boleto: "Boleto bancário",
    cash: "Dinheiro",
    mercadopago: "Mercado Pago",
    paypal: "PayPal",
  };

  return paymentTypes[paymentType] || paymentType;
};

// Obter classe de status do pedido
const getStatusClass = (status: string): string => {
  const statusMap: Record<string, string> = {
    pending: "bg-yellow-100 text-yellow-800",
    approved: "bg-green-100 text-green-800",
    rejected: "bg-red-100 text-red-800",
    shipped: "bg-blue-100 text-blue-800",
    delivered: "bg-purple-100 text-purple-800",
    cancelled: "bg-gray-100 text-gray-800",
    "em produção": "bg-blue-100 text-blue-800",
    "em fila de produção": "bg-purple-100 text-purple-800",
    "enviado": "bg-teal-100 text-teal-800",
    "concluída": "bg-emerald-100 text-emerald-800",
  };

  return statusMap[status] || "bg-gray-100 text-gray-800";
};

// Adicionar endereço
const addAddress = () => {
  showAddAddress.value = true;
};

// Definir endereço padrão
const handleSetDefaultAddress = async (addressId: string) => {
  if (!customer.value) return;

  try {
    await customerStore.setDefaultAddress(addressId);
    console.log(`Endereço definido como padrão com sucesso!`);
  } catch (error) {
    console.error("Erro ao definir endereço como padrão:", error);
  }
};

// Remover endereço
const handleRemoveAddress = async (addressId: string) => {
  if (!customer.value) return;

  try {
    await customerStore.removeAddress(addressId);
    console.log(`Endereço removido com sucesso!`);
  } catch (error) {
    console.error("Erro ao remover endereço:", error);
  }
};

// Função para rastrear o pedido usando a transportadora correta
const trackOrder = async (order: IOrder) => {
  if (!order.trackCode) {
    alert("Este pedido não possui código de rastreamento.");
    return;
  }

  const trackCode = order.trackCode.trim();

  // Se tem transportadora definida, usar sua URL de rastreamento
  if (order.shippingTracking?.provider) {
    const trackingUrl = `${order.shippingTracking.provider.trackingUrl}${trackCode}`;
    window.open(trackingUrl, "_blank");
    return;
  }

  // Fallback para Correios se não tem transportadora definida
  window.open(
    `https://rastreamento.correios.com.br/app/index.php?objetos=${trackCode}`,
    "_blank"
  );
};

// Função para lidar com a atualização de um pedido
const handleOrderUpdate = (updatedOrder: IOrder) => {
  console.log('Pedido atualizado:', updatedOrder);

  // Atualizar o pedido na lista de pedidos
  if (customer.value && customer.value.orders) {
    const orderIndex = customer.value.orders.findIndex(o => o.id === updatedOrder.id);
    if (orderIndex !== -1) {
      customer.value.orders[orderIndex] = updatedOrder;
    }
  }
};
// Mudar seção atual
const handleSectionChange = (section: string) => {
  currentSection.value = section;

  // Se estiver mudando para a seção de perfil, inicializar os dados
  if (section === 'perfil') {
    initializeProfileData();
  }
};

// Inicializar dados do perfil
const initializeProfileData = () => {
  if (customer.value) {
    profileData.name = customer.value.name || '';
    profileData.email = customer.value.email || '';
    profileData.phone = customer.value.phone || '';
    profileData.cpf = customer.value.cpf || '';
  }
};

// Atualizar perfil
const updateProfile = async () => {
  try {
    updatingProfile.value = true;
    profileUpdateSuccess.value = false;
    profileUpdateError.value = '';

    // Validar CPF
    if (profileData.cpf && profileData.cpf.replace(/\D/g, '').length !== 11) {
      profileUpdateError.value = 'CPF inválido. Por favor, verifique.';
      return;
    }

    // Validar telefone
    if (profileData.phone && profileData.phone.replace(/\D/g, '').length < 11) {
      profileUpdateError.value = 'Telefone inválido. Por favor, verifique.';
      return;
    }

    if (!customer.value) {
      profileUpdateError.value = 'Erro ao atualizar perfil: cliente não encontrado.';
      return;
    }

    // Atualizar dados do cliente
    const updatedCustomer = {
      ...customer.value,
      name: profileData.name,
      phone: profileData.phone,
      cpf: profileData.cpf,
      updatedAt: new Date()
    };

    // Salvar no Firebase
    await customerStore.save();

    // Mostrar mensagem de sucesso
    profileUpdateSuccess.value = true;

    // Esconder mensagem após 3 segundos
    setTimeout(() => {
      profileUpdateSuccess.value = false;
    }, 3000);

  } catch (error: any) {
    console.error('Erro ao atualizar perfil:', error);
    profileUpdateError.value = error.message || 'Erro ao atualizar perfil. Por favor, tente novamente.';
  } finally {
    updatingProfile.value = false;
  }
};

// Estado para controlar o carregamento durante o salvamento do endereço
const savingAddress = ref(false);

// Estado para controle de busca de CEP
const loadingCEP = ref(false);
const cepError = ref('');

// Novo endereço
const newAddress = ref<IAddress>({
  name: "",
  zipCode: "",
  street: "",
  number: "",
  complement: "",
  neighborhood: "",
  city: "",
  state: "",
  isDefault: false,
});

// Função para buscar endereço pelo CEP usando a API ViaCEP
const searchCEP = async () => {
  // Limpar mensagem de erro anterior
  cepError.value = '';

  // Verificar se o CEP tem o formato correto
  const cep = newAddress.value.zipCode.replace(/\D/g, '');
  if (cep.length !== 8) {
    return;
  }

  try {
    loadingCEP.value = true;

    // Fazer a requisição para a API ViaCEP
    const response = await fetch(`https://viacep.com.br/ws/${cep}/json/`);
    const data = await response.json();

    // Verificar se a API retornou erro
    if (data.erro) {
      cepError.value = 'CEP não encontrado';
      return;
    }

    // Preencher os campos do endereço com os dados retornados
    newAddress.value.street = data.logradouro || '';
    newAddress.value.neighborhood = data.bairro || '';
    newAddress.value.city = data.localidade || '';
    newAddress.value.state = data.uf || '';

    // Focar no campo número após preencher os dados
    setTimeout(() => {
      const numberInput = document.querySelector('input[v-model="newAddress.number"]');
      if (numberInput) {
        (numberInput as HTMLInputElement).focus();
      }
    }, 100);

  } catch (error) {
    console.error('Erro ao buscar CEP:', error);
    cepError.value = 'Erro ao buscar CEP. Tente novamente.';
  } finally {
    loadingCEP.value = false;
  }
};

// Salvar endereço
const saveAddress = async () => {
  try {
    savingAddress.value = true;

    // Validar campos obrigatórios
    const requiredFields = [
      "name",
      "zipCode",
      "street",
      "number",
      "neighborhood",
      "city",
      "state",
    ];
    const missingFields = requiredFields.filter(
      (field) => !newAddress.value[field as keyof IAddress]
    );

    if (missingFields.length > 0) {
      console.error("Campos obrigatórios faltando:", missingFields);
      alert("Por favor, preencha todos os campos obrigatórios.");
      savingAddress.value = false;
      return;
    }

    // Adicionar endereço usando o método correto do customerStore
    await customerStore.addAddress({
      ...newAddress.value,
      isDefault: customer.value?.addresses?.length === 0, // Se for o primeiro endereço, definir como padrão
    });

    // Limpar formulário e fechar
    newAddress.value = {
      name: "",
      zipCode: "",
      street: "",
      number: "",
      complement: "",
      neighborhood: "",
      city: "",
      state: "",
      isDefault: false,
    };

    showAddAddress.value = false;

    // Log para debug
    console.log("Endereço adicionado com sucesso!");
  } catch (error) {
    console.error("Erro ao salvar endereço:", error);
    alert("Ocorreu um erro ao salvar o endereço. Tente novamente.");
  } finally {
    savingAddress.value = false;
  }
};

const localUserPhoto = ref(
  "https://placehold.co/400x400/e2e8f0/1e293b?text=Avatar"
);
watch(
  user,
  (user) => {
    nextTick(() => {
      localUserPhoto.value =
        user?.photoURL ||
        "https://placehold.co/400x400/e2e8f0/1e293b?text=Avatar";
    });
  },
  {
    immediate: true,
    deep: true,
  }
);

// Observar mudanças no cliente para atualizar os dados do perfil
watch(
  customer,
  (newCustomer) => {
    if (newCustomer && currentSection.value === 'perfil') {
      initializeProfileData();
    }
  },
  {
    immediate: true,
    deep: true,
  }
);
</script>
