import type { Coupon } from "./coupons";
import { FILTER_WORDS_TO_REMOVE } from "~/config/constants";
// Interface para os dados brutos
export interface FigureData {
  base_price: number;
  lockedPrice?: boolean;
  lockedAttrs?: boolean;
  lockedImages?: boolean;
  date_created: string;
  characteristics?: {
    height?: string;
    weight?: string;
  };
  height?: string;
  alias?: string;
  weight?: string;
  gallery_imgs: string[];
  gallery_videos?: string[]; // URLs dos vídeos
  idML: string;
  title: string;
  ready_to_ship: boolean;
  categories?: string[];
  site_exclusive?: boolean;
  search_tokens?: string[];
  is_exclusive?: boolean;
  release?: boolean;
  in_stock?: boolean;
  new_content?: boolean;
  hidden?: boolean;
  colored?: boolean;
  updated?: boolean; // Campo para indicar se a figura foi atualizada
  verified?: boolean; // Campo para indicar se a figura foi verificada
  previously_produced?: boolean; // Campo para indicar se a figura já foi produzida anteriormente
  no_discount?: boolean; // Campo para indicar se a figura não deve receber desconto base
}

export class Figure {
  // Propriedades base
  base_price: number;
  date_created: string;
  gallery_imgs: string[];
  gallery_videos?: string[]; // URLs dos vídeos
  idML: string;
  title: string;
  hidden: boolean;
  colored: boolean;
  alias: string;
  characteristics: {
    height?: string;
    weight?: string;
  };
  verified?: boolean;
  lockedPrice?: boolean;
  lockedAttrs?: boolean;
  lockedImages?: boolean;
  ready_to_ship: boolean;
  categories: string[];
  search_tokens?: string[];
  description?: string;
  is_exclusive?: boolean;
  id: string;
  coupons: Coupon[];
  in_stock?: boolean;
  release?: boolean = false;
  site_exclusive?: boolean = false;
  new_content: boolean;
  updated?: boolean; // Campo para indicar se a figura foi atualizada
  previously_produced?: boolean = false; // Campo para indicar se a figura já foi produzida anteriormente
  no_discount?: boolean = false; // Campo para indicar se a figura não deve receber desconto base

  constructor(data: FigureData, id: string, coupons: Coupon[] = []) {
    this.base_price = data.base_price;
    this.date_created = data.date_created;
    this.in_stock = data.in_stock;
    this.lockedPrice = data.lockedPrice;
    this.lockedAttrs = data.lockedAttrs;
    this.lockedImages = data.lockedImages;
    this.gallery_imgs = data.gallery_imgs;
    this.gallery_videos = data.gallery_videos;
    this.colored = data.colored ?? false;
    this.idML = data.idML || "";
    this.title = data.title || "";
    this.alias = data.alias || (data.title ? generateAlias(data.title) : "");
    this.site_exclusive = data.site_exclusive ?? false;
    this.ready_to_ship = data.ready_to_ship;
    this.categories = data.categories || [];
    this.search_tokens = data.search_tokens;
    this.is_exclusive = data.is_exclusive;
    this.id = id;
    this.verified = data.verified;
    this.coupons = coupons;
    this.characteristics = data.characteristics || {};
    this.release = data.release;
    this.new_content = data.new_content !== false; // será true se não for explicitamente false
    this.hidden = data.hidden ?? false;
    this.updated = data.updated;
    this.previously_produced = data.previously_produced ?? false;
    this.no_discount = data.no_discount ?? false;
  }

  get maxDiscount(): number {
    return this.coupons.reduce((max, coupon) => {
      return Math.max(max, coupon.amount);
    }, 0);
  }

  get totalDiscount(): number {
    // Desconto base configurável via variável de ambiente (apenas se no_discount for false)
    const config = useRuntimeConfig();
    const baseDiscount = this.no_discount ? 0 : (parseFloat(config.public.BASE_DISCOUNT_PERCENTAGE) || 0);

    const couponDiscount = this.coupons.reduce((total, coupon) => {
      return total + coupon.amount;
    }, 0);

    return baseDiscount + couponDiscount;
  }

  get finalPrice(): number {
    return this.totalDiscount > 0
      ? this.base_price * (1 - this.totalDiscount / 100)
      : this.base_price;
  }

  get formattedDate(): string {
    return new Date(this.date_created).toLocaleDateString("pt-BR");
  }

  get formattedPrice(): string {
    return new Intl.NumberFormat("pt-BR", {
      style: "currency",
      currency: "BRL",
    }).format(this.finalPrice);
  }

  get formattedWithoutDiscount(): string {
    return new Intl.NumberFormat("pt-BR", {
      style: "currency",
      currency: "BRL",
    }).format(this.base_price);
  }

  toJSON() {
    return {
      ...this,
      formattedDate: this.formattedDate,
      formattedPrice: this.formattedPrice,
      formattedWithoutDiscount: this.formattedWithoutDiscount,
      totalDiscount: this.totalDiscount,
      finalPrice: this.finalPrice,
    };
  }
}

// Tipo para resposta do Firebase
export interface FirebaseFigureResponse {
  [key: string]: FigureData;
}

// Tipo para figura formatada com campos adicionais
export interface FormattedFigure
  extends Omit<
    Figure,
    "finalPrice" | "maxDiscount" | "totalDiscount" | "toJSON"
  > {
  formattedDate: string;
  formattedPrice: string;
  formattedWithoutDiscount: string;
  discountPercentage: number;
  finalPrice: number;
  maxDiscount: number;
  totalDiscount: number;
  toJSON(): any;
  updated?: boolean; // Campo para indicar se a figura foi atualizada
  previously_produced?: boolean; // Campo para indicar se a figura já foi produzida anteriormente
  no_discount?: boolean; // Campo para indicar se a figura não deve receber desconto base
}

// Lista de palavras para remover do título
const wordsToRemove = FILTER_WORDS_TO_REMOVE;

// Funções de formatação
function formatDate(date: Date): string {
  return date.toLocaleDateString("pt-BR", {
    year: "numeric",
    month: "long",
    day: "numeric",
  });
}

function formatPrice(value: number): string {
  return new Intl.NumberFormat("pt-BR", {
    style: "currency",
    currency: "BRL",
  }).format(value);
}

// Função para limpar o título e gerar o alias
function generateAlias(title: string): string {
  let alias = title.toLowerCase();

  // Remove as palavras específicas
  wordsToRemove.forEach((word) => {
    // Cria uma regex que pega a palavra com ou sem acentos
    const regex = new RegExp(`\\b${word}\\b`, "gi");
    alias = alias.replace(regex, "");
  });

  // Remove números seguidos de "cm" e padrões como "14x"
  alias = alias.replace(/\d+\s*cm/gi, "");
  alias = alias.replace(/\d+x(?=\s|$)/gi, ""); // Remove padrões como "14x" seguidos de espaço ou fim da linha

  // Remove espaços extras e trim
  alias = alias.replace(/\s+/g, " ").trim();

  // Capitaliza
  alias = alias.replace(/(?:^|\s)\S/g, (a) => a.toUpperCase());

  return alias || title; // Se o alias ficar vazio, usa o título original
}

// Função de utilidade para criar uma nova instância de Figure
export function createFigure(
  data: FigureData,
  id: string,
  {
    coupons,
    ignoreCoupons,
  }: { coupons?: Coupon[]; ignoreCoupons?: boolean } = {}
): Figure {
  let validCoupons: Coupon[] = [];
  if (!ignoreCoupons && !coupons?.length) {
    const couponsStore = useCouponsStore();
    const coupons = couponsStore.getCoupons;

    // Filtra cupons válidos para este produto
    validCoupons = coupons.filter((coupon) =>
      coupon.products.some((p) =>
        typeof p === "string" ? p === id : (p as Figure).id === id
      )
    );
  } else {
    validCoupons = coupons?.filter((coupon) =>
      coupon.products.some((p) =>
        typeof p === "string" ? p === id : (p as Figure).id === id
      )
    ) as Coupon[];
  }

  return new Figure(data, id, validCoupons);
}

// Função para formatar os dados da figura
export function formatFigureData(
  data: FigureData,
  id: string,
  options: {
    ignoreCoupons?: boolean;
    coupons?: Coupon[];
  } = {}
): FormattedFigure {
  // Verificar se os dados essenciais estão presentes
  if (!data || !id) {
    console.error("Dados inválidos para formatFigureData:", { data, id });
    // Criar um objeto com valores padrão para evitar erros
    data = {
      ...data,
      base_price: data?.base_price || 0,
      date_created: data?.date_created || new Date().toISOString(),
      gallery_imgs: data?.gallery_imgs || [],
      idML: data?.idML || "",
      title: data?.title || "Produto",
      ready_to_ship: data?.ready_to_ship || false,
    };
  }

  const figure = createFigure(data, id, options);

  return {
    ...figure,
    formattedDate: formatDate(new Date(figure.date_created || new Date())),
    formattedPrice: formatPrice(figure.finalPrice || 0),
    formattedWithoutDiscount: formatPrice(figure.base_price || 0),
    discountPercentage: figure.maxDiscount || 0,
    finalPrice: figure.finalPrice || 0,
    maxDiscount: figure.maxDiscount || 0,
    totalDiscount: figure.totalDiscount || 0,
    toJSON: figure.toJSON,
    updated: figure.updated,
    previously_produced: figure.previously_produced,
    no_discount: figure.no_discount,
  };
}
