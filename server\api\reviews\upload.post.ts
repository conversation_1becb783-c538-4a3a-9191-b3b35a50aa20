import { defineEventHandler, readMultipartFormData, H3Event } from 'h3';
import { ref as storageRef, uploadBytes, getDownloadURL, uploadBytesResumable } from 'firebase/storage';
import { storage } from '~/server/utils/firebase';

/**
 * Rota para upload de mídia (fotos e vídeos) para avaliações
 * POST /api/reviews/upload
 */
export default defineEventHandler(async (event: H3Event) => {
  try {
    // Ler dados multipart
    const formData = await readMultipartFormData(event);
    
    if (!formData || formData.length === 0) {
      throw createError({
        statusCode: 400,
        statusMessage: "Nenhum arquivo foi enviado",
      });
    }
    
    // Encontrar campos de arquivo e metadados
    const fileFields = formData.filter(field => field.filename);
    const userIdField = formData.find(field => field.name === 'userId');
    const reviewIdField = formData.find(field => field.name === 'reviewId');
    
    if (!userIdField || !userIdField.data) {
      throw createError({
        statusCode: 400,
        statusMessage: "ID do usuário é obrigatório",
      });
    }
    
    if (!reviewIdField || !reviewIdField.data) {
      throw createError({
        statusCode: 400,
        statusMessage: "ID da avaliação é obrigatório",
      });
    }
    
    if (fileFields.length === 0) {
      throw createError({
        statusCode: 400,
        statusMessage: "Nenhum arquivo válido foi enviado",
      });
    }
    
    const userId = Buffer.from(userIdField.data).toString('utf-8');
    const reviewId = Buffer.from(reviewIdField.data).toString('utf-8');
    
    console.log(`📤 [API] Iniciando upload de ${fileFields.length} arquivos para avaliação ${reviewId}`);
    
    // Validar tipos de arquivo
    const allowedImageTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
    const allowedVideoTypes = ['video/mp4', 'video/webm', 'video/mov'];
    const maxFileSize = 50 * 1024 * 1024; // 50MB
    
    const uploadPromises: Promise<{ url: string; type: 'image' | 'video' }>[] = [];
    
    // Processar cada arquivo
    for (const field of fileFields) {
      if (!field.filename || !field.data || !field.type) {
        console.warn(`⚠️ Campo inválido ignorado: ${field.name}`);
        continue;
      }
      
      // Verificar tamanho do arquivo
      if (field.data.length > maxFileSize) {
        throw createError({
          statusCode: 400,
          statusMessage: `Arquivo ${field.filename} é muito grande. Máximo permitido: 50MB`,
        });
      }
      
      // Determinar tipo de arquivo
      let fileType: 'image' | 'video';
      if (allowedImageTypes.includes(field.type)) {
        fileType = 'image';
      } else if (allowedVideoTypes.includes(field.type)) {
        fileType = 'video';
      } else {
        throw createError({
          statusCode: 400,
          statusMessage: `Tipo de arquivo não suportado: ${field.type}. Tipos permitidos: JPG, PNG, WebP, MP4, WebM, MOV`,
        });
      }
      
      // Gerar nome único para o arquivo
      const timestamp = Date.now();
      const randomId = Math.random().toString(36).substring(2, 15);
      const extension = field.filename.split('.').pop();
      const uniqueFilename = `${timestamp}_${randomId}.${extension}`;
      
      // Definir caminho no Storage
      const storagePath = `reviews/${userId}/${reviewId}/${fileType}s/${uniqueFilename}`;
      const fileRef = storageRef(storage, storagePath);
      
      console.log(`📤 Enviando ${fileType}: ${field.filename} → ${storagePath}`);
      
      // Criar promise de upload
      const uploadPromise = (async () => {
        try {
          if (fileType === 'video') {
            // Para vídeos, usar uploadBytesResumable para melhor controle
            const uploadTask = uploadBytesResumable(fileRef, field.data);
            
            await new Promise<void>((resolve, reject) => {
              uploadTask.on(
                'state_changed',
                (snapshot) => {
                  const progress = (snapshot.bytesTransferred / snapshot.totalBytes) * 100;
                  console.log(`📹 Upload do vídeo ${field.filename}: ${progress.toFixed(1)}%`);
                },
                (error) => {
                  console.error(`❌ Erro no upload do vídeo ${field.filename}:`, error);
                  reject(error);
                },
                () => {
                  console.log(`✅ Upload do vídeo ${field.filename} concluído`);
                  resolve();
                }
              );
            });
          } else {
            // Para imagens, usar upload simples
            await uploadBytes(fileRef, field.data);
            console.log(`✅ Upload da imagem ${field.filename} concluído`);
          }
          
          // Obter URL de download
          const downloadURL = await getDownloadURL(fileRef);
          return { url: downloadURL, type: fileType };
          
        } catch (error) {
          console.error(`❌ Erro no upload de ${field.filename}:`, error);
          throw error;
        }
      })();
      
      uploadPromises.push(uploadPromise);
    }
    
    // Aguardar todos os uploads
    const uploadResults = await Promise.all(uploadPromises);
    
    // Separar URLs por tipo
    const imageUrls = uploadResults.filter(result => result.type === 'image').map(result => result.url);
    const videoUrls = uploadResults.filter(result => result.type === 'video').map(result => result.url);
    
    console.log(`✅ [API] Upload concluído: ${imageUrls.length} imagens, ${videoUrls.length} vídeos`);
    
    return {
      success: true,
      message: "Upload realizado com sucesso",
      data: {
        images: imageUrls,
        videos: videoUrls,
        totalFiles: uploadResults.length,
      },
    };
    
  } catch (error: any) {
    console.error("❌ [API] Erro no upload de mídia para avaliação:", error);
    
    // Se for um erro já tratado, repassar
    if (error.statusCode) {
      throw error;
    }
    
    // Erro genérico
    throw createError({
      statusCode: 500,
      statusMessage: "Erro interno do servidor durante upload",
    });
  }
});
