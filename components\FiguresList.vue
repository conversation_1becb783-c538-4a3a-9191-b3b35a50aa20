<template>
  <div class="w-full">
    <!-- Conteúdo normal quando não está carregando -->
    <div v-if="!props.loading" class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
      <FigureCard
        v-for="figure in visibleFigures"
        :key="figure.id"
        :figure="figure"
        :hide-overlay="props.hideOverlay"
      />
    </div>

    <!-- Skeletons durante o carregamento -->
    <div v-else class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
      <FigureCardSkeleton v-for="n in 8" :key="n" />
    </div>

    <!-- Empty state -->
    <div
      v-if="!props.loading && !props.error && visibleFigures.length === 0"
      class="text-center py-8"
    >
      <p class="text-gray-600">Nenhuma figure encontrada</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, computed } from "vue";
import type { FormattedFigure } from "~/types/figures";
import FigureCard from './FigureCard.vue';
import FigureCardSkeleton from './FigureCardSkeleton.vue';
import { useRouter } from "vue-router";

interface Props {
  figures: FormattedFigure[];
  loading?: boolean;
  error?: string | null;
  hideOverlay: boolean;
}

const props = defineProps<Props>();
const router = useRouter();

// Filtra as figures ocultas
const visibleFigures = computed(() => {
  return props.figures?.filter(figure => !figure.hidden) || [];
});

watch(
  () => props.figures,
  (newFigures) => {
    if (!newFigures || newFigures.length === 0) {
      console.log("FiguresList recebeu array vazio ou undefined");
      return;
    }

    console.log("FiguresList recebeu figures:", {
      quantidade: newFigures.length,
      figures: newFigures,
      temCupons: newFigures.map((f) => ({
        id: f.id,
        title: f.title,
        cupons: f.coupons?.length || 0,
        precoOriginal: f.base_price,
        precoFinal: f.finalPrice,
        desconto: f.discount_percentage,
      })),
    });
  },
  { immediate: true, deep: true }
);

const openGallery = (figure: FormattedFigure) => {
  router.push(`/produto/${figure.id}`);
};
</script>

<style scoped>
/* Garantir que o hover funcione no mobile */
@media (hover: none) {
  .group:active .group-hover\:scale-105 {
    transform: scale(1.05);
  }
}
</style>
