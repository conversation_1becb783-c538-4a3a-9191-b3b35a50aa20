<template>
  <div class="space-y-4">
    <!-- <PERSON><PERSON> de upload -->
    <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors">
      <input
        ref="fileInput"
        type="file"
        multiple
        accept="image/*,video/*"
        @change="handleFileSelect"
        class="hidden"
      />
      
      <div class="space-y-2">
        <i class="fas fa-cloud-upload-alt text-3xl text-gray-400"></i>
        <div>
          <button
            type="button"
            @click="$refs.fileInput.click()"
            class="text-blue-600 hover:text-blue-800 font-medium"
          >
            Clique para selecionar arquivos
          </button>
          <p class="text-sm text-gray-500">ou arraste e solte aqui</p>
        </div>
        <div class="text-xs text-gray-400">
          <p>Imagens: JPG, PNG, WebP (máx. 10MB cada)</p>
          <p>Vídeos: MP4, WebM, MOV (máx. 50MB cada)</p>
        </div>
      </div>
    </div>

    <!-- Preview dos arquivos selecionados -->
    <div v-if="selectedFiles.length > 0" class="space-y-3">
      <h4 class="font-medium text-gray-700">Arquivos selecionados:</h4>
      
      <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
        <div
          v-for="(file, index) in selectedFiles"
          :key="index"
          class="relative group"
        >
          <!-- Preview da imagem -->
          <div
            v-if="file.type.startsWith('image/')"
            class="aspect-square rounded-lg overflow-hidden bg-gray-100"
          >
            <img
              :src="file.preview"
              :alt="file.name"
              class="w-full h-full object-cover"
            />
          </div>
          
          <!-- Preview do vídeo -->
          <div
            v-else-if="file.type.startsWith('video/')"
            class="aspect-square rounded-lg overflow-hidden bg-gray-100 flex items-center justify-center"
          >
            <div class="text-center">
              <i class="fas fa-video text-2xl text-gray-400 mb-1"></i>
              <p class="text-xs text-gray-500 px-1">{{ file.name }}</p>
            </div>
          </div>
          
          <!-- Botão de remover -->
          <button
            type="button"
            @click="removeFile(index)"
            class="absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity"
          >
            <i class="fas fa-times text-xs"></i>
          </button>
          
          <!-- Indicador de progresso durante upload -->
          <div
            v-if="file.uploading"
            class="absolute inset-0 bg-black bg-opacity-50 rounded-lg flex items-center justify-center"
          >
            <div class="text-white text-center">
              <i class="fas fa-spinner fa-spin text-lg mb-1"></i>
              <p class="text-xs">{{ file.progress }}%</p>
            </div>
          </div>
          
          <!-- Indicador de sucesso -->
          <div
            v-if="file.uploaded"
            class="absolute top-1 right-1 w-6 h-6 bg-green-500 text-white rounded-full flex items-center justify-center"
          >
            <i class="fas fa-check text-xs"></i>
          </div>
        </div>
      </div>
    </div>

    <!-- Botão de upload -->
    <div v-if="selectedFiles.length > 0 && !isUploading" class="flex justify-end">
      <button
        type="button"
        @click="uploadFiles"
        :disabled="selectedFiles.length === 0"
        class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
      >
        <i class="fas fa-upload mr-2"></i>
        Fazer Upload ({{ selectedFiles.length }} arquivo{{ selectedFiles.length > 1 ? 's' : '' }})
      </button>
    </div>

    <!-- Indicador de upload em progresso -->
    <div v-if="isUploading" class="text-center py-4">
      <div class="inline-flex items-center space-x-2 text-blue-600">
        <i class="fas fa-spinner fa-spin"></i>
        <span>Fazendo upload dos arquivos...</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, defineEmits, defineProps } from 'vue'

interface FileWithPreview extends File {
  preview?: string
  uploading?: boolean
  uploaded?: boolean
  progress?: number
  url?: string
}

const props = defineProps<{
  userId: string
  reviewId: string
  maxFiles?: number
}>()

const emit = defineEmits<{
  'upload-complete': [{ images: string[], videos: string[] }]
  'upload-error': [string]
}>()

const selectedFiles = ref<FileWithPreview[]>([])
const isUploading = ref(false)

const handleFileSelect = (event: Event) => {
  const input = event.target as HTMLInputElement
  if (!input.files) return

  const files = Array.from(input.files)
  const maxFiles = props.maxFiles || 10

  // Verificar limite de arquivos
  if (selectedFiles.value.length + files.length > maxFiles) {
    emit('upload-error', `Máximo de ${maxFiles} arquivos permitidos`)
    return
  }

  // Validar e processar cada arquivo
  files.forEach(file => {
    // Validar tipo
    const isImage = file.type.startsWith('image/')
    const isVideo = file.type.startsWith('video/')
    
    if (!isImage && !isVideo) {
      emit('upload-error', `Tipo de arquivo não suportado: ${file.name}`)
      return
    }

    // Validar tamanho
    const maxSize = isVideo ? 50 * 1024 * 1024 : 10 * 1024 * 1024 // 50MB para vídeo, 10MB para imagem
    if (file.size > maxSize) {
      const maxSizeMB = isVideo ? 50 : 10
      emit('upload-error', `Arquivo ${file.name} é muito grande. Máximo: ${maxSizeMB}MB`)
      return
    }

    const fileWithPreview = file as FileWithPreview

    // Criar preview para imagens
    if (isImage) {
      const reader = new FileReader()
      reader.onload = (e) => {
        fileWithPreview.preview = e.target?.result as string
      }
      reader.readAsDataURL(file)
    }

    selectedFiles.value.push(fileWithPreview)
  })

  // Limpar input
  input.value = ''
}

const removeFile = (index: number) => {
  selectedFiles.value.splice(index, 1)
}

const uploadFiles = async () => {
  if (selectedFiles.value.length === 0) return

  isUploading.value = true

  try {
    // Preparar FormData
    const formData = new FormData()
    formData.append('userId', props.userId)
    formData.append('reviewId', props.reviewId)

    selectedFiles.value.forEach((file, index) => {
      formData.append(`file_${index}`, file)
      file.uploading = true
      file.progress = 0
    })

    // Fazer upload
    const response = await $fetch('/api/reviews/upload', {
      method: 'POST',
      body: formData,
    })

    if (response.success) {
      // Marcar todos os arquivos como enviados
      selectedFiles.value.forEach(file => {
        file.uploading = false
        file.uploaded = true
        file.progress = 100
      })

      // Emitir evento de sucesso
      emit('upload-complete', {
        images: response.data.images || [],
        videos: response.data.videos || []
      })

      // Limpar arquivos após um tempo
      setTimeout(() => {
        selectedFiles.value = []
      }, 2000)
    } else {
      throw new Error('Falha no upload')
    }

  } catch (error: any) {
    console.error('Erro no upload:', error)
    
    // Resetar estado dos arquivos
    selectedFiles.value.forEach(file => {
      file.uploading = false
      file.uploaded = false
      file.progress = 0
    })

    emit('upload-error', error.data?.message || 'Erro ao fazer upload dos arquivos')
  } finally {
    isUploading.value = false
  }
}
</script>
