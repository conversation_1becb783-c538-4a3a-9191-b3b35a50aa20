import type { IShippingProvider } from '~/types/customer';

// Transportadoras padrão do sistema
export const DEFAULT_SHIPPING_PROVIDERS: Omit<IShippingProvider, 'createdAt' | 'updatedAt'>[] = [
  {
    id: 'correios',
    name: 'Co<PERSON><PERSON>',
    code: 'CORREIOS',
    trackingUrl: 'https://rastreamento.correios.com.br/app/index.php?objeto=',
    isActive: true
  },
  {
    id: 'jadlog',
    name: 'Jadlog',
    code: 'JADLOG',
    trackingUrl: 'https://www.jadlog.com.br/siteInstitucional/tracking.jad?cte=',
    isActive: true
  },
  {
    id: 'sedex',
    name: 'Sedex',
    code: 'SEDEX',
    trackingUrl: 'https://rastreamento.correios.com.br/app/index.php?objeto=',
    isActive: true
  },
  {
    id: 'pac',
    name: 'PA<PERSON>',
    code: 'PAC',
    trackingUrl: 'https://rastreamento.correios.com.br/app/index.php?objeto=',
    isActive: true
  }
];

// Transportadora padrão (Correios) para compatibilidade com dados existentes
export const DEFAULT_SHIPPING_PROVIDER: Omit<IShippingProvider, 'createdAt' | 'updatedAt'> = DEFAULT_SHIPPING_PROVIDERS[0];

// Função para gerar URL completa de rastreamento
export function generateTrackingUrl(provider: IShippingProvider, trackCode: string): string {
  return `${provider.trackingUrl}${trackCode.trim()}`;
}

// Função para encontrar transportadora por código
export function findProviderByCode(code: string): Omit<IShippingProvider, 'createdAt' | 'updatedAt'> | undefined {
  return DEFAULT_SHIPPING_PROVIDERS.find(provider => provider.code === code.toUpperCase());
}

// Função para encontrar transportadora por ID
export function findProviderById(id: string): Omit<IShippingProvider, 'createdAt' | 'updatedAt'> | undefined {
  return DEFAULT_SHIPPING_PROVIDERS.find(provider => provider.id === id);
}
