import { useGoogleAdsConversion } from '~/composables/useGoogleAdsConversion';

export default defineNuxtPlugin(() => {
    // Evitar execução no servidor
    if (process.server) return;

    const head = document.head;

    // Adiciona o script principal do Google Ads
    const scriptMain = document.createElement('script');
    scriptMain.async = true;
    scriptMain.src = 'https://www.googletagmanager.com/gtag/js?id=AW-16649667979';
    head.appendChild(scriptMain);

    // Adiciona o script de configuração
    window.dataLayer = window.dataLayer || [];
    function gtag(...args: any[]) {
        window.dataLayer.push(args);
    }
    window.gtag = gtag; // Garantir que gtag está disponível globalmente

    gtag('js', new Date());
    gtag('config', 'AW-16649667979');

    // Registrar evento de conversão de visita ao site
    // Usamos um pequeno atraso para garantir que o gtag esteja carregado
    setTimeout(() => {
        const { trackVisit } = useGoogleAdsConversion();
        trackVisit();
    }, 2000);
});
