<template>
  <div class="bg-white rounded-lg shadow-sm p-4 border">
    <div class="flex justify-between items-start mb-4">
      <div>
        <p class="text-sm text-gray-500">Pedido #{{ order.paymentId }}</p>
        <p class="text-sm text-gray-500">
          {{ convertTimestamp(order.createdAt) }}
        </p>
      </div>
      <span
        :class="getStatusClass(order.status || order.collectionStatus)"
        class="px-3 py-1 rounded-full text-xs font-medium"
      >
        {{ getStatusText(order.status || order.collectionStatus) }}
      </span>
    </div>

    <div class="flex items-center space-x-4">
      <div class="w-16 h-16 bg-gray-100 rounded-md overflow-hidden">
        <img
          v-if="order.figure?.gallery_imgs[0]"
          :src="order.figure.gallery_imgs[0]"
          :alt="order.figure?.title || 'Produto'"
          class="w-full h-full object-contain"
        />
        <div
          v-else
          class="w-full h-full flex items-center justify-center text-gray-400"
        >
          <i class="fas fa-box text-2xl"></i>
        </div>
      </div>

      <div class="flex-1">
        <h3 class="font-medium">{{ order.figure?.title || "Produto" }}</h3>

        <!-- Preço base e final (similar à página do produto) -->
        <div class="space-y-1 mt-1">
          <!-- Se houver diferença entre preço base e final, mostra ambos -->
          <p class="text-sm text-gray-500 line-through">
            {{ order.figure?.formattedWithoutDiscount }}
          </p>
          <p class="text-red-600 font-medium">
            {{ order.figure?.formattedPrice }}
          </p>
          <!-- <p class="text-xs text-green-600 font-medium">
            Economia de {{ order.figure?.totalDiscount }}%
          </p> -->
        </div>
      </div>
    </div>

    <div class="mt-3 grid grid-cols-1 md:grid-cols-2 gap-2">
      <div>
        <p class="text-sm text-gray-500">Método de pagamento</p>
        <p class="text-sm font-medium">
          {{ getPaymentType(order.paymentMethod || order.paymentType) }}
        </p>
      </div>
    </div>

    <div
      v-if="order.trackCode"
      class="mt-4 pt-4 border-t border-gray-100"
    >
      <div class="flex justify-between items-start">
        <div class="flex-grow">
          <span class="text-sm text-gray-500 font-medium">{{
            order.trackCode
          }}</span>
          <div v-if="order.shippingTracking?.provider" class="text-xs text-gray-400 mt-1">
            <i class="fas fa-truck mr-1"></i>{{ order.shippingTracking.provider.name }}
          </div>
        </div>
        <button
          @click="trackOrder(order)"
          class="text-blue-600 hover:text-blue-800 text-sm font-medium flex items-center"
        >
          <i class="fas fa-truck mr-1"></i> Rastrear pedido
        </button>
      </div>
    </div>
    <div v-else-if="editingTrackCode" class="mt-4 pt-4 border-t border-gray-100">
      <div class="flex flex-col space-y-2">
        <div class="flex items-center">
          <input
            v-model="trackCodeInput"
            type="text"
            placeholder="Digite o código de rastreamento"
            class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
          />
        </div>
        <div class="flex justify-end space-x-2">
          <button
            @click="cancelEditTrackCode"
            class="px-3 py-1 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
          >
            Cancelar
          </button>
          <button
            @click="saveTrackCode"
            class="px-3 py-1 bg-blue-600 text-white rounded-md hover:bg-blue-700"
            :disabled="saving"
          >
            <span v-if="saving" class="mr-1"><i class="fas fa-spinner fa-spin"></i></span>
            Salvar
          </button>
        </div>
      </div>
    </div>
    <div v-else class="mt-4 pt-4 border-t border-gray-100 flex justify-between">
      <span class="text-gray-400 text-sm italic">Pedido em produção</span>
      <button
        @click="startEditTrackCode"
        class="text-blue-600 hover:text-blue-800 text-sm font-medium flex items-center"
      >
        <i class="fas fa-plus mr-1"></i> Adicionar
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import type { IOrder } from "~/types/customer";
import { useCustomerStore } from "~/stores/customerStore";

const props = defineProps<{
  order: IOrder;
}>();

const emit = defineEmits(['update:order']);

const customerStore = useCustomerStore();
const editingTrackCode = ref(false);
const trackCodeInput = ref('');
const saving = ref(false);

// Função para converter timestamp do Firestore para string formatada
function convertTimestamp(timestamp: any): string {
  if (!timestamp) return "Data não disponível";

  // Log para depuração - ver o formato exato do timestamp recebido
  console.log("Tipo de timestamp recebido:", typeof timestamp, timestamp);

  try {
    let date;

    // Verificar o tipo de timestamp
    if (timestamp instanceof Date) {
      date = timestamp;
      console.log("Timestamp é um objeto Date");
    } else if (
      typeof timestamp === "object" &&
      timestamp.seconds !== undefined
    ) {
      // Formato do Firestore timestamp
      const totalMilliseconds =
        timestamp.seconds * 1000 + (timestamp.nanoseconds || 0) / 1e6;
      date = new Date(totalMilliseconds);
      console.log("Timestamp é um objeto Firestore, convertido para:", date);
    } else if (typeof timestamp === "string") {
      // String de data
      date = new Date(timestamp);
      console.log("Timestamp é uma string, convertido para:", date);
    } else if (typeof timestamp === "number") {
      // Timestamp em milissegundos
      date = new Date(timestamp);
      console.log("Timestamp é um número, convertido para:", date);
    } else if (timestamp && typeof timestamp.toDate === "function") {
      // Objeto Firestore com método toDate()
      date = timestamp.toDate();
      console.log("Timestamp tem método toDate(), convertido para:", date);
    } else {
      // Tentar converter qualquer outro formato
      date = new Date(timestamp);
      console.log(
        "Timestamp é de outro formato, tentativa de conversão para:",
        date
      );
    }

    // Verificar se a data é válida
    if (isNaN(date.getTime())) {
      console.warn("Data inválida:", timestamp);
      return "Data inválida";
    }

    // Formatar a data e hora sem os segundos
    const options: Intl.DateTimeFormatOptions = {
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
      hour: "2-digit",
      minute: "2-digit",
    };

    // Tentar formatar sem especificar timezone primeiro
    const formattedDate =
      date.toLocaleDateString("pt-BR", options).replace(",", "") +
      " " +
      date.toLocaleTimeString("pt-BR", {
        hour: "2-digit",
        minute: "2-digit",
      });

    console.log("Data formatada:", formattedDate);
    return formattedDate;
  } catch (error) {
    console.error("Erro ao formatar data:", error, timestamp);
    return "Data inválida";
  }
}

// Obter classe de status do pedido
const getStatusClass = (status: string): string => {
  const statusMap: Record<string, string> = {
    pending: "bg-yellow-100 text-yellow-800",
    approved: "bg-green-100 text-green-800",
    rejected: "bg-red-100 text-red-800",
    shipped: "bg-blue-100 text-blue-800",
    delivered: "bg-purple-100 text-purple-800",
    cancelled: "bg-gray-100 text-gray-800",
    "em produção": "bg-blue-100 text-blue-800",
    "em fila de produção": "bg-purple-100 text-purple-800",
    "enviado": "bg-teal-100 text-teal-800",
    "concluída": "bg-emerald-100 text-emerald-800",
  };

  return statusMap[status] || "bg-gray-100 text-gray-800";
};

// Obter texto de status do pedido
const getStatusText = (status: string): string => {
  const statusMap: Record<string, string> = {
    pending: "Pendente",
    approved: "Aprovado",
    rejected: "Rejeitado",
    shipped: "Enviado",
    delivered: "Entregue",
    cancelled: "Cancelado",
    "em produção": "Em Produção",
    "em fila de produção": "Em Fila de Produção",
    "enviado": "Enviado",
    "concluída": "Concluída",
  };

  return statusMap[status] || "Desconhecido";
};

// Obter texto do método de pagamento
const getPaymentType = (paymentType: string | undefined): string => {
  if (!paymentType) return "Não informado";

  const paymentTypes: Record<string, string> = {
    credit_card: "Cartão de crédito",
    debit_card: "Cartão de débito",
    pix: "PIX",
    bank_transfer: "Transferência bancária",
    boleto: "Boleto bancário",
    cash: "Dinheiro",
    mercadopago: "Mercado Pago",
    paypal: "PayPal",
  };

  return paymentTypes[paymentType] || paymentType;
};

// Rastrear pedido
const trackOrder = (order: IOrder) => {
  if (!order.trackCode) {
    alert("Este pedido não possui código de rastreamento.");
    return;
  }

  try {
    const trackCode = order.trackCode.trim();

    // Se tem transportadora definida, usar sua URL de rastreamento
    if (order.shippingTracking?.provider) {
      const trackingUrl = `${order.shippingTracking.provider.trackingUrl}${trackCode}`;
      window.open(trackingUrl, "_blank");
      return;
    }

    // Fallback para Correios se não tem transportadora definida
    window.open(
      `https://rastreamento.correios.com.br/app/index.php?objeto=${trackCode}`,
      "_blank"
    );
  } catch (e) {
    console.error("Erro ao rastrear pedido:", e);
    alert("Ocorreu um erro ao rastrear o pedido. Por favor, tente novamente.");
  }
};

// Funções para gerenciar o código de rastreamento
const startEditTrackCode = () => {
  trackCodeInput.value = '';
  editingTrackCode.value = true;
};

const cancelEditTrackCode = () => {
  editingTrackCode.value = false;
  trackCodeInput.value = '';
};

const saveTrackCode = async () => {
  if (!trackCodeInput.value.trim()) {
    alert('Por favor, digite um código de rastreamento válido.');
    return;
  }

  saving.value = true;

  try {
    // Verificar se o cliente existe
    const customerId = props.order.customerId;
    if (!customerId) {
      throw new Error('ID do cliente não encontrado no pedido');
    }

    // Buscar o cliente
    const customer = customerStore.getCustomerById(customerId);
    if (!customer) {
      throw new Error('Cliente não encontrado');
    }

    // Encontrar o pedido na lista de pedidos do cliente
    const orderIndex = customer.orders.findIndex(o => o.id === props.order.id);
    if (orderIndex === -1) {
      throw new Error('Pedido não encontrado na lista de pedidos do cliente');
    }

    // Atualizar o código de rastreamento no pedido
    const updatedOrder = { ...customer.orders[orderIndex], trackCode: trackCodeInput.value.trim() };

    // Atualizar o pedido no cliente
    const updatedOrders = [...customer.orders];
    updatedOrders[orderIndex] = updatedOrder;

    // Atualizar o cliente no Firestore
    await customerStore.updateCustomer({
      ...customer,
      orders: updatedOrders
    });

    // Atualizar o pedido localmente
    emit('update:order', { ...props.order, trackCode: trackCodeInput.value.trim() });

    // Sair do modo de edição
    editingTrackCode.value = false;

    // Atualizar o pedido local
    Object.assign(props.order, { trackCode: trackCodeInput.value.trim() });

    console.log(`✅ Código de rastreamento adicionado com sucesso: ${trackCodeInput.value.trim()}`);
  } catch (error) {
    console.error('Erro ao salvar código de rastreamento:', error);
    alert('Ocorreu um erro ao salvar o código de rastreamento. Por favor, tente novamente.');
  } finally {
    saving.value = false;
  }
};
</script>
