<template>
  <div class="bg-white rounded-lg shadow-sm p-4 border">
    <div class="flex justify-between items-start mb-4">
      <div>
        <p class="text-sm text-gray-500">Pedido #{{ order.paymentId }}</p>
        <p class="text-sm text-gray-500">
          {{ convertTimestamp(order.createdAt) }}
        </p>
      </div>
      <span
        :class="getStatusClass(order.status || order.collectionStatus)"
        class="px-3 py-1 rounded-full text-xs font-medium"
      >
        {{ getStatusText(order.status || order.collectionStatus) }}
      </span>
    </div>

    <div class="flex items-center space-x-4">
      <div class="w-16 h-16 bg-gray-100 rounded-md overflow-hidden">
        <img
          v-if="order.figure?.gallery_imgs[0]"
          :src="order.figure.gallery_imgs[0]"
          :alt="order.figure?.title || 'Produto'"
          class="w-full h-full object-contain"
        />
        <div
          v-else
          class="w-full h-full flex items-center justify-center text-gray-400"
        >
          <i class="fas fa-box text-2xl"></i>
        </div>
      </div>

      <div class="flex-1">
        <h3 class="font-medium">{{ order.figure?.title || "Produto" }}</h3>

        <!-- Preço base e final (similar à página do produto) -->
        <div class="space-y-1 mt-1">
          <!-- Se houver diferença entre preço base e final, mostra ambos -->
          <p class="text-sm text-gray-500 line-through">
            {{ order.figure?.formattedWithoutDiscount }}
          </p>
          <p class="text-red-600 font-medium">
            {{ order.figure?.formattedPrice }}
          </p>
          <!-- <p class="text-xs text-green-600 font-medium">
            Economia de {{ order.figure?.totalDiscount }}%
          </p> -->
        </div>
      </div>
    </div>

    <div class="mt-3 grid grid-cols-1 md:grid-cols-2 gap-2">
      <div>
        <p class="text-sm text-gray-500">Método de pagamento</p>
        <p class="text-sm font-medium">
          {{ getPaymentType(order.paymentMethod || order.paymentType) }}
        </p>
      </div>
    </div>

    <div
      v-if="order.trackCode"
      class="mt-4 pt-4 border-t border-gray-100"
    >
      <div class="flex justify-between items-start">
        <div class="flex-grow">
          <span class="text-sm text-gray-500 font-medium">{{
            order.trackCode
          }}</span>
          <div v-if="order.shippingTracking?.provider" class="text-xs text-gray-400 mt-1">
            <i class="fas fa-truck mr-1"></i>{{ order.shippingTracking.provider.name }}
          </div>
        </div>
        <button
          @click="trackOrder(order)"
          class="text-blue-600 hover:text-blue-800 text-sm font-medium flex items-center"
        >
          <i class="fas fa-truck mr-1"></i> Rastrear pedido
        </button>
      </div>
    </div>
    <div v-else-if="editingTrackCode" class="mt-4 pt-4 border-t border-gray-100">
      <div class="flex flex-col space-y-2">
        <div class="flex items-center">
          <input
            v-model="trackCodeInput"
            type="text"
            placeholder="Digite o código de rastreamento"
            class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
          />
        </div>
        <div class="flex justify-end space-x-2">
          <button
            @click="cancelEditTrackCode"
            class="px-3 py-1 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
          >
            Cancelar
          </button>
          <button
            @click="saveTrackCode"
            class="px-3 py-1 bg-blue-600 text-white rounded-md hover:bg-blue-700"
            :disabled="saving"
          >
            <span v-if="saving" class="mr-1"><i class="fas fa-spinner fa-spin"></i></span>
            Salvar
          </button>
        </div>
      </div>
    </div>
    <div v-else class="mt-4 pt-4 border-t border-gray-100 flex justify-between">
      <span class="text-gray-400 text-sm italic">Pedido em produção</span>
      <button
        @click="startEditTrackCode"
        class="text-blue-600 hover:text-blue-800 text-sm font-medium flex items-center"
      >
        <i class="fas fa-plus mr-1"></i> Adicionar
      </button>
    </div>

    <!-- Debug: Mostrar sempre informações sobre avaliação -->
    <div v-if="isDebugMode" class="mt-4 pt-4 border-t border-yellow-200 bg-yellow-50 p-3 rounded">
      <h5 class="text-sm font-bold text-yellow-800 mb-2">🔍 DEBUG - Status da Avaliação</h5>
      <div class="text-xs text-yellow-700 space-y-1">
        <p><strong>Status:</strong> {{ order.status || order.collectionStatus || 'Não definido' }}</p>
        <p><strong>Data criação:</strong> {{ new Date(order.createdAt).toLocaleString('pt-BR') }}</p>
        <p><strong>Horas desde criação:</strong> {{ ((new Date().getTime() - new Date(order.createdAt).getTime()) / (1000 * 60 * 60)).toFixed(2) }}h</p>
        <p><strong>Pode avaliar:</strong> {{ canShowReviewSection ? '✅ SIM' : '❌ NÃO' }}</p>
        <p><strong>Product ID:</strong> {{ order.productId || 'Não definido' }}</p>
        <p><strong>Customer ID:</strong> {{ customerStore.customer?.uid || 'Não logado' }}</p>
      </div>
    </div>

    <!-- Seção de avaliação -->
    <div v-if="canShowReviewSection" class="mt-4 pt-4 border-t border-gray-100">
      <!-- Avaliação existente -->
      <div v-if="existingReview" class="space-y-3">
        <div class="flex items-center justify-between">
          <h4 class="text-sm font-medium text-gray-700">Sua avaliação</h4>
          <button
            @click="editReview"
            class="text-blue-600 hover:text-blue-800 text-sm font-medium"
          >
            <i class="fas fa-edit mr-1"></i> Editar
          </button>
        </div>

        <div class="bg-gray-50 rounded-lg p-3">
          <div class="flex items-center space-x-2 mb-2">
            <div class="flex items-center">
              <i
                v-for="star in 5"
                :key="star"
                class="fas fa-star text-sm"
                :class="star <= existingReview.rating ? 'text-yellow-400' : 'text-gray-300'"
              ></i>
            </div>
            <span class="text-sm text-gray-600">{{ formatDate(existingReview.createdAt) }}</span>
          </div>
          <p class="text-sm text-gray-700">{{ existingReview.comment }}</p>

          <!-- Mídia da avaliação -->
          <div v-if="existingReview.images?.length || existingReview.videos?.length" class="mt-2">
            <div v-if="existingReview.images?.length" class="flex space-x-2 mb-2">
              <img
                v-for="(image, index) in existingReview.images.slice(0, 3)"
                :key="index"
                :src="image"
                alt="Imagem da avaliação"
                class="w-12 h-12 object-cover rounded"
              />
              <span v-if="existingReview.images.length > 3" class="text-xs text-gray-500 self-center">
                +{{ existingReview.images.length - 3 }} fotos
              </span>
            </div>
            <div v-if="existingReview.videos?.length" class="text-xs text-gray-500">
              <i class="fas fa-video mr-1"></i>{{ existingReview.videos.length }} vídeo{{ existingReview.videos.length > 1 ? 's' : '' }}
            </div>
          </div>
        </div>
      </div>

      <!-- Botão para criar avaliação -->
      <div v-else class="text-center">
        <button
          @click="openReviewModal"
          class="inline-flex items-center px-4 py-2 bg-yellow-500 text-white rounded-md hover:bg-yellow-600 transition-colors"
        >
          <i class="fas fa-star mr-2"></i>
          Avaliar produto
        </button>
        <p class="text-xs text-gray-500 mt-1">Compartilhe sua experiência com este produto</p>
      </div>
    </div>

    <!-- Modal de avaliação -->
    <div
      v-if="showReviewModal"
      class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
      @click.self="closeReviewModal"
    >
      <div class="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div class="p-6">
          <ReviewForm
            :user-id="customerStore.customer?.uid || ''"
            :product-id="order.productId"
            :order-id="order.id"
            :product="order.figure"
            :existing-review="existingReview"
            @submit="handleReviewSubmit"
            @cancel="closeReviewModal"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import type { IOrder, IProductReview } from "~/types/customer";
import { useCustomerStore } from "~/stores/customerStore";
import ReviewForm from './reviews/ReviewForm.vue';

const props = defineProps<{
  order: IOrder;
}>();

const emit = defineEmits(['update:order']);

const customerStore = useCustomerStore();
const editingTrackCode = ref(false);
const trackCodeInput = ref('');
const saving = ref(false);

// Estado da avaliação
const showReviewModal = ref(false);
const existingReview = ref<IProductReview | null>(null);

// Estado do debug (verificação segura para SSR)
const isDebugMode = ref(false);

// Computed para verificar se pode mostrar seção de avaliação
const canShowReviewSection = computed(() => {
  const status = props.order.status || props.order.collectionStatus;
  const orderDate = new Date(props.order.createdAt);
  const hoursSinceOrder = (new Date().getTime() - orderDate.getTime()) / (1000 * 60 * 60);

  // Debug para ajudar a identificar problemas
  console.log('🔍 DEBUG OrderCard - Pedido:', props.order.id?.substring(0, 8));
  console.log('📊 Status:', status);
  console.log('📅 Data criação:', orderDate.toLocaleString('pt-BR'));
  console.log('⏰ Horas desde criação:', hoursSinceOrder.toFixed(2));
  console.log('✅ Status válido:', status === 'approved' || status === 'concluída');
  console.log('⏳ Tempo válido:', hoursSinceOrder >= 1);

  // Verificar se está em modo debug (verificação segura para SSR)
  if (isDebugMode.value) {
    console.log('🧪 MODO DEBUG ATIVADO - Mostrando seção de avaliação');
    return true;
  }

  // Só mostrar para pedidos aprovados/concluídos
  if (status !== 'approved' && status !== 'concluída') {
    console.log('❌ Status não permite avaliação:', status);
    return false;
  }

  // Verificar se passou pelo menos 1 hora desde a criação do pedido
  // TEMPORÁRIO: Reduzindo para 1 minuto para facilitar testes
  if (hoursSinceOrder < 0.017) { // 0.017 horas = 1 minuto
    console.log('❌ Tempo insuficiente. Faltam:', ((0.017 - hoursSinceOrder) * 60).toFixed(1), 'minutos');
    return false;
  }

  console.log('✅ Pode mostrar seção de avaliação');
  return true;
});

// Função para converter timestamp do Firestore para string formatada
function convertTimestamp(timestamp: any): string {
  if (!timestamp) return "Data não disponível";

  // Log para depuração - ver o formato exato do timestamp recebido
  console.log("Tipo de timestamp recebido:", typeof timestamp, timestamp);

  try {
    let date;

    // Verificar o tipo de timestamp
    if (timestamp instanceof Date) {
      date = timestamp;
      console.log("Timestamp é um objeto Date");
    } else if (
      typeof timestamp === "object" &&
      timestamp.seconds !== undefined
    ) {
      // Formato do Firestore timestamp
      const totalMilliseconds =
        timestamp.seconds * 1000 + (timestamp.nanoseconds || 0) / 1e6;
      date = new Date(totalMilliseconds);
      console.log("Timestamp é um objeto Firestore, convertido para:", date);
    } else if (typeof timestamp === "string") {
      // String de data
      date = new Date(timestamp);
      console.log("Timestamp é uma string, convertido para:", date);
    } else if (typeof timestamp === "number") {
      // Timestamp em milissegundos
      date = new Date(timestamp);
      console.log("Timestamp é um número, convertido para:", date);
    } else if (timestamp && typeof timestamp.toDate === "function") {
      // Objeto Firestore com método toDate()
      date = timestamp.toDate();
      console.log("Timestamp tem método toDate(), convertido para:", date);
    } else {
      // Tentar converter qualquer outro formato
      date = new Date(timestamp);
      console.log(
        "Timestamp é de outro formato, tentativa de conversão para:",
        date
      );
    }

    // Verificar se a data é válida
    if (isNaN(date.getTime())) {
      console.warn("Data inválida:", timestamp);
      return "Data inválida";
    }

    // Formatar a data e hora sem os segundos
    const options: Intl.DateTimeFormatOptions = {
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
      hour: "2-digit",
      minute: "2-digit",
    };

    // Tentar formatar sem especificar timezone primeiro
    const formattedDate =
      date.toLocaleDateString("pt-BR", options).replace(",", "") +
      " " +
      date.toLocaleTimeString("pt-BR", {
        hour: "2-digit",
        minute: "2-digit",
      });

    console.log("Data formatada:", formattedDate);
    return formattedDate;
  } catch (error) {
    console.error("Erro ao formatar data:", error, timestamp);
    return "Data inválida";
  }
}

// Obter classe de status do pedido
const getStatusClass = (status: string): string => {
  const statusMap: Record<string, string> = {
    pending: "bg-yellow-100 text-yellow-800",
    approved: "bg-green-100 text-green-800",
    rejected: "bg-red-100 text-red-800",
    shipped: "bg-blue-100 text-blue-800",
    delivered: "bg-purple-100 text-purple-800",
    cancelled: "bg-gray-100 text-gray-800",
    "em produção": "bg-blue-100 text-blue-800",
    "em fila de produção": "bg-purple-100 text-purple-800",
    "enviado": "bg-teal-100 text-teal-800",
    "concluída": "bg-emerald-100 text-emerald-800",
  };

  return statusMap[status] || "bg-gray-100 text-gray-800";
};

// Obter texto de status do pedido
const getStatusText = (status: string): string => {
  const statusMap: Record<string, string> = {
    pending: "Pendente",
    approved: "Aprovado",
    rejected: "Rejeitado",
    shipped: "Enviado",
    delivered: "Entregue",
    cancelled: "Cancelado",
    "em produção": "Em Produção",
    "em fila de produção": "Em Fila de Produção",
    "enviado": "Enviado",
    "concluída": "Concluída",
  };

  return statusMap[status] || "Desconhecido";
};

// Obter texto do método de pagamento
const getPaymentType = (paymentType: string | undefined): string => {
  if (!paymentType) return "Não informado";

  const paymentTypes: Record<string, string> = {
    credit_card: "Cartão de crédito",
    debit_card: "Cartão de débito",
    pix: "PIX",
    bank_transfer: "Transferência bancária",
    boleto: "Boleto bancário",
    cash: "Dinheiro",
    mercadopago: "Mercado Pago",
    paypal: "PayPal",
  };

  return paymentTypes[paymentType] || paymentType;
};

// Rastrear pedido
const trackOrder = (order: IOrder) => {
  if (!order.trackCode) {
    alert("Este pedido não possui código de rastreamento.");
    return;
  }

  try {
    const trackCode = order.trackCode.trim();

    // Se tem transportadora definida, usar sua URL de rastreamento
    if (order.shippingTracking?.provider) {
      const trackingUrl = `${order.shippingTracking.provider.trackingUrl}${trackCode}`;
      window.open(trackingUrl, "_blank");
      return;
    }

    // Fallback para Correios se não tem transportadora definida
    window.open(
      `https://rastreamento.correios.com.br/app/index.php?objeto=${trackCode}`,
      "_blank"
    );
  } catch (e) {
    console.error("Erro ao rastrear pedido:", e);
    alert("Ocorreu um erro ao rastrear o pedido. Por favor, tente novamente.");
  }
};

// Funções para gerenciar o código de rastreamento
const startEditTrackCode = () => {
  trackCodeInput.value = '';
  editingTrackCode.value = true;
};

const cancelEditTrackCode = () => {
  editingTrackCode.value = false;
  trackCodeInput.value = '';
};

// Funções para gerenciar avaliações
const loadExistingReview = async () => {
  if (!customerStore.customer?.uid || !props.order.productId) return;

  try {
    const response = await $fetch(`/api/reviews/user/${customerStore.customer.uid}`);
    if (response.success) {
      const review = response.data.reviews.find((r: IProductReview) =>
        r.productId === props.order.productId
      );
      existingReview.value = review || null;
    }
  } catch (error) {
    console.error('Erro ao carregar avaliação existente:', error);
  }
};

const openReviewModal = () => {
  showReviewModal.value = true;
};

const closeReviewModal = () => {
  showReviewModal.value = false;
};

const editReview = () => {
  showReviewModal.value = true;
};

const handleReviewSubmit = (review: IProductReview) => {
  existingReview.value = review;
  closeReviewModal();

  // Atualizar o store do cliente se necessário
  if (customerStore.customer) {
    if (existingReview.value) {
      // Atualizar avaliação existente
      customerStore.customer.updateReview(review.id, review);
    } else {
      // Adicionar nova avaliação
      customerStore.customer.addReview(review);
    }
  }
};

const formatDate = (date: string | Date) => {
  const d = new Date(date);
  return d.toLocaleDateString('pt-BR', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
};

// Carregar avaliação existente quando o componente for montado
onMounted(() => {
  // Verificar modo debug (apenas no cliente)
  if (typeof window !== 'undefined' && localStorage.getItem('debug-reviews') === 'true') {
    isDebugMode.value = true;
  }

  if (canShowReviewSection.value) {
    loadExistingReview();
  }
});

const saveTrackCode = async () => {
  if (!trackCodeInput.value.trim()) {
    alert('Por favor, digite um código de rastreamento válido.');
    return;
  }

  saving.value = true;

  try {
    // Verificar se o cliente existe
    const customerId = props.order.customerId;
    if (!customerId) {
      throw new Error('ID do cliente não encontrado no pedido');
    }

    // Buscar o cliente
    const customer = customerStore.getCustomerById(customerId);
    if (!customer) {
      throw new Error('Cliente não encontrado');
    }

    // Encontrar o pedido na lista de pedidos do cliente
    const orderIndex = customer.orders.findIndex(o => o.id === props.order.id);
    if (orderIndex === -1) {
      throw new Error('Pedido não encontrado na lista de pedidos do cliente');
    }

    // Atualizar o código de rastreamento no pedido
    const updatedOrder = { ...customer.orders[orderIndex], trackCode: trackCodeInput.value.trim() };

    // Atualizar o pedido no cliente
    const updatedOrders = [...customer.orders];
    updatedOrders[orderIndex] = updatedOrder;

    // Atualizar o cliente no Firestore
    await customerStore.updateCustomer({
      ...customer,
      orders: updatedOrders
    });

    // Atualizar o pedido localmente
    emit('update:order', { ...props.order, trackCode: trackCodeInput.value.trim() });

    // Sair do modo de edição
    editingTrackCode.value = false;

    // Atualizar o pedido local
    Object.assign(props.order, { trackCode: trackCodeInput.value.trim() });

    console.log(`✅ Código de rastreamento adicionado com sucesso: ${trackCodeInput.value.trim()}`);
  } catch (error) {
    console.error('Erro ao salvar código de rastreamento:', error);
    alert('Ocorreu um erro ao salvar o código de rastreamento. Por favor, tente novamente.');
  } finally {
    saving.value = false;
  }
};
</script>
