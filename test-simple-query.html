<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste Query Simplificada - Reviews API v2</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #fafafa;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        button.success {
            background: #28a745;
        }
        button.error {
            background: #dc3545;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 5px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        input {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            width: 300px;
        }
        .status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        .status.success { background: #d4edda; color: #155724; }
        .status.error { background: #f8d7da; color: #721c24; }
        .status.pending { background: #fff3cd; color: #856404; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Teste Query Simplificada - Reviews API v2</h1>
        <p><strong>Objetivo:</strong> Verificar se a query ultra-simplificada funciona sem índices compostos</p>
        
        <div class="test-section">
            <h2>📋 Status dos Testes</h2>
            <div id="test-status">
                <div>1. Buscar Produtos: <span id="status-products" class="status pending">Pendente</span></div>
                <div>2. Query Simples: <span id="status-simple" class="status pending">Pendente</span></div>
                <div>3. Criar Review: <span id="status-create" class="status pending">Pendente</span></div>
                <div>4. Testar Delete (403): <span id="status-delete" class="status pending">Pendente</span></div>
                <div>5. Buscar Reviews: <span id="status-fetch" class="status pending">Pendente</span></div>
            </div>
        </div>

        <div class="test-section">
            <h2>1. Buscar Produtos Disponíveis</h2>
            <button onclick="testGetProducts()">Buscar Produtos</button>
            <div id="products-result" class="result info">Clique no botão para buscar produtos...</div>
        </div>

        <div class="test-section">
            <h2>2. Teste Query Ultra-Simplificada</h2>
            <p><strong>Query:</strong> <code>where("productId", "==", productId)</code> - SEM orderBy, SEM outros filtros</p>
            <input type="text" id="productId" placeholder="ID do Produto (será preenchido automaticamente)">
            <br><br>
            <button onclick="testSimpleQuery()">Testar Query Simples</button>
            <div id="simple-result" class="result info">Aguardando teste...</div>
        </div>

        <div class="test-section">
            <h2>3. Criar Review de Teste</h2>
            <p>Criar uma review para testar se a API de criação funciona</p>
            <button onclick="testCreateReview()">Criar Review</button>
            <div id="create-result" class="result info">Aguardando criação...</div>
        </div>

        <div class="test-section">
            <h2>3.1. Testar Tentativa de Delete (Deve Falhar)</h2>
            <p><strong>Nova Política:</strong> Usuários não podem mais deletar avaliações, apenas editar</p>
            <button onclick="testDeleteReview()">Tentar Deletar (Deve Retornar 403)</button>
            <div id="delete-result" class="result info">Aguardando teste...</div>
        </div>

        <div class="test-section">
            <h2>4. Buscar Reviews com Diferentes Parâmetros</h2>
            <p>Testar diferentes combinações de parâmetros que antes causavam problemas</p>
            <button onclick="testFetchWithParams()">Testar com Parâmetros</button>
            <div id="fetch-result" class="result info">Aguardando teste...</div>
        </div>

        <div class="test-section">
            <h2>5. Executar Todos os Testes</h2>
            <button onclick="runAllTests()" style="background: #17a2b8; font-size: 16px; padding: 12px 24px;">🚀 Executar Sequência Completa</button>
            <div id="all-results" class="result info">Clique para executar todos os testes em sequência...</div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:4001';
        let currentProductId = '';

        function updateStatus(testId, status) {
            const element = document.getElementById(`status-${testId}`);
            element.className = `status ${status}`;
            element.textContent = status === 'success' ? 'Sucesso' : status === 'error' ? 'Erro' : 'Pendente';
        }

        async function testGetProducts() {
            const resultDiv = document.getElementById('products-result');
            resultDiv.textContent = 'Buscando produtos...';
            
            try {
                const response = await fetch(`${API_BASE}/api/products`);
                const data = await response.json();
                
                if (response.ok && data.products && data.products.length > 0) {
                    currentProductId = data.products[0].id;
                    document.getElementById('productId').value = currentProductId;
                    
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ Encontrados ${data.products.length} produtos\n\nPrimeiro produto selecionado:\nID: ${currentProductId}\nTítulo: ${data.products[0].title || data.products[0].alias}\n\nPrimeiros 3 produtos:\n` + 
                        JSON.stringify(data.products.slice(0, 3).map(p => ({
                            id: p.id,
                            title: p.title || p.alias
                        })), null, 2);
                    
                    updateStatus('products', 'success');
                } else {
                    throw new Error('Nenhum produto encontrado');
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ Erro: ${error.message}`;
                updateStatus('products', 'error');
            }
        }

        async function testSimpleQuery() {
            const resultDiv = document.getElementById('simple-result');
            const productId = document.getElementById('productId').value || currentProductId;
            
            if (!productId) {
                resultDiv.className = 'result error';
                resultDiv.textContent = '❌ Por favor, busque produtos primeiro para obter um ID válido';
                return;
            }

            resultDiv.textContent = 'Testando query ultra-simplificada...';
            
            try {
                const url = `${API_BASE}/api/v2/reviews/product/${productId}`;
                console.log('🧪 Testando URL:', url);
                
                const response = await fetch(url);
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ Query ultra-simplificada funcionou!\n\nURL: ${url}\nStatus: ${response.status}\n\nResposta:\n${JSON.stringify(data, null, 2)}`;
                    updateStatus('simple', 'success');
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ Erro na query: ${response.status}\n\n${JSON.stringify(data, null, 2)}`;
                    updateStatus('simple', 'error');
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ Erro de rede: ${error.message}`;
                updateStatus('simple', 'error');
            }
        }

        let lastCreatedReviewId = null;

        async function testCreateReview() {
            const resultDiv = document.getElementById('create-result');
            const productId = document.getElementById('productId').value || currentProductId;

            if (!productId) {
                resultDiv.className = 'result error';
                resultDiv.textContent = '❌ Por favor, busque produtos primeiro';
                return;
            }

            resultDiv.textContent = 'Criando review de teste...';

            const reviewData = {
                userId: 'test-user-' + Date.now(),
                productId: productId,
                orderId: 'test-order-' + Date.now(),
                rating: 5,
                comment: 'Review de teste para verificar se a API v2 funciona sem índices compostos!',
                images: [],
                videos: []
            };

            try {
                const response = await fetch(`${API_BASE}/api/v2/reviews`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(reviewData)
                });

                const data = await response.json();

                if (response.ok) {
                    lastCreatedReviewId = data.data?.review?.id;
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ Review criado com sucesso!\n\nID: ${lastCreatedReviewId}\n\n${JSON.stringify(data, null, 2)}`;
                    updateStatus('create', 'success');
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ Erro ao criar: ${response.status}\n\n${JSON.stringify(data, null, 2)}`;
                    updateStatus('create', 'error');
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ Erro de rede: ${error.message}`;
                updateStatus('create', 'error');
            }
        }

        async function testDeleteReview() {
            const resultDiv = document.getElementById('delete-result');

            if (!lastCreatedReviewId) {
                resultDiv.className = 'result error';
                resultDiv.textContent = '❌ Por favor, crie uma review primeiro para testar a deleção';
                return;
            }

            resultDiv.textContent = 'Tentando deletar review (deve falhar com 403)...';

            try {
                const response = await fetch(`${API_BASE}/api/v2/reviews/${lastCreatedReviewId}`, {
                    method: 'DELETE',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ userId: 'test-user-123' })
                });

                const data = await response.json();

                if (response.status === 403) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ Política funcionando corretamente!\n\nStatus: ${response.status} (Forbidden)\nMensagem: ${data.message || data.statusMessage}\n\n🔒 Usuários não podem mais deletar avaliações - apenas editar!`;
                    updateStatus('delete', 'success');
                } else if (response.ok) {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ ERRO: Delete deveria ter falhado!\n\nStatus: ${response.status}\nResposta: ${JSON.stringify(data, null, 2)}`;
                    updateStatus('delete', 'error');
                } else {
                    resultDiv.className = 'result info';
                    resultDiv.textContent = `ℹ️ Erro inesperado: ${response.status}\n\n${JSON.stringify(data, null, 2)}`;
                    updateStatus('delete', 'error');
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ Erro de rede: ${error.message}`;
            }
        }

        async function testFetchWithParams() {
            const resultDiv = document.getElementById('fetch-result');
            const productId = document.getElementById('productId').value || currentProductId;
            
            if (!productId) {
                resultDiv.className = 'result error';
                resultDiv.textContent = '❌ Por favor, busque produtos primeiro';
                return;
            }

            resultDiv.textContent = 'Testando busca com parâmetros...';
            
            const testUrls = [
                `${API_BASE}/api/v2/reviews/product/${productId}`,
                `${API_BASE}/api/v2/reviews/product/${productId}?limit=5`,
                `${API_BASE}/api/v2/reviews/product/${productId}?sortBy=newest`,
                `${API_BASE}/api/v2/reviews/product/${productId}?sortBy=rating_high&limit=3`,
                `${API_BASE}/api/v2/reviews/product/${productId}?minRating=4&hasMedia=false`
            ];

            let results = [];
            
            for (const url of testUrls) {
                try {
                    const response = await fetch(url);
                    const data = await response.json();
                    
                    results.push({
                        url: url.replace(API_BASE, ''),
                        status: response.status,
                        success: response.ok,
                        reviewCount: data.data?.reviews?.length || 0,
                        error: response.ok ? null : data.message
                    });
                } catch (error) {
                    results.push({
                        url: url.replace(API_BASE, ''),
                        status: 'NETWORK_ERROR',
                        success: false,
                        error: error.message
                    });
                }
            }

            const allSuccess = results.every(r => r.success);
            resultDiv.className = `result ${allSuccess ? 'success' : 'error'}`;
            resultDiv.textContent = `${allSuccess ? '✅' : '❌'} Teste com parâmetros ${allSuccess ? 'bem-sucedido' : 'falhou'}!\n\n` + 
                results.map(r => `${r.success ? '✅' : '❌'} ${r.url} - Status: ${r.status}${r.success ? ` (${r.reviewCount} reviews)` : ` - ${r.error}`}`).join('\n');
            
            updateStatus('fetch', allSuccess ? 'success' : 'error');
        }

        async function runAllTests() {
            const resultDiv = document.getElementById('all-results');
            resultDiv.className = 'result info';
            resultDiv.textContent = '🚀 Executando sequência completa de testes...\n\n';
            
            // Reset status
            ['products', 'simple', 'create', 'delete', 'fetch'].forEach(id => updateStatus(id, 'pending'));

            try {
                resultDiv.textContent += '1. Buscando produtos...\n';
                await testGetProducts();
                await new Promise(resolve => setTimeout(resolve, 1000));

                resultDiv.textContent += '2. Testando query simples...\n';
                await testSimpleQuery();
                await new Promise(resolve => setTimeout(resolve, 1000));

                resultDiv.textContent += '3. Criando review...\n';
                await testCreateReview();
                await new Promise(resolve => setTimeout(resolve, 1000));

                resultDiv.textContent += '4. Testando política de delete (deve falhar)...\n';
                await testDeleteReview();
                await new Promise(resolve => setTimeout(resolve, 1000));

                resultDiv.textContent += '5. Testando busca com parâmetros...\n';
                await testFetchWithParams();

                resultDiv.textContent += '\n🎉 Sequência completa finalizada! Verifique os resultados acima.';
                resultDiv.textContent += '\n\n📋 Nova Política: Usuários podem apenas EDITAR avaliações, não DELETAR.';
                resultDiv.className = 'result success';
            } catch (error) {
                resultDiv.textContent += `\n❌ Erro durante execução: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }

        // Auto-carregar produtos ao abrir a página
        window.onload = function() {
            testGetProducts();
        };
    </script>
</body>
</html>
