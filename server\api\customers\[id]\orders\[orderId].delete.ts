import { doc, getDoc, updateDoc, Timestamp } from "firebase/firestore";
import { db } from "~/server/utils/firebase";

const USERS_COLLECTION = "users";

/**
 * Rota para remover um pedido do cliente
 * DELETE /api/customers/:id/orders/:orderId
 */
export default defineEventHandler(async (event) => {
  try {
    // Obter o ID do cliente e do pedido da URL
    const id = getRouterParam(event, "id");
    const orderId = getRouterParam(event, "orderId");
    
    if (!id || !orderId) {
      throw createError({
        statusCode: 400,
        statusMessage: "ID do cliente ou do pedido não fornecido",
      });
    }
    
    console.log(`🔍 [API] Removendo pedido ${orderId} para cliente ${id}`);

    // Verificar se o cliente existe
    const customerRef = doc(db, USERS_COLLECTION, id);
    const customerDoc = await getDoc(customerRef);

    if (!customerDoc.exists()) {
      console.log(`🔍 [API] Cliente com ID ${id} não encontrado`);
      throw createError({
        statusCode: 404,
        statusMessage: "Cliente não encontrado",
      });
    }

    const customerData = customerDoc.data();
    let orderFound = false;
    let orderType = '';

    // Log para debug - mostrar estrutura do cliente
    console.log(`🔍 [API] Cliente encontrado. Estrutura:`, {
      hasOrders: !!customerData.orders,
      ordersCount: customerData.orders?.length || 0,
      hasPendingOrders: !!customerData.pendingOrders,
      pendingOrdersCount: customerData.pendingOrders?.length || 0,
      hasRejectedOrders: !!customerData.rejectedOrders,
      rejectedOrdersCount: customerData.rejectedOrders?.length || 0
    });

    // Log dos IDs dos pedidos para debug
    if (customerData.orders) {
      console.log(`🔍 [API] IDs dos pedidos aprovados:`, customerData.orders.map((o: any) => o.id));
    }
    if (customerData.pendingOrders) {
      console.log(`🔍 [API] IDs dos pedidos pendentes:`, customerData.pendingOrders.map((o: any) => o.id));
    }
    if (customerData.rejectedOrders) {
      console.log(`🔍 [API] IDs dos pedidos rejeitados:`, customerData.rejectedOrders.map((o: any) => o.id));
    }
    
    // Verificar e remover de pedidos aprovados
    if (customerData.orders && Array.isArray(customerData.orders)) {
      console.log(`🔍 [API] Procurando pedido ${orderId} em ${customerData.orders.length} pedidos aprovados`);

      const orderIndex = customerData.orders.findIndex(
        (order: any) => {
          console.log(`🔍 [API] Comparando: ${order.id} === ${orderId} = ${order.id === orderId}`);
          return order.id === orderId;
        }
      );

      if (orderIndex !== -1) {
        console.log(`✅ [API] Pedido encontrado em orders[${orderIndex}]`);
        const updatedOrders = [...customerData.orders];
        updatedOrders.splice(orderIndex, 1);

        await updateDoc(customerRef, {
          orders: updatedOrders,
          updatedAt: Timestamp.now()
        });

        orderFound = true;
        orderType = 'approved';
      } else {
        console.log(`❌ [API] Pedido ${orderId} não encontrado em orders`);
      }
    } else {
      console.log(`❌ [API] Cliente não possui array de orders ou está vazio`);
    }
    
    // Verificar e remover de pedidos pendentes
    if (!orderFound && customerData.pendingOrders && Array.isArray(customerData.pendingOrders)) {
      console.log(`🔍 [API] Procurando pedido ${orderId} em ${customerData.pendingOrders.length} pedidos pendentes`);

      const orderIndex = customerData.pendingOrders.findIndex(
        (order: any) => {
          console.log(`🔍 [API] Comparando: ${order.id} === ${orderId} = ${order.id === orderId}`);
          return order.id === orderId;
        }
      );

      if (orderIndex !== -1) {
        console.log(`✅ [API] Pedido encontrado em pendingOrders[${orderIndex}]`);
        const updatedPendingOrders = [...customerData.pendingOrders];
        updatedPendingOrders.splice(orderIndex, 1);

        await updateDoc(customerRef, {
          pendingOrders: updatedPendingOrders,
          updatedAt: Timestamp.now()
        });

        orderFound = true;
        orderType = 'pending';
      } else {
        console.log(`❌ [API] Pedido ${orderId} não encontrado em pendingOrders`);
      }
    } else if (!orderFound) {
      console.log(`❌ [API] Cliente não possui array de pendingOrders ou está vazio`);
    }
    
    // Verificar e remover de pedidos rejeitados
    if (!orderFound && customerData.rejectedOrders && Array.isArray(customerData.rejectedOrders)) {
      console.log(`🔍 [API] Procurando pedido ${orderId} em ${customerData.rejectedOrders.length} pedidos rejeitados`);

      const orderIndex = customerData.rejectedOrders.findIndex(
        (order: any) => {
          console.log(`🔍 [API] Comparando: ${order.id} === ${orderId} = ${order.id === orderId}`);
          return order.id === orderId;
        }
      );

      if (orderIndex !== -1) {
        console.log(`✅ [API] Pedido encontrado em rejectedOrders[${orderIndex}]`);
        const updatedRejectedOrders = [...customerData.rejectedOrders];
        updatedRejectedOrders.splice(orderIndex, 1);

        await updateDoc(customerRef, {
          rejectedOrders: updatedRejectedOrders,
          updatedAt: Timestamp.now()
        });

        orderFound = true;
        orderType = 'rejected';
      } else {
        console.log(`❌ [API] Pedido ${orderId} não encontrado em rejectedOrders`);
      }
    } else if (!orderFound) {
      console.log(`❌ [API] Cliente não possui array de rejectedOrders ou está vazio`);
    }
    
    if (!orderFound) {
      throw createError({
        statusCode: 404,
        statusMessage: "Pedido não encontrado",
      });
    }
    
    console.log(`✅ [API] Pedido ${orderId} (${orderType}) removido com sucesso`);

    // Fazer uma requisição para invalidar o cache de clientes
    try {
      await $fetch('/api/customers?refresh=true');
      console.log('🔄 [API] Cache de clientes invalidado');
    } catch (error) {
      console.warn('⚠️ [API] Não foi possível invalidar o cache:', error);
    }

    return {
      success: true,
      message: `Pedido ${orderType} removido com sucesso`,
      orderType
    };
  } catch (error: any) {
    console.error("❌ [API] Erro ao remover pedido:", error);
    
    throw createError({
      statusCode: error.statusCode || 500,
      statusMessage: error.statusMessage || "Erro ao remover pedido",
    });
  }
});
